using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class PostQLAMapping
    {
        public long PostQLAMappingID { get; set; }
        public Nullable<long> PostQLAStateID { get; set; }
        public Nullable<long> QLARuleID { get; set; }
        public Nullable<long> ContractSectionsID { get; set; }
        public Nullable<long> LegalityPhaseID { get; set; }
        public string UpdatedBy { get; set; }
        public System.DateTime UpdatedDate { get; set; }
        public Nullable<int> RunContextID { get; set; }
    
        public virtual QLARule QLARule { get; set; }
        public virtual ContractSection ContractSection { get; set; }
        public virtual LegalityPhas LegalityPhas { get; set; }
        public virtual PostQLAState PostQLAState { get; set; }
    }
}
