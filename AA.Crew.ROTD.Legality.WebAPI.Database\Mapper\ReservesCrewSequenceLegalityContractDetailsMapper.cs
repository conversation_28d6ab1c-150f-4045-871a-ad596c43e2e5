using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class ReservesCrewSequenceLegalityContractDetailsMapper : MapperBase<List<ReservesCrewSequenceLegalityContractDetailsDTO>, List<ContractDetailsTable>>
    {
        public override List<ContractDetailsTable> Map(List<ReservesCrewSequenceLegalityContractDetailsDTO> reservesCrewSequenceLegalityContractDetailsDtoList)
        {
            try
            {
                return reservesCrewSequenceLegalityContractDetailsDtoList.Select(reservesCrewSequenceLegalityContractDetailsDto => new ContractDetailsTable
                {
                    ReservesCrewSequenceLegalityContractDetailsID = reservesCrewSequenceLegalityContractDetailsDto.ReservesCrewSequenceLegalityContractDetailsID,
                    ContractSectionsID = reservesCrewSequenceLegalityContractDetailsDto.ContractSectionsID,
                    LegalityPhaseID = reservesCrewSequenceLegalityContractDetailsDto.LegalityPhaseID,
                    ReservesCrewSequenceLegalityID = reservesCrewSequenceLegalityContractDetailsDto.ReservesCrewSequenceLegalityID,
                    LanguageID = reservesCrewSequenceLegalityContractDetailsDto.LanguageID,
                    FosRAP = reservesCrewSequenceLegalityContractDetailsDto.FosRAP,
                    IsCurrentRAP = reservesCrewSequenceLegalityContractDetailsDto.IsCurrentRAP,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<ReservesCrewSequenceLegalityContractDetailsDTO> Map(List<ContractDetailsTable> element)
        {
            throw new NotImplementedException();
        }
    }
}
