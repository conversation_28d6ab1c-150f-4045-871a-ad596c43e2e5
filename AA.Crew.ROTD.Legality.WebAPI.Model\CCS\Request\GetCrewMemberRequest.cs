﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Request
{
    public class GetCrewMemberRequest : RequestBase
    {
        public CrewMemberKey crewMemberKeyDTO { get; set; }

        public string[] contractMonths { get; set; }

        [JsonProperty(ItemConverterType = typeof(StringEnumConverter))]
        public List<CrewMemberRequestOptions> gets { get; set; }
    }
}
