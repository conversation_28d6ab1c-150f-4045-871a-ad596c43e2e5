using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityROTDPhaseDetailOrdinal
    {
        /* Oridinal variables */

        internal Int32 ReservesCrewSequenceLegalityID;
        internal Int32 ReservesCrewMemberID;
        internal Int32 RunID;
        internal Int32 SequencePositionDetailsID;
        internal Int32 StandbyID;
        internal Int32 IsOver35By7;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.ReservesCrewSequenceLegalityID = sqlDataReader.GetOrdinal("ReservesCrewSequenceLegalityID");
            this.ReservesCrewMemberID = sqlDataReader.GetOrdinal("ReservesCrewMemberID");
            this.RunID = sqlDataReader.GetOrdinal("RunID");
            this.SequencePositionDetailsID = sqlDataReader.GetOrdinal("SequencePositionDetailsID");
            this.StandbyID = sqlDataReader.GetOrdinal("StandbyID");
            this.IsOver35By7 = sqlDataReader.GetOrdinal("IsOver35By7");


            this.Initialized = true;
        }
    }
}
