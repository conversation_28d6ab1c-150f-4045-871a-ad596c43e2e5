using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class ReservesCrewSequenceLegality
    {
        public ReservesCrewSequenceLegality()
        {
            this.ReservesCrewSequenceLegalityContractDetails = new HashSet<ReservesCrewSequenceLegalityContractDetail>();
            this.LegalityQLADetails = new HashSet<LegalityQLADetail>();
        }
    
        public long ReservesCrewSequenceLegalityID { get; set; }
        public long ReservesCrewMemberID { get; set; }
        public long RunID { get; set; }
        public Nullable<long> SequencePositionDetailsID { get; set; }
        public Nullable<int> StandByID { get; set; }
        public Nullable<bool> IsOver35By7 { get; set; }
    
        public virtual ICollection<ReservesCrewSequenceLegalityContractDetail> ReservesCrewSequenceLegalityContractDetails { get; set; }
        public virtual SequencePositionDetail SequencePositionDetail { get; set; }
        public virtual Run Run { get; set; }
        public virtual Standby Standby { get; set; }
        public virtual ICollection<LegalityQLADetail> LegalityQLADetails { get; set; }
    }
}
