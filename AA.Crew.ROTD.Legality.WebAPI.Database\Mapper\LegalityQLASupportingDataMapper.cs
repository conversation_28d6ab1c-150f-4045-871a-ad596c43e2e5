using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityQLASupportingDataMapper : MapperBase<List<LegalityQLASupportingDataDTO>, List<LegalityQLASupportingData>>
    {
        public override List<LegalityQLASupportingData> Map(List<LegalityQLASupportingDataDTO> legalityQLASupportingDataDtoList)
        {
            try
            {
                return legalityQLASupportingDataDtoList.Select(legalityQLASupportingDataDto => new LegalityQLASupportingData
                {
                    RunId = legalityQLASupportingDataDto.RunId,
                    EmployeeId = legalityQLASupportingDataDto.EmployeeId,
                    AffectedType = legalityQLASupportingDataDto.AffectedType,
                    ContractMonth = legalityQLASupportingDataDto.ContractMonth,
                    ActivityId = legalityQLASupportingDataDto.ActivityId,
                    ActivityCode = legalityQLASupportingDataDto.ActivityCode,
                    ActivityType = legalityQLASupportingDataDto.ActivityType,
                    ActivityOriginationDate = legalityQLASupportingDataDto.ActivityOriginationDate,
                    PositionCode = legalityQLASupportingDataDto.PositionCode,
                    StartDateTime = legalityQLASupportingDataDto.StartDateTime.Value,
                    EndDateTime = legalityQLASupportingDataDto.EndDateTime,
                    PickupContractMonth = legalityQLASupportingDataDto.PickupContractMonth,
                    PickupActivityId = legalityQLASupportingDataDto.PickupActivityId,
                    PickupActivityCode = legalityQLASupportingDataDto.PickupActivityCode,
                    PickupActivityType = legalityQLASupportingDataDto.PickupActivityType,
                    PickupActivityOriginationDate = legalityQLASupportingDataDto.PickupActivityOriginationDate,
                    PickupPositionCode = legalityQLASupportingDataDto.PickupPositionCode,
                    PickupStartDateTime = legalityQLASupportingDataDto.PickupStartDateTime.Value,
                    PickupEndDateTime = legalityQLASupportingDataDto.PickupEndDateTime,
                    ActivityReportDateTime = legalityQLASupportingDataDto.ActivityReportDateTime,
                    LeaglityPhaseID = legalityQLASupportingDataDto.LegalityPhaseId.Value,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityQLASupportingDataDTO> Map(List<LegalityQLASupportingData> element)
        {
            throw new NotImplementedException();
        }
    }
}
