﻿using AA.Crew.ROMS.Client;
using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Database;
using AA.Crew.ROTD.Legality.WebAPI.Database.Factories;
using AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.Provider;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Exceptions;
using AA.Crew.ROTD.Legality.WebAPI.Model.Request;
using AA.Crew.ROTD.Legality.WebAPI.Model.Response;
using AA.Crew.ROTD.Legality.WebAPI.Service;
using AA.Crew.ROTD.Legality.WebAPI.Service.Interfaces;
using AA.Crew.WebClient.Implementation;
using AA.Crew.WebClient.Messages;
using AA.Crew.WebClient.Messages.Interface;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business
{
    public class DataLoader //: IDataLoader
    {
        #region Variable Declarations
        int resultDecimals;
        private readonly ILogger<DataLoader> _logger;
        readonly IDataProviderFactory _dataProviderFactory;
        private IAppSettingsDataProvider applicationSettingsProvider;
        private ICrewDataServiceProvider _crewDataServiceProvider;
        private ILegalityServiceProvider _legalityServiceProvider;
        private IInterpretiveDataProvider _interpretiveDataProvider;
        public static List<Task> TaskList = new List<Task>();
        private readonly object lockObject = new object();
        List<AppSettings> _appSettings;

        private List<Activity> lstFaActivity = null;
        public List<FlightAttendant> lstFlightAttendant = null;
        private bool IsSickProcess = false;
        private List<Model.BusinessObject.Sequence> lstSequence = null;
        private List<StandBy> lstStandBy = null;
        private List<BidCrewWaiver> lstBidCrewWaiver = null;
        private List<AggressiveBidCrewWaiver> lstAggressiveBidCrewWaiver = null;

        

        #endregion



        public DataLoader(ILogger<DataLoader> logger, List<AppSettings> appSettings, IDataProviderFactory dataProviderFactory, ICrewDataServiceProvider crewDataServiceProvider, ILegalityServiceProvider legalityServiceProvider)
        {
            _logger = logger;
            _dataProviderFactory = dataProviderFactory;
            _appSettings = appSettings;
            applicationSettingsProvider = _dataProviderFactory.GetAppSettingsDataProvider();
            _interpretiveDataProvider = _dataProviderFactory.GetROTDInterpretiveDataProvider();
            _crewDataServiceProvider = crewDataServiceProvider;
        }

        public async Task<AppSettings> GetAppSettings(string name)
        {
            return applicationSettingsProvider.GetApplicationSetting("QLARetryCount").Result;
        }

        //public void LoadData(int runID, int subPhase, int runContextId)
        //{
        //    string baseCD;
        //    DateTime processingDate;
        //    string logTitle = "DataLoader.LoadData() {0}";
        //    ROTDPhase phase = new ROTDPhase();
        //    try
        //    {
        //        var Result = _interpretiveDataProvider.GetBaseProcessingDate(runID).Result;
        //        if (Result == null)
        //        {
        //            throw new NoValidDataException("No Valid Data for the Run ID : " + runID);
        //        }

        //        baseCD = Result.BaseCD;
        //        processingDate = Result.ProcessingDate;
        //        //this.runId = runId;

        //        lstFaActivity = _interpretiveDataProvider.GetLegalityCrewMemberActivity(runID).Result;
        //        LstFlightAttendant = _interpretiveDataProvider.getReservesCrewMemberDetails(lstFaActivity, runID).Result;

        //        var isSick = GetAppSettings("SickProcess").Result.SettingValues;
        //        if (!string.IsNullOrEmpty(isSick))
        //            IsSickProcess = Convert.ToBoolean(isSick);
        //        LstFlightAttendant = LstFlightAttendant.Where(a => a.IsSick == false || IsSickProcess).ToList();

        //        if (LstFlightAttendant == null || LstFlightAttendant.Count == 0)
        //        {
        //            throw new NoValidDataException("No Valid Flight Attendant");
        //        }

        //        lstSequence = _interpretiveDataProvider.getSequenceDetails(baseCD).Result;
        //        lstStandBy = _interpretiveDataProvider.getStandByDetails(processingDate).Result;

        //        if ((lstSequence == null || lstSequence.Count == 0) && (lstStandBy == null || lstStandBy.Count == 0))
        //        {
        //            throw new NoValidDataException("No Valid Sequence and Standby");
        //        }

        //        lstBidCrewWaiver = _interpretiveDataProvider.getLegalityProcessBidCrewWaiver().Result;
        //        // Retrive Bids ( Following naming is misleading, actually below SP call returns the Bids.
        //        lstAggressiveBidCrewWaiver = _interpretiveDataProvider.getLegalityProcessAggressiveBidCrewWaiver().Result;

        //        //filter the BidType
        //        lstBidCrewWaiver = (from bidCW in lstBidCrewWaiver
        //                            join bidACW in lstAggressiveBidCrewWaiver on bidCW.BidTypeId equals bidACW.BidTypeId
        //                            where bidCW.CrewMemberId == bidACW.EmployeeID
        //                            select new BidCrewWaiver
        //                            {
        //                                BidCrewWaiverId = bidCW.BidCrewWaiverId,
        //                                BidTypeId = bidCW.BidTypeId,
        //                                CrewMemberId = bidCW.CrewMemberId,
        //                                IsActive = bidCW.IsActive,
        //                                StartDate = bidCW.StartDate,
        //                                WaiverTypeDescription = bidCW.WaiverTypeDescription,
        //                                WaiverTypeID = bidCW.WaiverTypeID,
        //                                BidCategoryID = bidACW.BidCategoryID,
        //                                WaiverSupportingData = bidCW.WaiverSupportingData
        //                            }).ToList();


        //        var contractMonth = _crewDataServiceProvider.GetContractMonthForGivenDate(processingDate).Result;





        //        var conMonthYear = contractMonth.Month;
        //        //come back after data layer migration
        //        //var isBaseCoTerminal = _interpretiveDataProvider.GetCoterminalBases().Any(x => x.BaseCD == baseCD);
        //        //var IsBaseCoTerminal = isBaseCoTerminal;

        //        //var lstRAPShift = rotdLoader.getRAPShift(processingDate, baseCD, conMonthYear);
        //        //var currentContractMonthEndDate = contract.FirstOrDefault().EndDate;
        //        //nextContractMonth = rotdLoader.getContractMonth(currentContractMonthEndDate.AddDays(1)).FirstOrDefault().Month;

        //        //lstPreviousMonthRAPShift = new List<ROMS.Model.RapShift>();
        //        //currentContractMonthStartDate = contract.FirstOrDefault().StartDate;
        //        //if (currentContractMonthStartDate.Date == processingDate.Date)
        //        //{
        //        //    string previousContractMonth = rotdLoader.getContractMonth(currentContractMonthStartDate.AddDays(-1)).FirstOrDefault().Month;
        //        //    lstPreviousMonthRAPShift = rotdLoader.getRAPShift(processingDate.AddDays(-1), baseCD, previousContractMonth);
        //        //}

        //        //if (runId == 0)
        //        //{
        //        //    runId = rotdLoader.getRunId(baseCD, processingDate);
        //        //}

        //        //Entities.BaseDate objBaseDate = new Entities.BaseDate() { BaseName = baseCD, ProcessingDate = processingDate };
        //        ////string stlbase = baseCD;
        //        ////if (baseCD.ToUpper() == "SLT")
        //        ////{
        //        ////    stlbase = "STL";
        //        ////}

        //        //var CurrentSysTime = _container.Resolve<ITimeConversion>().ConvertUTCToStation(baseCD, DateTime.UtcNow);

        //        //objBaseDate.CurrentSysTime = CurrentSysTime.StationLocalTIme;

        //        //mappedDetails = rotdLoader.GetLegalityPostQLAMapping(runId);

                



        //    }
        //    catch (NoValidDataException ex)
        //    {
        //        throw ex;
        //    }
        //    catch (CCSException ex)
        //    {
        //        _logger.LogError(logTitle, "Error occured while calling CCS from LoadData for ROTD. " + ex.GetAllException());
        //        throw ex;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(logTitle, "Error occured while Load Data from DB for ROTD. " + ex.GetAllException());
        //        throw new Exception("Error occured while Load Data from DB for ROTD. " + ex.GetAllException());
        //    }
        //}
    }
}
