<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>1ea67c13-4e26-494f-b901-79995c301453</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="appsettings.Development.json" />
    <Content Remove="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AA.Crew.ROMS.LogHelper" Version="1.24.11112.102" />
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Azure.Security.KeyVault.Certificates" Version="4.5.1" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0-preview.6.23329.7" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.18.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="AA.Crew.ROMS.Model" Version="1.23.11213.101" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Documentation\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Business\AA.Crew.ROTD.Legality.WebAPI.Business.csproj" />
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Database\AA.Crew.ROTD.Legality.WebAPI.Database.csproj" />
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Model\AA.Crew.ROTD.Legality.WebAPI.Model.csproj" />
  </ItemGroup>

</Project>
