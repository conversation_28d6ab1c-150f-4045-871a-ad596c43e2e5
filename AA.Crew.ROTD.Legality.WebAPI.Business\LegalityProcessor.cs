﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business
{
    public class LegalityProcessor : ILegalityProcessor
    {
        private List<string> ruleName;
        private bool _checkIsValidStop;
        private ILogger<LegalityProcessor> _loggingProvider;
        
        public LegalityProcessor(ILogger<LegalityProcessor> loggingProvider)
        {
            _loggingProvider = loggingProvider;
        }
        public List<string> RefreshingAssignAward(QLAResponse qlaResponse, List<QLARuleResult> qlaRuleResult)
        {
            _checkIsValidStop = true;
            ruleName = new List<string>();
            try
            {
                if (qlaResponse != null)
                {

                    if (qlaResponse.IsQualified == true && qlaResponse.IsLegal == true && qlaResponse.IsContractual == true && qlaRuleResult.Count() == 0)
                    {
                        _checkIsValidStop = false;
                    }

                    if (qlaResponse.IsQualified == true && qlaResponse.IsLegal == true && qlaResponse.IsContractual == false && qlaRuleResult.Count() > 0)
                    {
                        _checkIsValidStop = false;
                        if (qlaRuleResult.Any(x => x.Result != QLAResultConst.NC))
                        {
                            _checkIsValidStop = true;
                        }
                    }

                    if (_checkIsValidStop)
                    {
                        ruleName.AddRange(from qq in qlaRuleResult select qq.Rule + "(" + qq.Result + ")");
                        if (ruleName.Count() == 0)
                        {
                            ruleName.Add("Invalid QLA Response");
                        }
                    }


                    #region Existing

                    //if (qlaRuleResult.Count > 0)
                    //{
                    //    if (qlaResponse.IsQualified == true && qlaResponse.IsLegal == true && qlaResponse.IsContractual == true && qlaRuleResult.Count() > 0)
                    //    {
                    //        ruleName.Add("ROTDInterpretiveRuleSet");
                    //        return ruleName;
                    //    }
                    //    var hhs = qlaRuleResult.Where(x => x.Result != QLAResultConst.IL && x.Result != QLAResultConst.NC && x.Result != QLAResultConst.NQ);
                    //    if (qlaRuleResult.Any(x => x.Result != QLAResultConst.IL && x.Result != QLAResultConst.NC && x.Result != QLAResultConst.NQ))
                    //    {
                    //        ruleName.Add("ROTDInterpretiveRuleSet");
                    //    }
                    //    else
                    //    {
                    //        if ((qlaResponse.IsLegal == false && qlaRuleResult.Any(x => x.Result == QLAResultConst.IL)) || qlaResponse.IsLegal == false)
                    //        {
                    //            ruleName.AddRange((from result in qlaRuleResult where result.Result == QLAResultConst.IL select result.Rule).ToList());
                    //            if (ruleName.Count() == 0)
                    //            {
                    //                ruleName.Add("ROTDInterpretiveRuleSet");
                    //            }
                    //        }

                    //        if ((qlaResponse.IsQualified == false && qlaRuleResult.Any(x => x.Result == QLAResultConst.NQ)) || qlaResponse.IsQualified == false)
                    //        {
                    //            ruleName.AddRange((from result in qlaRuleResult where result.Result == QLAResultConst.NQ select result.Rule).ToList());
                    //            if (ruleName.Count() == 0)
                    //            {
                    //                ruleName.Add("ROTDInterpretiveRuleSet");
                    //            }
                    //        }

                    //        if (qlaResponse.IsQualified == true && qlaResponse.IsLegal == true && qlaRuleResult.Any(x => x.Result == QLAResultConst.IL || x.Result == QLAResultConst.NQ))
                    //        {
                    //            ruleName.AddRange((from result in qlaRuleResult where (result.Result == QLAResultConst.NQ || result.Result == QLAResultConst.IL) select result.Rule).ToList());
                    //            if (ruleName.Count() == 0)
                    //            {
                    //                ruleName.Add("ROTDInterpretiveRuleSet");
                    //            }
                    //        }
                    //    }
                    //}
                    //else
                    //{
                    //    if (qlaResponse.IsContractual == false)
                    //    {
                    //        ruleName.Add("ROTDInterpretiveRuleSet");
                    //    }
                    //}
                    #endregion
                }
                return ruleName;
            }
            catch (Exception ex)
            {
                //_loggingProvider.LogError("AA.Crew.Legalities.Contextual", "ROTAContextualProcessor.RefreshingAssignAward", ex.Message ?? "" + ex.StackTrace ?? "");
                throw;
            }
        }
    }
}
