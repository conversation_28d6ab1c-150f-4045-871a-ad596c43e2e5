﻿using AA.Crew.ROMS.LogHelper.Helper;
using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Database.Factories;
using AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.Mapper;
using AA.Crew.ROTD.Legality.WebAPI.Database.Provider;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.DBEntities;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Transactions;
using entity = AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using LegalityQLARule = AA.Crew.ROTD.Legality.WebAPI.Model.Entities.LegalityQLARule;
using LegalityQLASupportingData = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData;
using ReservesCrewSequenceLegality = AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality;
using Sequence = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence;

namespace AA.Crew.ROTD.Legality.WebAPI.Database
{
    public class LegalityInterpretiveDataProvider : ProviderBase<ILegalityInterpretiveDataAdaptor>, ILegalityInterpretiveDataProvider
    {
        private readonly DateTime OperatingDate;
        private string legalityPhase;
        private DataSet ds;
        private DataTable dt;
        private int runID;
        private ILogger<LegalityInterpretiveDataProvider> _log;
        private readonly object lockObject = new object();
        private string baseCD;
        private string _logTitle= "AA.Crew.ROTD.Legality.WebAPI.Database.LegalityInterpretiveDataProvider ";
        private string connectionString;

        public int RunId
        {
            get { return runID; }
            set { runID = value; }
        }

        protected ILogger<LegalityInterpretiveDataProvider> Logs
        {
            get
            {
                return _log;
            }
        }
        
        public string BaseCD
        {
            get { return baseCD; }
            set { baseCD = value; }
        }


        public LegalityInterpretiveDataProvider(IDataAdaptorFactory factory, ILogger<LegalityInterpretiveDataProvider> log) 
           : base()
        {

            if (factory == null)
                throw new ArgumentNullException("factory", "IDataAdaptorFactory parameter is required");
            
            this.Adaptor = factory.GetLegalityInterpretiveDataAdaptor();
            connectionString = factory.ConnectionString();
            _log = log;
        }

        public async Task<List<StandBy>> GetLegalityStandBy(long runID, DateTime bidOperatingDate)
        {
            MapperBase<List<LegalityStandByDTO>, List<StandBy>> legalityStandbyMapper = new LegalityStandByMapper();
            List<LegalityStandByDTO> legalityStandbyDto = new List<LegalityStandByDTO>();
            List<StandBy> legalityStandby = new List<StandBy>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityStandbyDto = await this.Adaptor.GetLegalityStandBy(runID, bidOperatingDate);
                if (legalityStandbyDto != null)
                {
                    legalityStandby = legalityStandbyMapper.Map(legalityStandbyDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalityStandby;
        }
        public async Task<List<entity.LegalityQLARule>> GetQLARuleList(decimal rulesCount, int phaseID, int runId)
        {
            MapperBase<List<QLARuleListDTO>, List<entity.LegalityQLARule>> legalityQLARuleMapper = new QLARuleListMapper();
            List<QLARuleListDTO> legalityQLARuleDto = new List<QLARuleListDTO>();
            List<entity.LegalityQLARule> legalityQLARule = new List<entity.LegalityQLARule>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityQLARuleDto = await this.Adaptor.GetQLARuleList(rulesCount, phaseID, runId);
                if (legalityQLARuleDto != null)
                {
                    legalityQLARule = legalityQLARuleMapper.Map(legalityQLARuleDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalityQLARule;
        }
        public async Task<List<ContractDetailsTable>> GetReservesCrewSequenceLegalityContractDetails(decimal contractCount, int phaseID, int runId)
        {
            MapperBase<List<ReservesCrewSequenceLegalityContractDetailsDTO>, List<ContractDetailsTable>> contractDetailsMapper = new ReservesCrewSequenceLegalityContractDetailsMapper();
            List<ReservesCrewSequenceLegalityContractDetailsDTO> contractDetailsDto = new List<ReservesCrewSequenceLegalityContractDetailsDTO>();
            List<ContractDetailsTable> contractDetails = new List<ContractDetailsTable>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                contractDetailsDto = await this.Adaptor.GetReservesCrewSequenceLegalityContractDetails(contractCount, phaseID, runId);
                if (contractDetailsDto != null)
                {
                    contractDetails = contractDetailsMapper.Map(contractDetailsDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return contractDetails;
        }

        public async Task<List<FlightAttendant>> GetReservesCrewMember(long runID)
        {
            MapperBase<List<LegalityReservesCrewMembersDTO>, List<FlightAttendant>> legalityReservesCrewMembersMapper = new LegalityReservesCrewMembersMapper();
            List<LegalityReservesCrewMembersDTO> legalityReservesCrewMembersDto = new List<LegalityReservesCrewMembersDTO>();
            List<FlightAttendant> legalityReservesCrewMembers = new List<FlightAttendant>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityReservesCrewMembersDto = await this.Adaptor.GetLegalityReservesCrewMembers(runID);
                if (legalityReservesCrewMembersDto != null)
                {
                    legalityReservesCrewMembers = legalityReservesCrewMembersMapper.Map(legalityReservesCrewMembersDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return legalityReservesCrewMembers;
        }

        public async Task<List<FlightAttendant>> GetReservesCrewMemberDetails(List<Activity> lstFaActivity, long runID)
        {
            MapperBase<List<LegalityReservesCrewMembersDTO>, List<FlightAttendant>> legalityReservesCrewMembersMapper = new LegalityReservesCrewMembersMapper();
            List<LegalityReservesCrewMembersDTO> legalityReservesCrewMembersDto = new List<LegalityReservesCrewMembersDTO>();
            List<FlightAttendant> legalityReservesCrewMembers = new List<FlightAttendant>();

            try
            {

                var tempBidStatus = GetLegalityBidStatus(runID).Result;
                var tempCrememberLanguages = getLegalityReservesCrewMemberLanguageDetails(runID).Result;

                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityReservesCrewMembersDto = await this.Adaptor.GetLegalityReservesCrewMembers(runID);
                if (legalityReservesCrewMembersDto != null)
                {
                    legalityReservesCrewMembers = legalityReservesCrewMembersMapper.Map(legalityReservesCrewMembersDto);
                }

                foreach (FlightAttendant data in legalityReservesCrewMembers)
                {
                    if(lstFaActivity != null)
                    {
                        data.FAStatus = tempBidStatus.Where(a => a.ReservesCrewMemberID == data.ReservesCrewMemberID).ToList();
                        data.FARAPActivity = lstFaActivity.Where(a => a.Employeenumber == data.EmployeeNumber).FirstOrDefault();
                        data.fALanguageDetails = tempCrememberLanguages.Where(x => x.FA.EmployeeNumber == data.EmployeeNumber).ToList();
                    }
                    
                }

            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return legalityReservesCrewMembers;
        }
        
        public async Task<List<Activity>> GetLegalityCrewMemberActivity(long runID)
        {
            MapperBase<List<LegalityCrewMemberActivityDTO>, List<Activity>> legalityCrewmemberActivityMapper = new LegalityCrewMemberActivityMapper();
            List<LegalityCrewMemberActivityDTO> legalityCrewmemberActivityDto = new List<LegalityCrewMemberActivityDTO>();
            List<Activity> legalityCrewmemberActivity = new List<Activity>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityCrewmemberActivityDto = await this.Adaptor.GetLegalityCrewMemberActivity(runID);
                if (legalityCrewmemberActivityDto != null)
                {
                    legalityCrewmemberActivity = legalityCrewmemberActivityMapper.Map(legalityCrewmemberActivityDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalityCrewmemberActivity;
        }
        
        public async Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence>> GetSequenceDetails(long runID, string baseCd)
        {
            MapperBase<List<LegalitySequenceDTO>, List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence>> legalitySequenceMapper = new LegalitySequenceMapper();
            List<LegalitySequenceDTO> legalitySequenceDto = new List<LegalitySequenceDTO>();
            List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence> legalitySequence = new List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence>();

            try
            {
                var sequenceLanguageDetails = GetLegalitySequenceLanguageDetails(runID).Result;

                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalitySequenceDto = await this.Adaptor.GetLegalitySequence(runID);
                if (legalitySequenceDto != null)
                {
                    legalitySequence = legalitySequenceMapper.Map(legalitySequenceDto);
                }

                foreach (AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence data in legalitySequence)
                {
                    data.isSpeaker = sequenceLanguageDetails.Where(s => s.SequencePosDetailID == Convert.ToInt32(data.SequencePositionDetailsID)).Where(s => s.SeqPosition == data.SequencePosition).Count() > 0 ? true : false;
                    data.SequenceLanguageDetails = sequenceLanguageDetails.Where(s => s.SequencePosDetailID == Convert.ToInt32(data.SequencePositionDetailsID)).Select(x => x).ToList();
                    data.StartBase = data.StartBase.Equals("base") ? baseCd : data.StartBase;
                }

            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return legalitySequence;
        }
        public async Task<List<BidCrewWaiver>> GetLegalityBidCrewWaiver(long runID)
        {
            MapperBase<List<LegalityBidCrewWaiverDTO>, List<BidCrewWaiver>> bidCrewWaiverMapper = new LegalityBidCrewWaiverMapper();
            List<LegalityBidCrewWaiverDTO> bidCrewWaiverDto = new List<LegalityBidCrewWaiverDTO>();
            List<BidCrewWaiver> bidCrewWaiver = new List<BidCrewWaiver>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                bidCrewWaiverDto = await this.Adaptor.GetLegalityBidCrewWaiver(runID);
                if (bidCrewWaiverDto != null)
                {
                    bidCrewWaiver = bidCrewWaiverMapper.Map(bidCrewWaiverDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return bidCrewWaiver;
        }

        //TODO: Update return type
        public async Task<List<BidCrewWaiver>> GetLegalityProcessBidCrewWaiver(long runID)
        {
            MapperBase<List<LegalityProcessBidCrewWaiverDTO>, List<BidCrewWaiver>> bidCrewWaiverMapper = new LegalityProcessBidCrewWaiverMapper();
            List<LegalityProcessBidCrewWaiverDTO> bidCrewWaiverDto = new List<LegalityProcessBidCrewWaiverDTO>();
            List<BidCrewWaiver> bidCrewWaiver = new List<BidCrewWaiver>();

            try
            {
                var bidCrewWaiverSupportingData = GetLegalityProcessBidCrewWaiverSupportingData(runID).Result;

                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                bidCrewWaiverDto = await this.Adaptor.GetLegalityProcessBidCrewWaiver(runID);
                if (bidCrewWaiverDto != null)
                {
                    bidCrewWaiver = bidCrewWaiverMapper.Map(bidCrewWaiverDto);
                }

                foreach (BidCrewWaiver data in bidCrewWaiver)
                {
                    data.WaiverSupportingData = bidCrewWaiverSupportingData.Where(d => d.BidCrewWaiverId == data.BidCrewWaiverId).ToList();
                }

            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return bidCrewWaiver;
        }
        public async Task<List<BidCrewWaiverSupportingData>> GetLegalityProcessBidCrewWaiverSupportingData(long runID)
        {
            MapperBase<List<LegalityProcessBidCrewWaiverSupportingDataDTO>, List<BidCrewWaiverSupportingData>> legalityProcessBidCrewWaiverSupportingDataMapper = new LegalityProcessBidCrewWaiverSupportingDataMapper();
            List<LegalityProcessBidCrewWaiverSupportingDataDTO> legalityProcessBidCrewWaiverSupportingDataDto = new List<LegalityProcessBidCrewWaiverSupportingDataDTO>();
            List<BidCrewWaiverSupportingData> legalityProcessBidCrewWaiverSupportingData = new List<BidCrewWaiverSupportingData>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityProcessBidCrewWaiverSupportingDataDto = await this.Adaptor.GetLegalityProcessBidCrewWaiverSupportingData(runID);
                if (legalityProcessBidCrewWaiverSupportingDataDto != null)
                {
                    legalityProcessBidCrewWaiverSupportingData = legalityProcessBidCrewWaiverSupportingDataMapper.Map(legalityProcessBidCrewWaiverSupportingDataDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalityProcessBidCrewWaiverSupportingData;
        }
        public async Task<List<AggressiveBidCrewWaiver>> GetLegalityProcessAggressiveBidCrewWaiver(long runID)
        {
            MapperBase<List<LegalityProcessAggressiveBidCrewWaiverDTO>, List<AggressiveBidCrewWaiver>> legalityProcessAggressiveBidCrewWaiverMapper = new LegalityProcessAggressiveBidCrewWaiverMapper();
            List<LegalityProcessAggressiveBidCrewWaiverDTO> legalityProcessAggressiveBidCrewWaiverDto = new List<LegalityProcessAggressiveBidCrewWaiverDTO>();
            List<AggressiveBidCrewWaiver> legalityProcessAggressiveBidCrewWaiver = new List<AggressiveBidCrewWaiver>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityProcessAggressiveBidCrewWaiverDto = await this.Adaptor.GetLegalityProcessAggressiveBidCrewWaiver(runID);
                if (legalityProcessAggressiveBidCrewWaiverDto != null)
                {
                    legalityProcessAggressiveBidCrewWaiver = legalityProcessAggressiveBidCrewWaiverMapper.Map(legalityProcessAggressiveBidCrewWaiverDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalityProcessAggressiveBidCrewWaiver;
        }
        public async Task<List<PostQLAMappingDetails>> GetLegalityPostQLAMapping(long runID)
        {
            MapperBase<List<LegalityPostQLAMappingDTO>, List<PostQLAMappingDetails>> legalityPostQLAMappingMapper = new LegalityPostQLAMappingMapper();
            List<LegalityPostQLAMappingDTO> legalityPostQLAMappingDto = new List<LegalityPostQLAMappingDTO>();
            List<PostQLAMappingDetails> legalityPostQLAMapping = new List<PostQLAMappingDetails>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityPostQLAMappingDto = await this.Adaptor.GetLegalityPostQLAMapping(runID);
                if (legalityPostQLAMappingDto != null)
                {
                    legalityPostQLAMapping = legalityPostQLAMappingMapper.Map(legalityPostQLAMappingDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalityPostQLAMapping;
        }
        public async Task<List<FlightAttendantBidStatus>> GetLegalityBidStatus(long runID)
        {
            MapperBase<List<LegalityBidStatusDTO>, List<FlightAttendantBidStatus>> legalityBidStatusMapper = new LegalityBidStatusMapper();
            List<LegalityBidStatusDTO> legalityBidStatusDto = new List<LegalityBidStatusDTO>();
            List<FlightAttendantBidStatus> legalityBidStatus = new List<FlightAttendantBidStatus>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityBidStatusDto = await this.Adaptor.GetLegalityBidStatus(runID);
                if (legalityBidStatusDto != null)
                {
                    legalityBidStatus = legalityBidStatusMapper.Map(legalityBidStatusDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalityBidStatus;
        }
        public async Task<List<SequenceLanguageDetails>> GetLegalitySequenceLanguageDetails(long runID)
        {
            MapperBase<List<LegalitySequenceLanguageDetailsDTO>, List<SequenceLanguageDetails>> legalitySequenceLanguageDetailsMapper = new LegalitySequenceLanguageDetailsMapper();
            List<LegalitySequenceLanguageDetailsDTO> legalitySequenceLanguageDetailsDto = new List<LegalitySequenceLanguageDetailsDTO>();
            List<SequenceLanguageDetails> legalitySequenceLanguageDetails = new List<SequenceLanguageDetails>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalitySequenceLanguageDetailsDto = await this.Adaptor.GetLegalitySequenceLanguageDetails(runID);
                if (legalitySequenceLanguageDetailsDto != null)
                {
                    legalitySequenceLanguageDetails = legalitySequenceLanguageDetailsMapper.Map(legalitySequenceLanguageDetailsDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalitySequenceLanguageDetails;
        }
        //TODO: Update return type
        //public async Task<List<LegalityCrewSequenceByRunIDDTO>> GetLegalityCrewSequenceByRunID(long runID)
        //{

        //}
        public async Task<getLegalityBaseProcessingDateByRunID_Result> GetBaseProcessingDate(long runID)
        {
            MapperBase<BaseProcessingDateDTO, getLegalityBaseProcessingDateByRunID_Result> legalitySequenceLanguageDetailsMapper = new BaseProcessingDateMapper();
            BaseProcessingDateDTO legalitySequenceLanguageDetailsDto = new BaseProcessingDateDTO();
            getLegalityBaseProcessingDateByRunID_Result legalitySequenceLanguageDetails = new getLegalityBaseProcessingDateByRunID_Result();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalitySequenceLanguageDetailsDto = await this.Adaptor.GetBaseProcessingDate(runID);
                if (legalitySequenceLanguageDetailsDto != null)
                {
                    legalitySequenceLanguageDetails = legalitySequenceLanguageDetailsMapper.Map(legalitySequenceLanguageDetailsDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalitySequenceLanguageDetails;
        }
        //TODO: Update return type
        //public async Task<List<LegalityCrewSequenceDTO>> GetLegalityCrewSequence(long runID)
        //{

        //}


        public async Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> GetLegalityQLASupportingData(long runID)
        {
            MapperBase<List<LegalityQLASupportingDataDTO>, List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataMapper = new LegalityQLASupportingDataMapper();
            List<LegalityQLASupportingDataDTO> getLegalityQLASupportingDataDto = new List<LegalityQLASupportingDataDTO>();
            List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData> getLegalityQLASupportingData = new List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                getLegalityQLASupportingDataDto = await this.Adaptor.GetLegalityQLASupportingData(runID);
                if (getLegalityQLASupportingDataDto != null)
                {
                    getLegalityQLASupportingData = getLegalityQLASupportingDataMapper.Map(getLegalityQLASupportingDataDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return getLegalityQLASupportingData;
        }
        public async Task<long> GetLegalityGetRunId(string baseCD, string processingDate)
        {
            long runId;
            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                runId = await this.Adaptor.GetLegalityGetRunId(baseCD, processingDate);

            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return runId;
        }
        public async Task<List<ROTDContextualRule>> GetAllContextualMappedRules(string applicationType)
        {
            MapperBase<List<AllContextualMappedRulesDTO>, List<ROTDContextualRule>> contextualRuleMapper = new AllContextualMappedRulesMapper();
            List<AllContextualMappedRulesDTO> contextualRuleDto = new List<AllContextualMappedRulesDTO>();
            List<ROTDContextualRule> contextualRule = new List<ROTDContextualRule>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                contextualRuleDto = await this.Adaptor.GetAllContextualMappedRules(applicationType);
                if (contextualRuleDto != null)
                {
                    contextualRule = contextualRuleMapper.Map(contextualRuleDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return contextualRule;
        }
        //TODO: Update return type
        //public async Task<List<WaiverTypeDTO>> getWaiverType()
        //{

        //}
        public async Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality>> getReservesCrewSequenceLegalitybyRunId(long runId)
        {
            MapperBase<List<ReservesCrewSequenceLegalitybyRunIdDTO>, List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality>> reservesCrewSequenceLegalityMapper = new ReservesCrewSequenceLegalitybyRunIdMapper();
            List<ReservesCrewSequenceLegalitybyRunIdDTO> reservesCrewSequenceLegalityDto = new List<ReservesCrewSequenceLegalitybyRunIdDTO>();
            List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality> reservesCrewSequenceLegality = new List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                reservesCrewSequenceLegalityDto = await this.Adaptor.getReservesCrewSequenceLegalitybyRunId(runId);
                if (reservesCrewSequenceLegalityDto != null)
                {
                    reservesCrewSequenceLegality = reservesCrewSequenceLegalityMapper.Map(reservesCrewSequenceLegalityDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return reservesCrewSequenceLegality;
        }
        public async Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality>> getReservesLegalityVolunteerList(long runId)
        {
            MapperBase<List<ReservesCrewSequenceLegalitybyRunIdDTO>, List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality>> reservesCrewSequenceLegalityMapper = new ReservesCrewSequenceLegalitybyRunIdMapper();
            List<ReservesCrewSequenceLegalitybyRunIdDTO> reservesCrewSequenceLegalityDto = new List<ReservesCrewSequenceLegalitybyRunIdDTO>();
            List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality> reservesCrewSequenceLegality = new List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                reservesCrewSequenceLegalityDto = await this.Adaptor.getReservesLegalityVolunteerList(runId);
                if (reservesCrewSequenceLegalityDto != null)
                {
                    reservesCrewSequenceLegality = reservesCrewSequenceLegalityMapper.Map(reservesCrewSequenceLegalityDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return reservesCrewSequenceLegality;
        }
        public async Task<List<RapCode>> getRapCode()
        {
            MapperBase<List<RapCodeDTO>, List<RapCode>> rapCodeMapper = new RapCodeMapper();
            List<RapCodeDTO> rapCodeDto = new List<RapCodeDTO>();
            List<RapCode> rapCode = new List<RapCode>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                rapCodeDto = await this.Adaptor.getRapCode();
                if (rapCodeDto != null)
                {
                    rapCode = rapCodeMapper.Map(rapCodeDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return rapCode;
        }

        public async Task<List<CoTerminalBase>> GetCoTerminalBases()
        {
            MapperBase<List<CoTerminalDTO>, List<CoTerminalBase>> coTerminalBaseMapper = new CoTerminalBaseMapper();
            List<CoTerminalDTO> coTerminalBaseDto = new List<CoTerminalDTO>();
            List<CoTerminalBase> coTerminalBase = new List<CoTerminalBase>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                coTerminalBaseDto = await this.Adaptor.getBaseWithCoTerminals();
                if (coTerminalBaseDto != null)
                {
                    coTerminalBase = coTerminalBaseMapper.Map(coTerminalBaseDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return coTerminalBase;
        }

        //TODO: Update return type
        //public async Task<List<QLARulesWaiverDetailsDTO>> getQLARulesWaiverDetails()
        //{

        //}
        public async Task<List<QLARules>> getQLARules()
        {
            MapperBase<List<QLARulesDTO>, List<QLARules>> qlaRulesMapper = new QLARulesMapper();
            List<QLARulesDTO> qlaRulesDto = new List<QLARulesDTO>();
            List<QLARules> qlaRules = new List<QLARules>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                qlaRulesDto = await this.Adaptor.getQLARules();
                if (qlaRulesDto != null)
                {
                    qlaRules = qlaRulesMapper.Map(qlaRulesDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return qlaRules;
        }

        public async Task<List<PostQLAState>> getPostQLAStates(long runId)
        {
            MapperBase<List<PostQLAStatesDTO>, List<PostQLAState>> postQLAStatesMapper = new PostQLAStatesMapper();
            List<PostQLAStatesDTO> postQLAStatesDto = new List<PostQLAStatesDTO>();
            List<PostQLAState> postQLAStates = new List<PostQLAState>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                postQLAStatesDto = await this.Adaptor.getPostQLAStates(runId);
                if (postQLAStatesDto != null)
                {
                    postQLAStates = postQLAStatesMapper.Map(postQLAStatesDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return postQLAStates;
        }
        public async Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataonly(long runId)
        {
            MapperBase<List<LegalityQLASupportingDataonlyDTO>, List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataMapper = new LegalityQLASupportingDataonlyMapper();
            List<LegalityQLASupportingDataonlyDTO> getLegalityQLASupportingDataDto = new List<LegalityQLASupportingDataonlyDTO>();
            List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData> getLegalityQLASupportingData = new List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                getLegalityQLASupportingDataDto = await this.Adaptor.getLegalityQLASupportingDataonly(runId);
                if (getLegalityQLASupportingDataDto != null)
                {
                    getLegalityQLASupportingData = getLegalityQLASupportingDataMapper.Map(getLegalityQLASupportingDataDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return getLegalityQLASupportingData;
        }
        public async Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataVolunteer(long runId)
        {
            MapperBase<List<LegalityQLASupportingDataVolunteerDTO>, List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataMapper = new LegalityQLASupportingDataVolunteerMapper();
            List<LegalityQLASupportingDataVolunteerDTO> getLegalityQLASupportingDataDto = new List<LegalityQLASupportingDataVolunteerDTO>();
            List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData> getLegalityQLASupportingData = new List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                getLegalityQLASupportingDataDto = await this.Adaptor.getLegalityQLASupportingDataVolunteer(runId);
                if (getLegalityQLASupportingDataDto != null)
                {
                    getLegalityQLASupportingData = getLegalityQLASupportingDataMapper.Map(getLegalityQLASupportingDataDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return getLegalityQLASupportingData;
        }
        public async Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataNonVolunteerStandBy(long runId)
        {
            MapperBase<List<LegalityQLASupportingDataNonVolunteerStandByDTO>, List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataMapper = new LegalityQLASupportingDataNonVolunteerStandByMapper();
            List<LegalityQLASupportingDataNonVolunteerStandByDTO> getLegalityQLASupportingDataDto = new List<LegalityQLASupportingDataNonVolunteerStandByDTO>();
            List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData> getLegalityQLASupportingData = new List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                getLegalityQLASupportingDataDto = await this.Adaptor.getLegalityQLASupportingDataNonVolunteerStandBy(runId);
                if (getLegalityQLASupportingDataDto != null)
                {
                    getLegalityQLASupportingData = getLegalityQLASupportingDataMapper.Map(getLegalityQLASupportingDataDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return getLegalityQLASupportingData;
        }
        public async Task<List<LegalityPhases>> getLegalityPhases()
        {
            MapperBase<List<LegalityPhasesDTO>, List<LegalityPhases>> legalityPhasesMapper = new LegalityPhasesMapper();
            List<LegalityPhasesDTO> legalityPhasesDto = new List<LegalityPhasesDTO>();
            List<LegalityPhases> legalityPhases = new List<LegalityPhases>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                legalityPhasesDto = await this.Adaptor.getLegalityPhases();
                if (legalityPhasesDto != null)
                {
                    legalityPhases = legalityPhasesMapper.Map(legalityPhasesDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return legalityPhases;

        }
        public async Task<List<ContractSection>> getContractSections()
        {
            MapperBase<List<ContractSectionsDTO>, List<ContractSection>> contractSectionsMapper = new ContractSectionsMapper();
            List<ContractSectionsDTO> contractSectionsDto = new List<ContractSectionsDTO>();
            List<ContractSection> contractSections = new List<ContractSection>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                contractSectionsDto = await this.Adaptor.getContractSections();
                if (contractSectionsDto != null)
                {
                    contractSections = contractSectionsMapper.Map(contractSectionsDto);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return contractSections;
        }

        public List<LegalityQLASupportingData> GetTouchRapLegalityQLASupportingData(int runId, int phaseId, DateTime processingDate)
        {
            List<LegalityQLASupportingData> qlaSupportingData = new List<LegalityQLASupportingData>();
            //using (CrewReservesROTDEntities crewReservesDaily = new CrewReservesROTDEntities())
            {
                //come back on TransactionScope
                TransactionOptions transOptions = new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted };
                using (new TransactionScope(TransactionScopeOption.Required, transOptions))
                {
                    if (phaseId == (int)ROTDLegalityPhase.NonVolunteer)
                    {
                        //come back on this for distinct
                        qlaSupportingData = getLegalityQLASupportingDataonly(runId).Result;
                    }
                    else if (phaseId == (int)ROTDLegalityPhase.IsVolunteer)
                    {
                        //come back on this for distinct
                        qlaSupportingData = getLegalityQLASupportingDataVolunteer(runId).Result;
                    }
                    else if (phaseId == (int)ROTDLegalityPhase.NonVolunteerStandBy)
                    {
                        qlaSupportingData = getLegalityQLASupportingDataNonVolunteerStandBy(runId).Result;
                    }
                }
            }

            return qlaSupportingData;
        }

        public List<RcsDetails> SaveQLADetails(List<Model.BusinessObject.Sequence> sequence, List<StandBy> standBy, List<ROTDLegalityContextDetails> contextualDetails, List<QLAResponse> lstQlaResponse, List<QLARequest> lstQlaRequest, List<QLARuleResult> lstQlaRuleResult, List<RcsDetails> lstRcs, int runId, int phaseID, ROTDPhase lstROTDPhase)
        {
            DataRow dr;


            List<QLARules> qlaRuleDetails = null;
            List<RapCode> rapDetails = null;

            try
            {
                //using (var objCrewReserversEntities = new CrewReservesROTDEntities())
                //{

                if (lstQlaResponse != null && lstQlaResponse.Count > 0)
                {
                    qlaRuleDetails = getQLARules().Result;

                    rapDetails = getRapCode().Result;

                    int batchSize = 10; // Set your batch size

                    var sequenceBatches = Batch(sequence, batchSize).ToList();
                    var standByBatches = Batch(standBy, batchSize).ToList();
                    int maxBatches = Math.Max(sequenceBatches.Count, standByBatches.Count);
                    List<SimplifiedContractResponse> simplifiedContractResponseList = new List<SimplifiedContractResponse>();
                    var interpretiveResult = lstROTDPhase.QLAResponse.ToList();
                    List<QLAResponseQLADetail> lstQLAResponseQLADetails = new List<QLAResponseQLADetail>();
                    LogMemoryUsage("Memory usage: start SaveQLADetails with batches " + maxBatches.ToString());

                    for (int i = 0; i < maxBatches; i++)
                    {
                        var currentSequenceBatch = i < sequenceBatches.Count ? sequenceBatches[i] : new List<Sequence>();
                        var currentStandByBatch = i < standByBatches.Count ? standByBatches[i] : new List<StandBy>();


                        var sequenceDetails = new List<SequencePositionDetail>();
                        long memoryBefore = GC.GetTotalMemory(true);

                        CreateROTDQLADataTableColumns();
                        GC.Collect(2, GCCollectionMode.Forced, true);
                        GC.WaitForPendingFinalizers();
                        GC.Collect(2, GCCollectionMode.Forced, true);
                        long memoryAfter = GC.GetTotalMemory(true);
                        Logs.LogInformation($"SaveROTDLegality completed. Memory before: {memoryBefore / (1024 * 1024)}MB, after: {memoryAfter / (1024 * 1024)}MB");

                        //var interpretiveResult = lstROTDPhase.QLAResponse.ToList();

                        List<QLAResponseQLADetail> qlaResponseQLADetails = (from resp in lstQlaResponse
                                                     join req in lstQlaRequest on resp.RequestId equals req.RequestId
                                                     join interpretive in interpretiveResult on resp.RequestId equals interpretive.RequestID
                                                     join seq in currentSequenceBatch on new { A = req.PositionCode, B = req.ActivityID, C = req.ActivityType.ToUpper(), D = req.ActivityOriginationDate.Date } equals new { A = seq.SequencePosition, B = seq.SequenceNumber, C = ActivityTypes.SEQ.ToString(), D = seq.OriginationDate.Date } into seqRes
                                                     from s in seqRes.DefaultIfEmpty()
                                                     join stb in currentStandByBatch on new { A = req.ActivityID, B = req.ActivityType.ToUpper() } equals new { A = stb.StandByID, B = ActivityTypes.STB.ToString() } into stbRes
                                                     from st in stbRes.DefaultIfEmpty()
                                                     join crewSequence in contextualDetails on new { A = s != null ? s.SequencePositionDetailsID : 0, B = st != null ? st.StandByID : 0, C = Convert.ToInt32(req.EmployeeID) } equals new { A = crewSequence.crewSequenceLegality.SequencePositionDetailsID, B = crewSequence.crewSequenceLegality.StandByID, C = crewSequence.crewSequenceLegality.EmployeeID }
                                                     join frap in rapDetails on resp.FosRAPActivityCode equals frap.RapCode1 into fr
                                                     from f in fr.DefaultIfEmpty()
                                                     join crap in rapDetails on resp.CalRAPActivityCode equals crap.RapCode1 into cr
                                                     from c in cr.DefaultIfEmpty()
                                                     select new QLAResponseQLADetail
                                                     {
                                                         RequestId = resp.RequestId,
                                                         IsLegal = resp.IsLegal,
                                                         IsContractual = resp.IsContractual,
                                                         IsQualified = resp.IsQualified,
                                                         IsCurrentRAP = resp.IsCurrentRAP,
                                                         IscurrentFA = resp.IscurrentFA,
                                                         ruleIdentity = resp.ruleIdentity,
                                                         EmployeeID = resp.EmployeeID,
                                                         ActivityCode = req.ActivityCode,
                                                         ActivityID = req.ActivityID,
                                                         ReservesCrewSequenceLegalityID = crewSequence.ReservesCrewSequenceLegalityID,
                                                         LanguageID = interpretive.LanguageID.GetValueOrDefault(),
                                                         FOSRAP = f == null ? 0 : f.RapCodeID,
                                                         CalcRAP = c == null ? 0 : c.RapCodeID
                                                     }).OrderBy(x => x.IscurrentFA).ToList();

                        qlaResponseQLADetails = qlaResponseQLADetails.Distinct().ToList();

                        qlaResponseQLADetails.ForEach(response =>
                        {

                            dr = ds.Tables["ROTD.LegalityQLADetails"].NewRow();
                            dr["IsLegal"] = response.IsLegal;
                            dr["IsContractual"] = response.IsContractual;
                            dr["IsQualified"] = response.IsQualified;
                            dr["Request"] = String.Empty;
                            dr["Response"] = String.Empty;
                            dr["CreatedBy"] = Convert.ToInt64(AuditColumns.CreatedByID);
                            dr["CreateDate"] = DateTime.Now;
                            dr["UpdatedBy"] = Convert.ToInt64(AuditColumns.UpdatedByID);
                            dr["UpdatedDate"] = DateTime.Now;
                            dr["ReservesCrewSequenceLegalityID"] = response.ReservesCrewSequenceLegalityID;
                            dr["LegalityPhaseID"] = phaseID;
                            dr["FosRAP"] = response.FOSRAP == 0 ? (object)DBNull.Value : response.FOSRAP;
                            dr["IsCurrentRAP"] = (response.FOSRAP == 0 && response.CalcRAP == 0) ? (object)DBNull.Value : response.IsCurrentRAP;
                            if (response.LanguageID > 0)
                                dr["LanguageID"] = response.LanguageID;
                            ds.Tables["ROTD.LegalityQLADetails"].Rows.Add(dr);

                        });

                        Logs.LogInformation("ROTDInterpretiveRepository " + "Start Saving" + "Start LegalityQLADetails && Count :  {0}", qlaResponseQLADetails.Count());
                        perofrmBulkCopy(connectionString, ds);
                        ds.Dispose();
                        ds = null;
                        
                        //CreateROTDQLADataTableColumns(); // Recreate for next batch
                        Logs.LogInformation("ROTDInterpretiveRepository " + "End Saving" + "End LegalityQLADetails");
                        lstQLAResponseQLADetails.AddRange(qlaResponseQLADetails);
                        qlaResponseQLADetails = null;

                    }
                    LogMemoryUsage("Memory usage: SaveQLADetails after batches");
                    CreateROTDLegalityQLARulesTableColumns();

                    List<LegalityQLADetail> legalityQLADetails = null;
                    //come back on TransactionScope
                    TransactionOptions transOptions = new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted };
                    using (new TransactionScope(TransactionScopeOption.Required, transOptions))
                    {

                        SqlParameter ParamRun = new SqlParameter("@RunID", runId);
                        SqlParameter ParamPhaseID = new SqlParameter("@PhaseID", phaseID);
                        legalityQLADetails = GetReservesLegalityQLAList(runId, phaseID).Result;
                    }
                    LogMemoryUsage("Memory usage: SaveQLADetails after GetReservesLegalityQLAList");
                    lstQlaRuleResult = lstQlaRuleResult.Select((item, idx) => 
                    { 
                        item.SequenceID = idx + 1; return item; 
                    }).ToList();

                    var qlaRuleResults = (from rule in lstQlaRuleResult
                                          join name in qlaRuleDetails on rule.Rule equals name.RuleName into qlaRule
                                          from r in qlaRule.DefaultIfEmpty()
                                          select new
                                          {
                                              Sequence = rule.SequenceID,
                                              RequestId = rule.RequestId,
                                              ActivityID = rule.ActivityID,
                                              Rule = rule.Rule,
                                              Result = rule.Result,
                                              Messages = rule.Messages,
                                              isContext = rule.isContext,
                                              ruleIdentity = rule.ruleIdentity,
                                              messageObjects = rule.messageObjects,
                                              RuleID = (r == null ? 0 : r.QLARuleID)
                                          }).ToList();

                    var groupQlaRuleResult = (from t in qlaRuleResults
                                              group t by new { t.RequestId } into grp
                                              select new
                                              {
                                                  RequestId = grp.Key.RequestId,
                                                  RulesResult = grp.ToList()
                                              }).ToList();

                    var qlaResponseQLARules = (from qlaRes in lstQLAResponseQLADetails
                                               join qlaDetails in legalityQLADetails on new { A = qlaRes.ReservesCrewSequenceLegalityID, B = qlaRes.FOSRAP, C = Convert.ToBoolean((qlaRes.FOSRAP == 0 && qlaRes.CalcRAP == 0) ? 0 : qlaRes.IsCurrentRAP), D = qlaRes.LanguageID } equals new { A = (int)qlaDetails.ReservesCrewSequenceLegalityID.GetValueOrDefault(), B = qlaDetails.FosRAP.GetValueOrDefault(), C = qlaDetails.IsCurrentRAP.GetValueOrDefault(), D = qlaDetails.LanguageID.GetValueOrDefault() }
                                               join rule in groupQlaRuleResult on qlaRes.RequestId equals rule.RequestId into qlaR
                                               from r in qlaR.DefaultIfEmpty()
                                               select new
                                               {
                                                   qlaResponse = qlaRes,
                                                   legalityQLADetailsID = qlaDetails.LegalityQLADetailsID,
                                                   qlaRuleResult = r != null ? r.RulesResult.Where(b => b.ruleIdentity == qlaRes.ruleIdentity || b.isContext == false).ToList() : null
                                               }).ToList();


                    var messageObjResponse = (from qlaResp in qlaResponseQLARules
                                              join qlaResult in lstQlaRuleResult on qlaResp.qlaResponse.RequestId equals qlaResult.RequestId
                                              where qlaResult.messageObjects != null && qlaResult.messageObjects.pickups.Count > 0 &&
                                                    qlaResult.messageObjects.affectedBy != null && qlaResp.qlaResponse.IscurrentFA == false
                                              select new
                                              {
                                                  qlaResult = qlaResult,
                                                  qlaResp
                                              }).ToList();
                    LogMemoryUsage("Memory usage: SaveQLADetails after joins");
                    if (messageObjResponse != null && messageObjResponse.Count > 0)
                    {

                        var qlaResponseWithMessageObj = qlaResponseQLARules.Where(x => x.qlaRuleResult.Any(y => y.messageObjects != null && y.messageObjects.pickups.Count > 0)).ToList();

                        qlaResponseWithMessageObj.ForEach(response =>
                        {
                            lstRcs.Add(new RcsDetails
                            {
                                ReservesCrewSequenceLegalityID = response.qlaResponse.ReservesCrewSequenceLegalityID,
                                QLADetailsID = response.legalityQLADetailsID,
                                FosRAP = response.qlaResponse.FOSRAP,
                                IsCurrentRAP = Convert.ToBoolean((response.qlaResponse.FOSRAP == 0 && response.qlaResponse.CalcRAP == 0) ? 0 : response.qlaResponse.IsCurrentRAP),
                                LanguageID = response.qlaResponse.LanguageID
                            });

                            // List<QLARuleResult> lst = lstQlaRuleResult.Where(r => r.RequestId == response.RequestId).ToList();
                            if (response.qlaRuleResult != null)
                            {
                                foreach (var item in response.qlaRuleResult)
                                {
                                    //ruleID = qlaRuleDetails.Where(i => i.QLARule1 == item.Rule).Select(r => r.QLARuleID).FirstOrDefault();
                                    dr = ds.Tables["ROTD.LegalityQLARules"].NewRow();
                                    dr["LegalityQLADetailsID"] = response.legalityQLADetailsID;
                                    dr["QLARuleID"] = item.RuleID > 0 ? item.RuleID : (object)DBNull.Value;
                                    dr["Result"] = item.Result;
                                    dr["Message"] = item.Messages;
                                    dr["QLARuleName"] = item.Rule;
                                    dr["ETLUpdateDateTime"] = DateTime.UtcNow;
                                    dr["SequenceID"] = item.Sequence;
                                    ds.Tables["ROTD.LegalityQLARules"].Rows.Add(dr);
                                }
                            }
                        });
                        LogMemoryUsage("Memory usage: SaveQLADetails before saving LegalityQLARules with MessageObjects");
                        Logs.LogInformation("ROTDInterpretiveRepository"+ "Start Saving"+ "Start LegalityQLARules with MessageObjects && Count :  {0}" , qlaResponseWithMessageObj.Count());
                        perofrmBulkCopy(connectionString, ds);
                        Logs.LogInformation("ROTDInterpretiveRepository"+ "End Saving"+ "End LegalityQLARules with MessageObjects and Actual qlaResponseQLARules Count is  " + qlaResponseQLARules.Count());
                        LogMemoryUsage("Memory usage: SaveQLADetails after saving LegalityQLARules with MessageObjects");
                        CreateROTDLegalityQLASupportingDataTableColumns();

                        var rulesCountWithMessage = qlaResponseWithMessageObj.SelectMany(a => a.qlaRuleResult).Count();
                        LogMemoryUsage("Memory usage: SaveQLADetails before GetQLARuleList");
                        List<LegalityQLARule> legalityQLARuleList = GetQLARuleList(rulesCountWithMessage, phaseID, runId).Result;
                        LogMemoryUsage("Memory usage: SaveQLADetails after GetQLARuleList");

                        var groupQlaRule = (from t in legalityQLARuleList
                                            group t by new { t.LegalityQLADetailsID, t.SequenceID } into grp
                                            select new
                                            {
                                                LegalityQLADetailsID = grp.Key.LegalityQLADetailsID,
                                                SequenceID = grp.Key.SequenceID,
                                                QLARules = grp.FirstOrDefault()
                                            }).Distinct().ToList();

                        var messageResponse = (from messageRes in messageObjResponse
                                               join rule in groupQlaRule on new { A = messageRes.qlaResp.legalityQLADetailsID, B = messageRes.qlaResult.SequenceID } equals new { A = rule.LegalityQLADetailsID.GetValueOrDefault(), B = rule.SequenceID }
                                               select new
                                               {
                                                   messageObjectResponse = messageRes.qlaResult,
                                                   qlaResponse = messageRes.qlaResp.qlaResponse,
                                                   legalityQLARulesID = rule.QLARules.LegalityQLARulesID
                                               }).Distinct().ToList();
                        messageResponse.ForEach(response =>
                        {

                            if (response.messageObjectResponse.messageObjects != null && response.messageObjectResponse.messageObjects.pickups.Count > 0 && response.messageObjectResponse.messageObjects.affectedBy != null && response.qlaResponse.IscurrentFA == false)
                            {

                                //var legalityQLARulesID = legalityQLARuleList.Where(x => x.LegalityQLADetailsID == response.qlaResp.legalityQLADetailsID && x.QLARuleName == response.qlaResult.Rule && x.Result == response.qlaResult.Result).Select(x => x.LegalityQLARulesID).FirstOrDefault();

                                dr = ds.Tables["ROTD.LegalityQLASupportingData"].NewRow();
                                dr["LegalityQLARulesID"] = response.legalityQLARulesID;
                                dr["RunId"] = runId;
                                dr["EmployeeId"] = response.qlaResponse.EmployeeID;
                                dr["AffectedType"] = response.messageObjectResponse.messageObjects.affectedType;
                                dr["ContractMonth"] = response.messageObjectResponse.messageObjects.affectedBy.contractMonth;
                                dr["ActivityId"] = response.messageObjectResponse.messageObjects.affectedBy.activityId;
                                dr["ActivityCode"] = response.messageObjectResponse.messageObjects.affectedBy.activityCode;
                                dr["ActivityType"] = response.messageObjectResponse.messageObjects.affectedBy.activityType;
                                dr["ActivityOriginationDate"] = response.messageObjectResponse.messageObjects.affectedBy.activityOriginationDate;
                                dr["PositionCode"] = response.messageObjectResponse.messageObjects.affectedBy.positionCode;
                                dr["StartDateTime"] = response.messageObjectResponse.messageObjects.affectedBy.startDateTime;
                                dr["EndDateTime"] = response.messageObjectResponse.messageObjects.affectedBy.endDateTime;
                                dr["PickupContractMonth"] = response.messageObjectResponse.messageObjects.pickups[0].contractMonth;
                                dr["PickupActivityId"] = response.messageObjectResponse.messageObjects.pickups[0].activityId > 0 ? response.messageObjectResponse.messageObjects.pickups[0].activityId : response.qlaResponse.ActivityID;
                                dr["PickupActivityCode"] = response.qlaResponse.ActivityCode;
                                dr["PickupActivityType"] = response.messageObjectResponse.messageObjects.pickups[0].activityType;
                                dr["PickupActivityOriginationDate"] = response.messageObjectResponse.messageObjects.pickups[0].activityOriginationDate;
                                dr["PickupPositionCode"] = response.messageObjectResponse.messageObjects.pickups[0].positionCode;
                                dr["PickupStartDateTime"] = response.messageObjectResponse.messageObjects.pickups[0].startDateTime;
                                dr["PickupEndDateTime"] = response.messageObjectResponse.messageObjects.pickups[0].endDateTime;
                                dr["ActivityReportDateTime"] = response.messageObjectResponse.messageObjects.affectedStartDateTime == null ? (object)DBNull.Value : response.messageObjectResponse.messageObjects.affectedStartDateTime;
                                ds.Tables["ROTD.LegalityQLASupportingData"].Rows.Add(dr);
                            }


                        });
                        LogMemoryUsage("Memory usage: SaveQLADetails before save LegalityQLASupportingData");
                        Logs.LogInformation("ROTDInterpretiveRepository "+ "Start Saving"+ "Start LegalityQLASupportingData && Count :  {0}" , messageResponse.Count());
                        perofrmBulkCopy(connectionString, ds);
                        Logs.LogInformation("ROTDInterpretiveRepository "+ "End Saving"+ "End LegalityQLASupportingData");
                        LogMemoryUsage("Memory usage: SaveQLADetails after save LegalityQLASupportingData");
                        //perofrmBulkCopy(objCrewReserversEntities.Database.Connection.ConnectionString, ds);
                        ds = null;
                        groupQlaRule = null;
                        messageResponse = null;
                        legalityQLARuleList = null;
                        qlaResponseWithMessageObj = null;

                    }

                    CreateROTDLegalityQLARulesTableColumns();
                    var qlaResponseWithOutMessageObj = qlaResponseQLARules.Where(x => !x.qlaRuleResult.Any(y => y.messageObjects != null && y.messageObjects.pickups.Count > 0)).ToList();

                    qlaResponseWithOutMessageObj.ForEach(response =>
                    {
                        lstRcs.Add(new RcsDetails
                        {
                            ReservesCrewSequenceLegalityID = response.qlaResponse.ReservesCrewSequenceLegalityID,
                            QLADetailsID = response.legalityQLADetailsID,
                            FosRAP = response.qlaResponse.FOSRAP,
                            IsCurrentRAP = Convert.ToBoolean((response.qlaResponse.FOSRAP == 0 && response.qlaResponse.CalcRAP == 0) ? 0 : response.qlaResponse.IsCurrentRAP),
                            LanguageID = response.qlaResponse.LanguageID
                        });

                        // List<QLARuleResult> lst = lstQlaRuleResult.Where(r => r.RequestId == response.RequestId).ToList();
                        if (response.qlaRuleResult != null)
                        {
                            foreach (var item in response.qlaRuleResult)
                            {
                                //ruleID = qlaRuleDetails.Where(i => i.QLARule1 == item.Rule).Select(r => r.QLARuleID).FirstOrDefault();
                                dr = ds.Tables["ROTD.LegalityQLARules"].NewRow();
                                dr["LegalityQLADetailsID"] = response.legalityQLADetailsID;
                                dr["QLARuleID"] = item.RuleID > 0 ? item.RuleID : (object)DBNull.Value;
                                dr["Result"] = item.Result;
                                dr["Message"] = item.Messages;
                                dr["QLARuleName"] = item.Rule;
                                dr["ETLUpdateDateTime"] = DateTime.UtcNow;
                                dr["SequenceID"] = item.Sequence;
                                ds.Tables["ROTD.LegalityQLARules"].Rows.Add(dr);
                            }
                        }

                    });
                    LogMemoryUsage("Memory usage: SaveQLADetails before save LegalityQLARules without MessageObjects");
                    Logs.LogInformation("ROTDInterpretiveRepository "+ "Start Saving"+ "Start LegalityQLARules without MessageObjects && Count : {0} " , qlaResponseWithOutMessageObj.Count());
                    perofrmBulkCopy(connectionString, ds);
                    Logs.LogInformation("ROTDInterpretiveRepository "+ "End Saving"+ "End LegalityQLARules without MessageObjects ");
                    LogMemoryUsage("Memory usage: SaveQLADetails after save LegalityQLARules without MessageObjects");

                    qlaRuleDetails = null;
                    rapDetails = null;
                    sequenceBatches = null;
                    standByBatches = null;
                    simplifiedContractResponseList = null;
                    interpretiveResult = null;
                    lstQLAResponseQLADetails = null;
                    legalityQLADetails = null;
                    qlaRuleResults = null;
                    groupQlaRuleResult = null;
                    qlaResponseQLARules = null;
                    messageObjResponse = null;
                    qlaResponseWithOutMessageObj = null;
                    GC.Collect(2, GCCollectionMode.Forced, true);
                    GC.WaitForPendingFinalizers();
                    GC.Collect(2, GCCollectionMode.Forced, true);
                    LogMemoryUsage("Memory usage: SaveQLADetails after clearing variables and force GC");
                }
                //}
            }
            catch
            {
                throw;
            }

            return lstRcs;
        }

        public void SaveQLAMessage(Int64 runId, Int64 legalityPhaseId, Dictionary<string, string> qlaRequestResponse)
        {
            DataRow dr;
            CreateROTDLegalityQLAMessageTableColumns();
            try
            {
                foreach (var qlaReqRes in qlaRequestResponse)
                {
                    dr = ds.Tables["ROTD.LegalityQLAMessage"].NewRow();
                    dr["RunID"] = runId;
                    dr["QLARequest"] = LogSanitizer.SerializeSafely(System.Text.Json.JsonSerializer.Deserialize<object>(qlaReqRes.Key));
                    dr["QLAResponse"] = LogSanitizer.SerializeSafely(System.Text.Json.JsonSerializer.Deserialize<object>(qlaReqRes.Value));
                    dr["CreatedBy"] = Convert.ToInt64(AuditColumns.UpdatedByID);
                    dr["CreatedDate"] = DateTime.Now;
                    dr["LegalityPhaseID"] = legalityPhaseId;
                    ds.Tables["ROTD.LegalityQLAMessage"].Rows.Add(dr);
                }

                perofrmBulkCopy(connectionString, ds);
            }
            catch
            {
                throw;
            }
        }

        public void SaveQLAMessage(Int64 runId, Int64 legalityPhaseId, string rawRequest, string rawResponse)
        {
            try
            {
                this.Adaptor.Connect();
                rawRequest = LogSanitizer.SerializeSafely(System.Text.Json.JsonSerializer.Deserialize<object>(rawRequest));
                rawResponse = LogSanitizer.SerializeSafely(System.Text.Json.JsonSerializer.Deserialize<object>(rawResponse));
                this.Adaptor.SaveLegalityQLAMessage(runId, legalityPhaseId, rawRequest, rawResponse, Convert.ToInt64(AuditColumns.UpdatedByID), DateTime.Now);
            }
            catch
            {
                throw;
            }
            finally
            {
                this.Adaptor.Close();
            }
        }

        public async Task<bool> UpdateAwardProgressAsInProgress(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime startDateTime)
        {
            const int maxRetries = 3;
            bool isSuccess;
            int attempt = 0;
            while (true)
            {
                try
                {
                    //await this.Adaptor.Connect();
                    isSuccess = await this.Adaptor.UpdateAwardProgressStepAsInProgressDaily(baseCode, processingDate, awardProgressStepID, startDateTime);
                    return isSuccess;
                }
                catch (Exception ex)
                {
                    attempt++;
                    _log.LogError(ex, $"{_logTitle} UpdateAwardProgressAsInProgress()- Attempt {attempt} failed. Message: {ex.Message}");
                    if (attempt >= maxRetries)
                        throw;
                    await Task.Delay(1000 * attempt); // Exponential backoff
                }
                finally
                {
                    this.Adaptor.Close();
                }
            }
            return false;
        }

        public async Task<bool> UpdateAwardProgressAsComplete(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime endDateTime)
        {
            const int maxRetries = 3;
            bool isSuccess;
            int attempt = 0;
            while (true)
            {
                try
                {
                    //await this.Adaptor.Connect();
                    isSuccess = await this.Adaptor.UpdateAwardProgressStepAsCompleteDaily(baseCode, processingDate, awardProgressStepID, endDateTime);
                    return isSuccess;
                }
                catch (Exception ex)
                {
                    attempt++;
                    _log.LogError(ex, $"{_logTitle} UpdateAwardProgressAsComplete()- Attempt {attempt} failed. Message: {ex.Message}");
                    if (attempt >= maxRetries)
                        throw;
                    await Task.Delay(1000 * attempt); // Exponential backoff
                }
                finally
                {
                    this.Adaptor.Close();
                }
            }
            return false;
        }

        public async Task<bool> UpdateAwardProgressAsError(string baseCode, DateTime processingDate, long awardProgressStepID)
        {
            const int maxRetries = 3;
            bool isSuccess;
            int attempt = 0;
            while (true)
            {
                try
                {
                    //await this.Adaptor.Connect();
                    isSuccess = await this.Adaptor.UpdateAwardProgressStepAsErrorDaily(baseCode, processingDate, awardProgressStepID);
                    return isSuccess;
                }
                catch (Exception ex)
                {
                    attempt++;
                    _log.LogError(ex, $"{_logTitle} UpdateAwardProgressAsError()- Attempt {attempt} failed. Message: {ex.Message}");
                    if (attempt >= maxRetries)
                        throw;
                    await Task.Delay(1000 * attempt); // Exponential backoff
                }
                finally
                {
                    this.Adaptor.Close();
                }
            }
            return false;
        }

        public static IEnumerable<List<T>> Batch<T>(List<T> source, int batchSize)
        {
            for (int i = 0; i < source.Count; i += batchSize)
                yield return source.GetRange(i, Math.Min(batchSize, source.Count - i));
        }

        public void SaveROTDInterpretiveLegality(List<Sequence> sequence, List<StandBy> standBy, List<ROTDLegalityContextDetails> contextualDetails, ROTDPhase lstROTDPhase, List<RcsDetails> lstRcs, int runId, int phaseID, List<LegalityQLASupportingData> qlaSupportingData, DateTime processingDate)
        {
            DataRow dr;

            List<PostQLAState> qlaStatesDetails = null;
            List<RapCode> rapDetails = null;
            List<QLARules> qlaRuleDetails = null;
            bool isTouchRapSupportingData = false;

            try
            {
                //using (var objCrewReserversEntities = new CrewReservesROTDEntities())
                {
                    qlaStatesDetails = getPostQLAStates(runId).Result;

                    qlaRuleDetails = getQLARules().Result;

                    rapDetails = getRapCode().Result;

                    var postQLAStates = (from state in qlaStatesDetails select state).ToList();

                    var response = lstROTDPhase.QLAResponse.SelectMany(s => s.ContractSections, (s, message) => new { s, message }).ToList();

                    if (response != null && response.Count > 0)
                    {
                        int batchSize = 10; // Set your batch size

                        var sequenceBatches = Batch(sequence, batchSize).ToList();
                        var standByBatches = Batch(standBy, batchSize).ToList();
                        int maxBatches = Math.Max(sequenceBatches.Count, standByBatches.Count);
                        List<SimplifiedContractResponse> simplifiedContractResponseList = new List<SimplifiedContractResponse>();

                        for (int i = 0; i < maxBatches; i++)
                        {
                            var currentSequenceBatch = i < sequenceBatches.Count ? sequenceBatches[i] : new List<Sequence>();
                            var currentStandByBatch = i < standByBatches.Count ? standByBatches[i] : new List<StandBy>();


                            var sequenceDetails = new List<SequencePositionDetail>();
                            long memoryBefore = GC.GetTotalMemory(true);
                            CreateROTDInterpretiveLegalityTableColumns();
                            GC.Collect(2, GCCollectionMode.Forced, true);
                            GC.WaitForPendingFinalizers();
                            GC.Collect(2, GCCollectionMode.Forced, true);
                            long memoryAfter = GC.GetTotalMemory(true);
                            Logs.LogInformation($"SaveROTDLegality completed. Memory before: {memoryBefore / (1024 * 1024)}MB, after: {memoryAfter / (1024 * 1024)}MB");


                            Logs.LogInformation("ROTDInterpretiveDataProvider" + "Start contractResponse");
                            var contractResponse = (from resp in response
                                                    join seq in currentSequenceBatch on new { A = resp.s.SequencePosition, B = resp.s.ActivityID, C = resp.s.SequenceOriginationDate.Date, D = resp.s.ActivityType.ToUpper() } equals new { A = seq.SequencePosition, B = seq.SequenceNumber, C = seq.OriginationDate.Date, D = ActivityTypes.SEQ.ToString() } into seqRes
                                                    from s in seqRes.DefaultIfEmpty()
                                                    join stb in currentStandByBatch on new { A = resp.s.ActivityID, B = resp.s.ActivityType.ToUpper() } equals new { A = stb.StandByID, B = ActivityTypes.STB.ToString() } into stbRes
                                                    from st in stbRes.DefaultIfEmpty()
                                                    join crewSequence in contextualDetails on new { A = s != null ? s.SequencePositionDetailsID : 0, B = st != null ? st.StandByID : 0, C = Convert.ToInt32(resp.s.EmployeeNo) } equals
                                                       new { A = crewSequence.crewSequenceLegality.SequencePositionDetailsID, B = crewSequence.crewSequenceLegality.StandByID, C = crewSequence.crewSequenceLegality.EmployeeID }
                                                    join frap in rapDetails on resp.s.FosRAP equals frap.RapCode1 into fr
                                                    from f in fr.DefaultIfEmpty()
                                                    join crap in rapDetails on resp.s.CalculateRAP equals crap.RapCode1 into cr
                                                    from c in cr.DefaultIfEmpty()
                                                    join rcs in lstRcs on new { A = crewSequence.ReservesCrewSequenceLegalityID, B = (f == null ? 0 : f.RapCodeID), C = Convert.ToBoolean(((f == null ? 0 : f.RapCodeID) == 0 && (c == null ? 0 : c.RapCodeID) == 0) ? 0 : resp.s.IsCurrentRAP), D = resp.s.LanguageID.GetValueOrDefault() } equals new
                                                    { A = rcs.ReservesCrewSequenceLegalityID, B = rcs.FosRAP, C = rcs.IsCurrentRAP, D = rcs.LanguageID }
                                                    select new
                                                    {
                                                        resp.s,
                                                        resp.message,
                                                        crewSequence.ReservesCrewSequenceLegalityID,
                                                        rcs.QLADetailsID,
                                                        FOSRAP = f == null ? 0 : f.RapCodeID,
                                                        CalcRAP = c == null ? 0 : c.RapCodeID,
                                                        isOver35By7 = crewSequence.crewSequenceLegality.IsOver35By7,
                                                        isOver35By7LH = crewSequence.crewSequenceLegality.IsOver35By7LH,
                                                        resp.s.ContractSections
                                                    }).OrderBy(x => x.QLADetailsID).ToList();

                            Logs.LogInformation("ROTDInterpretiveDataProvider" + "End contractResponse");
                            contractResponse.ForEach(res =>
                            {
                                var simplified = new SimplifiedContractResponse
                                {
                                    ReservesCrewSequenceLegalityID = Convert.ToInt64(res.ReservesCrewSequenceLegalityID),
                                    QLADetailsID = res.QLADetailsID,
                                    FOSRAP = res.FOSRAP,
                                    CalcRAP = res.CalcRAP,
                                    IsCurrentRAP = res.s.IsCurrentRAP,
                                    LanguageID = res.s.LanguageID,
                                    ContractSectionId = res.message.contractSectionId,
                                    IsLegal = res.message.isLegal,
                                    RuleDetails = res.message.ruleDetails != null ? res.message.ruleDetails : new List<ROTDRuleDetails>(),
                                    EmployeeNo = res.s.EmployeeNo,
                                    ActivityID = res.s.ActivityID,
                                    SequencePosition = res.s.SequencePosition,
                                    SequenceOriginationDate = res.s.SequenceOriginationDate
                                };
                                simplifiedContractResponseList.Add(simplified);

                                dr = ds.Tables["ROTD.ReservesCrewSequenceLegalityContractDetails"].NewRow();
                                dr["ContractSectionsID"] = res.message.contractSectionId;
                                dr["LegalityPhaseID"] = lstROTDPhase.phaseId;
                                dr["IsLegal"] = res.message.isLegal;
                                dr["ReservesCrewSequenceLegalityID"] = res.ReservesCrewSequenceLegalityID;
                                if (res.s.LanguageID != null)
                                    dr["LanguageID"] = res.s.LanguageID;
                                dr["FosRAP"] = res.FOSRAP == 0 ? (object)DBNull.Value : res.FOSRAP;
                                dr["CalcRAP"] = res.CalcRAP == 0 ? (object)DBNull.Value : res.CalcRAP;
                                dr["IsCurrentRAP"] = (res.FOSRAP == 0 && res.CalcRAP == 0) ? (object)DBNull.Value : res.s.IsCurrentRAP;
                                dr["IsOver35By7"] = res.isOver35By7;
                                dr["IsOver35By7LH"] = res.isOver35By7LH;
                                ds.Tables["ROTD.ReservesCrewSequenceLegalityContractDetails"].Rows.Add(dr);

                                if (res.message.ruleDetails != null)
                                {
                                    var postQLAStatusID = 0;
                                    string rules = string.Empty;
                                    foreach (var rule in res.message.ruleDetails)
                                    {
                                        if (rule.ruleId == 0)
                                        {
                                            postQLAStatusID = 3;
                                        }
                                        else
                                        {
                                            var postQLAStatusID1 = (from state in postQLAStates
                                                                    where state.PostQLAState1.Contains(rule.value)
                                                                    select state.PostQLAStateID).FirstOrDefault();
                                            postQLAStatusID = Convert.ToInt32(postQLAStatusID1);
                                        }
                                        if (!rule.isLegal)
                                        {
                                            rules = rules == "" ? rules + rule.ruleName : rules + "\n" + rule.ruleName;
                                        }
                                    }

                                    dr = ds.Tables["ROTD.LegalityPostQLAStatus"].NewRow();
                                    dr["ContractSectionsID"] = res.message.contractSectionId;
                                    dr["PostQLAStateID"] = postQLAStatusID > 0 ? postQLAStatusID : (object)DBNull.Value; ;
                                    if (res.QLADetailsID > 0)
                                        dr["LegalityQLADetailsID"] = res.QLADetailsID;
                                    dr["LegalityPhaseID"] = lstROTDPhase.phaseId;
                                    dr["RuleID"] = 0;
                                    dr["RuleName"] = rules;
                                    ds.Tables["ROTD.LegalityPostQLAStatus"].Rows.Add(dr);
                                }


                            });
                            Logs.LogInformation("ROTDInterpretiveDataProvider" + "collected ROTD.ReservesCrewSequenceLegalityContractDetails");

                            Logs.LogInformation("ROTDInterpretiveRepository" + "Start Saving" + "Start ReservesCrewSequenceLegalityContractDetails" + " LegalityPostQLAStatus && Count :  {0}", contractResponse.Count());
                            perofrmBulkCopy(connectionString, ds);
                            Logs.LogInformation("ROTDInterpretiveRepository" + "End Saving" + "End ReservesCrewSequenceLegalityContractDetails " + " LegalityPostQLAStatus");
                        }

                        CreateROTDRCSTableColumns();
                        CreateROTDDropActivityDetailsTableColumns();

                        List<ContractDetailsTable> reservesCrewSequenceLegalityContractDetailList = GetReservesCrewSequenceLegalityContractDetails(simplifiedContractResponseList.Count(), phaseID, runId).Result;

                        if (phaseID == (int)ROTDLegalityPhase.NonVolunteer || phaseID == (int)ROTDLegalityPhase.IsVolunteer || phaseID == (int)ROTDLegalityPhase.NonVolunteerStandBy)
                        {
                            qlaSupportingData = GetTouchRapLegalityQLASupportingData(runId, phaseID, processingDate);
                            isTouchRapSupportingData = (qlaSupportingData != null && qlaSupportingData.Count > 0);
                        }

                        var respContract = (from contRes in simplifiedContractResponseList
                                            join conDetails in reservesCrewSequenceLegalityContractDetailList on new
                                            { A = Convert.ToInt64(contRes.ReservesCrewSequenceLegalityID), B = contRes.FOSRAP, C = Convert.ToBoolean((contRes.FOSRAP == 0 && contRes.CalcRAP == 0) ? 0 : contRes.IsCurrentRAP), D = contRes.LanguageID, E = contRes.ContractSectionId, F = Convert.ToInt64(phaseID) } equals new
                                            { A = conDetails.ReservesCrewSequenceLegalityID.GetValueOrDefault(), B = conDetails.FosRAP.GetValueOrDefault(), C = conDetails.IsCurrentRAP.GetValueOrDefault(), D = conDetails.LanguageID, E = conDetails.ContractSectionsID.GetValueOrDefault(), F = conDetails.LegalityPhaseID.GetValueOrDefault() }
                                            select new
                                            {
                                                contRes,
                                                conDetails.ReservesCrewSequenceLegalityContractDetailsID
                                            }).ToList();


                        respContract.ForEach(contract =>
                        {

                            dr = ds.Tables["ROTD.RCS"].NewRow();
                            dr["ReservesCrewSequenceLegalityContractDetailsID"] = contract.ReservesCrewSequenceLegalityContractDetailsID;
                            dr["LegalityQLADetailsID"] = contract.contRes.QLADetailsID;
                            ds.Tables["ROTD.RCS"].Rows.Add(dr);

                            //Save Drop Activity Details
                            if (((phaseID == (int)ROTDLegalityPhase.Future ||
                                  phaseID == (int)ROTDLegalityPhase.ETB ||
                                  phaseID == (int)ROTDLegalityPhase.ETB_Future ||
                                  phaseID == (int)ROTDLegalityPhase.LineHolder ||
                                  phaseID == (int)ROTDLegalityPhase.LineHolder_Future ||
                                  phaseID == (int)ROTDLegalityPhase.LineHolderStandBy
                                  ) && contract.contRes.IsLegal
                                  )
                                 ||
                                (isTouchRapSupportingData && contract.contRes.IsLegal &&
                                (contract.contRes.RuleDetails != null && contract.contRes.RuleDetails.Any(x => x.ruleName.ToUpper().Contains(QLARuleConst.TOUCHRAP)))))
                            {

                                var dropData = (from supportData in qlaSupportingData
                                                where supportData.RunId == runId &&
                                                      supportData.EmployeeId == Convert.ToInt64(contract.contRes.EmployeeNo) &&
                                                      supportData.AffectedType != ROTDLegalityPhase.LineHolder.ToString().ToUpper() &&
                                                     ((supportData.PickupActivityType.ToUpper() == ActivityTypes.SEQ.ToString() &&
                                                      supportData.PickupActivityId == contract.contRes.ActivityID &&
                                                      supportData.PickupPositionCode == contract.contRes.SequencePosition &&
                                                      Convert.ToDateTime(supportData.PickupActivityOriginationDate).Date == contract.contRes.SequenceOriginationDate.Date)
                                                      ||
                                                     (supportData.PickupActivityType.ToUpper() == ActivityTypes.STB.ToString() &&
                                                      supportData.PickupActivityId == contract.contRes.ActivityID))
                                                select new
                                                {
                                                    ActivityType = supportData.ActivityType,
                                                    ActivityCode = supportData.ActivityCode,
                                                    ActivityId = supportData.ActivityId,
                                                    PositionCode = supportData.PositionCode,
                                                    ActivityOriginationDate = supportData.ActivityOriginationDate,
                                                    ActivityReportDateTime = supportData.ActivityReportDateTime
                                                }).Distinct().ToList();

                                foreach (var data in dropData)
                                {
                                    dr = ds.Tables["ROTD.DropActivityDetails"].NewRow();
                                    dr["ActivityCode"] = data.ActivityType == ActivityTypes.RAP.ToString() ? data.ActivityCode : (data.ActivityId > 0 ? data.ActivityId.ToString() : (object)DBNull.Value);
                                    dr["OriginationDate"] = data.ActivityOriginationDate == null ? (object)DBNull.Value : data.ActivityOriginationDate;
                                    dr["SequencePosition"] = data.PositionCode;
                                    dr["OpenSequenceAwardID"] = DBNull.Value;
                                    dr["ActivityStartDateTime"] = data.ActivityReportDateTime == null ? (object)DBNull.Value : data.ActivityReportDateTime;
                                    if (string.IsNullOrEmpty(data.ActivityType))
                                    {
                                        dr["DropActivityTypeID"] = (object)DBNull.Value;
                                    }
                                    else
                                    {
                                        dr["DropActivityTypeID"] = (data.ActivityType.ToUpper() == ActivityTypes.SEQ.ToString() ? (long)DropActivityType.Sequence :
                                                                   (data.ActivityType.ToUpper() == ActivityTypes.STB.ToString() ? (long)DropActivityType.Standby :
                                                                   (data.ActivityType.ToUpper() == ActivityTypes.RAP.ToString() ? (long)DropActivityType.RAP : (long)DropActivityType.Ghost)));
                                    }
                                    dr["ActivityEndDateTime"] = (object)DBNull.Value;
                                    dr["ReservesCrewSequenceLegalityContractDetailsID"] = contract.ReservesCrewSequenceLegalityContractDetailsID;
                                    ds.Tables["ROTD.DropActivityDetails"].Rows.Add(dr);
                                }
                            }

                        });

                        Logs.LogInformation("ROTDInterpretiveRepository" + "Start Saving" + "Start RCS+ DropActivityDetails  && Count :  {0}", respContract.Count());
                        perofrmBulkCopy(connectionString, ds);
                        Logs.LogInformation("ROTDInterpretiveRepository" + "End Saving" + "End  RCS+ DropActivityDetails");
                    }
                    
                }
            }
            catch
            {
                throw;
            }
        }

        public void SaveROTDLegality(List<ROTDLegalityContextDetails> contextualDetails, int runId, int phaseId)
        {
            //using (CrewReservesROTDEntities crewReservesDaily = new CrewReservesROTDEntities())
            {
                legalityPhase = string.Empty;

                DataRow dr;
                try
                {
                    if (contextualDetails != null && contextualDetails.Count > 0)
                    {

                        CreateReservesCrewSequenceLegalityTableColumns();

                        foreach (var context in contextualDetails)
                        {
                            if (context.crewSequenceLegality != null)
                            {

                                dr = ds.Tables["ROTD.ReservesCrewSequenceLegality"].NewRow();
                                dr["ReservesCrewMemberID"] = context.crewSequenceLegality.ReservesCrewMemberID;
                                dr["RunID"] = context.crewSequenceLegality.RunID;
                                if (context.crewSequenceLegality.SequencePositionDetailsID > 0)
                                    dr["SequencePositionDetailsID"] = context.crewSequenceLegality.SequencePositionDetailsID;
                                if (context.crewSequenceLegality.StandByID > 0)
                                    dr["StandByID"] = context.crewSequenceLegality.StandByID;
                                //dr["IsOver35By7"] = context.crewSequenceLegality.IsOver35By7;                                
                                ds.Tables["ROTD.ReservesCrewSequenceLegality"].Rows.Add(dr);
                            }
                        }
                        Logs.LogInformation("ROTDInterpretiveRepository"+ "Start Saving"+ "Start ReservesCrewSequenceLegality && Count :  {0}" , contextualDetails.Count());
                        perofrmBulkCopy(connectionString, ds);
                        Logs.LogInformation("ROTDInterpretiveRepository"+ "End Saving"+ "End ReservesCrewSequenceLegality");


                        CreateContextualDataTableColumns();

                        List<ReservesCrewSequenceLegality> reserveLegality = null;

                        TransactionOptions transOptions = new TransactionOptions() { IsolationLevel = System.Transactions.IsolationLevel.ReadUncommitted };
                        using (new TransactionScope(TransactionScopeOption.Required, transOptions))
                        {
                            if ((ROTDLegalityPhase)phaseId == ROTDLegalityPhase.NonVolunteer || (ROTDLegalityPhase)phaseId == ROTDLegalityPhase.NonVolunteerStandBy)
                            {
                                reserveLegality = getReservesCrewSequenceLegalitybyRunId(runId).Result;
                            }
                            else if ((ROTDLegalityPhase)phaseId == ROTDLegalityPhase.IsVolunteer)
                            {
                                SqlParameter ParamRun = new SqlParameter("@RunID", runId);
                                reserveLegality = getReservesLegalityVolunteerList(runId).Result;
                            }
                        }

                        var legalityContextDetails = (from reserve in reserveLegality
                                                      join con in contextualDetails on reserve.ReservesCrewMemberID equals con.crewSequenceLegality.ReservesCrewMemberID
                                                      where reserve.SequencePositionDetailsID > 0 ? reserve.SequencePositionDetailsID == con.crewSequenceLegality.SequencePositionDetailsID
                                                                                                  : reserve.StandByID == con.crewSequenceLegality.StandByID
                                                      select new
                                                      {
                                                          ReservesCrewSequenceLegalityID = reserve.ReservesCrewSequenceLegalityID,
                                                          IsLegal = con.IsLegal,
                                                          Message = con.Message,
                                                          ContextDetails = con
                                                      });

                        foreach (var context in legalityContextDetails)
                        {
                            context.ContextDetails.ReservesCrewSequenceLegalityID = Convert.ToInt32(context.ReservesCrewSequenceLegalityID);

                            dr = ds.Tables["ROTD.LegalityContextDetails"].NewRow();
                            dr["ReservesCrewSequenceLegalityID"] = context.ReservesCrewSequenceLegalityID;
                            dr["IsLegal"] = context.IsLegal;
                            if (context.Message != null)
                                dr["Message"] = context.Message;
                            dr["CreatedBy"] = Convert.ToInt64(AuditColumns.CreatedByID);
                            dr["CreateDate"] = DateTime.Now;
                            dr["UpdatedBy"] = Convert.ToInt64(AuditColumns.UpdatedByID);
                            dr["UpdatedDate"] = DateTime.Now;
                            ds.Tables["ROTD.LegalityContextDetails"].Rows.Add(dr);
                        }
                        Logs.LogInformation("ROTDInterpretiveRepository "+ "Start Saving"+ "Start LegalityContextDetails && Count : " + legalityContextDetails.Count());
                        perofrmBulkCopy(connectionString, ds);
                        Logs.LogInformation("ROTDInterpretiveRepository "+ "End Saving"+ "End LegalityContextDetails");
                    }
                }
                catch
                {
                    throw;
                }
            }
        }

        public void SetContextDetailsReservesCrewSequenceLegalityID(List<ROTDLegalityContextDetails> contextualDetails, int runId)
        {
            List<ReservesCrewSequenceLegality> reservesCrewSequenceLegality;

            //using (var crewReservesDaily = new CrewReservesROTDEntities())
            {
                reservesCrewSequenceLegality = getReservesCrewSequenceLegalitybyRunId(runId).Result;
            }

            foreach (var context in contextualDetails)
            {
                if (context.crewSequenceLegality != null)
                {
                    var reservesCrewSequenceLegalityID = reservesCrewSequenceLegality.Where(x => x.ReservesCrewMemberID == context.crewSequenceLegality.ReservesCrewMemberID &&
                                                                                           (x.SequencePositionDetailsID > 0 ? x.SequencePositionDetailsID == context.crewSequenceLegality.SequencePositionDetailsID
                                                                                            : x.StandByID == context.crewSequenceLegality.StandByID))
                                                                                           .Select(x => x.ReservesCrewSequenceLegalityID).FirstOrDefault();

                    context.ReservesCrewSequenceLegalityID = Convert.ToInt32(reservesCrewSequenceLegalityID);
                }
            }
        }

        public void CreateReservesCrewSequenceLegalityTableColumns()
        {
            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "ROTD.ReservesCrewSequenceLegality";
            dt.Columns.Add("ROTDReservesCrewSequenceLegalityID", typeof(Int64));
            dt.Columns.Add("ReservesCrewMemberID", typeof(Int64));
            dt.Columns.Add("RunID", typeof(Int64));
            dt.Columns.Add("SequencePositionDetailsID", typeof(Int64));
            dt.Columns.Add("StandByID", typeof(Int32));
            //dt.Columns.Add("IsOver35By7", typeof(Boolean));            
            ds.Tables.Add(dt);
        }

        public void CreateContextualDataTableColumns()
        {
            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "ROTD.LegalityContextDetails";
            dt.Columns.Add("LegalityContextDetailsID", typeof(Int64));
            dt.Columns.Add("ReservesCrewSequenceLegalityID", typeof(Int64));
            dt.Columns.Add("IsLegal", typeof(Boolean));
            dt.Columns.Add("Message", typeof(string));
            dt.Columns.Add("CreatedBy", typeof(Int64));
            dt.Columns.Add("CreateDate", typeof(DateTime));
            dt.Columns.Add("UpdatedBy", typeof(Int64));
            dt.Columns.Add("UpdatedDate", typeof(DateTime));
            ds.Tables.Add(dt);
        }

        public void CreateROTDInterpretiveLegalityTableColumns()
        {

            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "ROTD.ReservesCrewSequenceLegalityContractDetails";
            dt.Columns.Add("ReservesCrewSequenceLegalityContractDetailsID", typeof(Int64));
            dt.Columns.Add("ContractSectionsID", typeof(Int64));
            dt.Columns.Add("LegalityPhaseID", typeof(Int64));
            dt.Columns.Add("IsLegal", typeof(Boolean));
            dt.Columns.Add("ReservesCrewSequenceLegalityID", typeof(Int64));
            dt.Columns.Add("LanguageID", typeof(Int64));
            dt.Columns.Add("FosRAP", typeof(Int64));
            dt.Columns.Add("CalcRAP", typeof(Int64));
            dt.Columns.Add("IsCurrentRAP", typeof(Int64));
            dt.Columns.Add("IsOver35By7", typeof(Boolean));
            dt.Columns.Add("IsOver35By7LH", typeof(Boolean));
            ds.Tables.Add(dt);

            dt = new DataTable();
            dt.TableName = "ROTD.LegalityPostQLAStatus";
            dt.Columns.Add("LegalityPostQLAStatusID", typeof(Int64));
            dt.Columns.Add("LegalityQLADetailsID", typeof(Int64));
            dt.Columns.Add("PostQLAStateID", typeof(Int64));
            dt.Columns.Add("ContractSectionsID", typeof(Int64));
            dt.Columns.Add("LegalityPhaseID", typeof(Int64));
            dt.Columns.Add("RuleID", typeof(Int64));
            dt.Columns.Add("RuleName", typeof(string));
            ds.Tables.Add(dt);


        }

        public void CreateROTDRCSTableColumns()
        {
            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "ROTD.RCS";
            dt.Columns.Add("ReservesCrewSequenceLegalityContractDetailsID", typeof(Int64));
            dt.Columns.Add("LegalityQLADetailsID", typeof(Int64));
            dt.Columns.Add("RCSID", typeof(Int32));
            ds.Tables.Add(dt);
        }

        public void CreateROTDDropActivityDetailsTableColumns()
        {
            dt = new DataTable();
            dt.TableName = "ROTD.DropActivityDetails";
            dt.Columns.Add("DropActivityDetailsID", typeof(Int64));
            dt.Columns.Add("ActivityCode", typeof(string));
            dt.Columns.Add("OriginationDate", typeof(DateTime));
            dt.Columns.Add("SequencePosition", typeof(string));
            dt.Columns.Add("OpenSequenceAwardID", typeof(Int64));
            dt.Columns.Add("ActivityStartDateTime", typeof(DateTime));
            dt.Columns.Add("DropActivityTypeID", typeof(Int64));
            dt.Columns.Add("ActivityEndDateTime", typeof(DateTime));
            dt.Columns.Add("ReservesCrewSequenceLegalityContractDetailsID", typeof(Int64));
            ds.Tables.Add(dt);
        }

        public void CreateROTDQLADataTableColumns()
        {
            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "ROTD.LegalityQLADetails";
            dt.Columns.Add("LegalityQLADetailsID", typeof(Int64));
            dt.Columns.Add("IsLegal", typeof(Boolean));
            dt.Columns.Add("IsContractual", typeof(Boolean));
            dt.Columns.Add("IsQualified", typeof(Boolean));
            dt.Columns.Add("Request", typeof(string));
            dt.Columns.Add("Response", typeof(string));
            dt.Columns.Add("CreatedBy", typeof(Int64));
            dt.Columns.Add("CreateDate", typeof(DateTime));
            dt.Columns.Add("UpdatedBy", typeof(Int64));
            dt.Columns.Add("UpdatedDate", typeof(DateTime));
            dt.Columns.Add("ReservesCrewSequenceLegalityID", typeof(Int64));
            dt.Columns.Add("LegalityPhaseID", typeof(Int32));
            dt.Columns.Add("FosRAP", typeof(Int64));
            dt.Columns.Add("IsCurrentRAP", typeof(Int64));
            dt.Columns.Add("LanguageID", typeof(Int32));
            ds.Tables.Add(dt);
        }

        public void CreateROTDLegalityQLARulesTableColumns()
        {
            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "ROTD.LegalityQLARules";
            dt.Columns.Add("LegalityQLARulesID", typeof(Int64));
            dt.Columns.Add("LegalityQLADetailsID", typeof(Int64));
            dt.Columns.Add("QLARuleID", typeof(Int64));
            dt.Columns.Add("Result", typeof(string));
            dt.Columns.Add("Message", typeof(string));
            dt.Columns.Add("QLARuleName", typeof(string));
            dt.Columns.Add("ETLUpdateDateTime", typeof(DateTime));
            dt.Columns.Add("SequenceID", typeof(Int64));
            ds.Tables.Add(dt);
        }

        public void CreateROTDLegalityQLASupportingDataTableColumns()
        {
            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "ROTD.LegalityQLASupportingData";
            dt.Columns.Add("LegalityQLASupportingDataID", typeof(Int64));
            dt.Columns.Add("LegalityQLARulesID", typeof(Int64));
            dt.Columns.Add("RunId", typeof(Int64));
            dt.Columns.Add("EmployeeId", typeof(Int64));
            dt.Columns.Add("AffectedType", typeof(string));
            dt.Columns.Add("ContractMonth", typeof(string));
            dt.Columns.Add("ActivityId", typeof(Int32));
            dt.Columns.Add("ActivityCode", typeof(string));
            dt.Columns.Add("ActivityType", typeof(string));
            dt.Columns.Add("ActivityOriginationDate", typeof(string));
            dt.Columns.Add("PositionCode", typeof(string));
            dt.Columns.Add("PickupContractMonth", typeof(string));
            dt.Columns.Add("PickupActivityId", typeof(Int32));
            dt.Columns.Add("PickupActivityCode", typeof(string));
            dt.Columns.Add("PickupActivityType", typeof(string));
            dt.Columns.Add("PickupActivityOriginationDate", typeof(string));
            dt.Columns.Add("PickupPositionCode", typeof(string));
            dt.Columns.Add("StartDateTime", typeof(string));
            dt.Columns.Add("EndDateTime", typeof(string));
            dt.Columns.Add("PickupStartDateTime", typeof(string));
            dt.Columns.Add("PickupEndDateTime", typeof(string));
            dt.Columns.Add("ActivityReportDateTime", typeof(DateTime));
            ds.Tables.Add(dt);
        }

        public static void perofrmBulkCopy(string connection, DataSet ds)
        {
            using (SqlBulkCopy bulkcopy = new SqlBulkCopy(connection, SqlBulkCopyOptions.TableLock))
            {
                bulkcopy.BatchSize = 100000;
                bulkcopy.BulkCopyTimeout = 0;
                foreach (DataTable dt in ds.Tables)
                {
                    bulkcopy.DestinationTableName = dt.TableName;
                    bulkcopy.WriteToServer(dt);
                }
            }
        }

        public void CreateQLADataTableColumns()
        {
            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "LegalityQLADetails";
            dt.Columns.Add("LegalityQLADetailsID", typeof(Int64));
            dt.Columns.Add("ReservesCrewSequenceLegalityID", typeof(Int64));
            dt.Columns.Add("IsLegal", typeof(Boolean));
            dt.Columns.Add("IsContractual", typeof(Boolean));
            dt.Columns.Add("IsQualified", typeof(Boolean));
            dt.Columns.Add("Request", typeof(string));
            dt.Columns.Add("Response", typeof(string));
            dt.Columns.Add("CreatedBy", typeof(Int64));
            dt.Columns.Add("CreateDate", typeof(DateTime));
            dt.Columns.Add("UpdatedBy", typeof(Int64));
            dt.Columns.Add("UpdatedDate", typeof(DateTime));
            ds.Tables.Add(dt);

            dt = new DataTable();
            dt.TableName = "LegalityQLARules";
            dt.Columns.Add("LegalityQLARulesID", typeof(Int64));
            dt.Columns.Add("LegalityQLADetailsID", typeof(Int64));
            dt.Columns.Add("QLARuleID", typeof(Int64));
            dt.Columns.Add("Result", typeof(string));
            dt.Columns.Add("Message", typeof(string));
            ds.Tables.Add(dt);
        }

        public void CreateROTDLegalityQLAMessageTableColumns()
        {
            ds = new DataSet();
            dt = new DataTable();
            dt.TableName = "ROTD.LegalityQLAMessage";
            dt.Columns.Add("LegalityQLAMessageID", typeof(Int64));
            dt.Columns.Add("RunID", typeof(Int64));
            dt.Columns.Add("QLARequest", typeof(string));
            dt.Columns.Add("QLAResponse", typeof(string));
            dt.Columns.Add("CreatedBy", typeof(Int64));
            dt.Columns.Add("CreatedDate", typeof(DateTime));
            dt.Columns.Add("LegalityPhaseID", typeof(Int64));
            ds.Tables.Add(dt);
        }

        public async Task<List<FALanguageDetails>> getLegalityReservesCrewMemberLanguageDetails(long runId)
        {
            MapperBase<List<LegalityReservesCrewMemberLanguageDetailsDTO>, List<FALanguageDetails>> languageDetailssMapper = new LegalityReservesCrewMemberLanguageDetailsMapper();
            List<LegalityReservesCrewMemberLanguageDetailsDTO> languageDetailsDto = new List<LegalityReservesCrewMemberLanguageDetailsDTO>();
            List<FALanguageDetails> languageDetails = new List<FALanguageDetails>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                languageDetailsDto = await this.Adaptor.getLegalityReservesCrewMemberLanguageDetails(runId);
                if (languageDetailsDto != null)
                {
                    languageDetails = languageDetailssMapper.Map(languageDetailsDto);
                }

            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return languageDetails;
        }

        public async Task<List<LegalityQLADetail>> GetReservesLegalityQLAList(long runId, int phaseId)
        {
            MapperBase<List<ReservesLegalityQLAListDTO>, List<LegalityQLADetail>> reservesLegalityQLAListMapper = new ReservesLegalityQLAListMapper();
            List<ReservesLegalityQLAListDTO> reservesLegalityQLAListDto = new List<ReservesLegalityQLAListDTO>();
            List<LegalityQLADetail> reservesLegalityQLAList = new List<LegalityQLADetail>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();
                //Get the results 
                reservesLegalityQLAListDto = await this.Adaptor.GetReservesLegalityQLAList(runId, phaseId);
                if (reservesLegalityQLAListDto != null)
                {
                    reservesLegalityQLAList = reservesLegalityQLAListMapper.Map(reservesLegalityQLAListDto);
                }

            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return reservesLegalityQLAList;
        }
        private void LogMemoryUsage(string location)
        {
            long memoryBytes = GC.GetTotalMemory(forceFullCollection: false);
            double memoryMB = memoryBytes / (1024.0 * 1024.0);
            Logs.LogInformation($"[Memory] {location}: {memoryMB:F2} MB used.");
        }
    }
    public class QLAResponseQLADetail
    {
        public string RequestId { get; set; }
        public bool IsLegal { get; set; }
        public bool IsContractual { get; set; }
        public bool IsQualified { get; set; }
        public int IsCurrentRAP { get; set; }
        public bool IscurrentFA { get; set; }
        public long ruleIdentity { get; set; }
        public string EmployeeID { get; set; }
        public string ActivityCode { get; set; }
        public long ActivityID { get; set; }
        public int ReservesCrewSequenceLegalityID { get; set; }
        public int LanguageID { get; set; }
        public int FOSRAP { get; set; }
        public int CalcRAP { get; set; }
    }
    public class SimplifiedContractResponse
    {
        public Int64 ReservesCrewSequenceLegalityID { get; set; }
        public long QLADetailsID { get; set; }
        public int FOSRAP { get; set; }
        public int CalcRAP { get; set; }
        public int IsCurrentRAP { get; set; }
        public int? LanguageID { get; set; }
        public long ContractSectionId { get; set; }
        public bool IsLegal { get; set; }
        public List<ROTDRuleDetails> RuleDetails { get; set; } 
        public string EmployeeNo { get; set; }
        public long ActivityID { get; set; }
        public string SequencePosition { get; set; }
        public DateTime SequenceOriginationDate { get; set; }
    }
}
