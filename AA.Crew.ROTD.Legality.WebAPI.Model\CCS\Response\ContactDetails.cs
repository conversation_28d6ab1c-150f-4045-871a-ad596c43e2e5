﻿using System.Collections.Generic;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class ContactDetails
    {

        /// <summary>
        ///Home Phone Number 
        /// </summary>
        public string phoneHome { get; set; }

        /// <summary>
        /// Business Phone Number 
        /// </summary>
        public string phoneBusiness { get; set; }

        /// <summary>
        ///Temporary Phone Number 
        /// </summary>
        public string phoneTemp { get; set; }

        /// <summary>
        ///Other Phone Number 
        /// </summary>
        public string phoneOther { get; set; }

        /// <summary>
        /// Emergency Phone Number 
        /// </summary>
        public string emergencyPhone { get; set; }

        /// <summary>
        /// Home to Airport time 
        /// </summary>
        public SortedDictionary<string, int> homeToAirportTravelTimes { get; set; }

        public ContactDetails()
        {
            homeToAirportTravelTimes = new SortedDictionary<string, int>();
        }
    }
}
