using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class ContractSectionsMapper : MapperBase<List<ContractSectionsDTO>, List<ContractSection>>
    {
        public override List<ContractSection> Map(List<ContractSectionsDTO> contractSectionsDtoList)
        {
            try
            {
                return contractSectionsDtoList.Select(contractSectionsDto => new ContractSection
                {
                    ContractSectionsID = contractSectionsDto.ContractSectionsID,
                    ContractSection1 = contractSectionsDto.ContractSection,
                    DisplayName = contractSectionsDto.DisplayName,
                    ReportType = contractSectionsDto.ReportType,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<ContractSectionsDTO> Map(List<ContractSection> element)
        {
            throw new NotImplementedException();
        }
    }
}
