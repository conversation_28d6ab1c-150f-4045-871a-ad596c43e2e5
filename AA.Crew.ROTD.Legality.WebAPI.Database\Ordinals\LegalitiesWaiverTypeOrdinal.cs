using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalitiesWaiverTypeOrdinal
    {
        /* Oridinal variables */

        internal Int32 QLARule;
        internal Int32 WaiverTypeDescription;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.QLARule = sqlDataReader.GetOrdinal("QLARule");
            this.WaiverTypeDescription = sqlDataReader.GetOrdinal("WaiverTypeDescription");


            this.Initialized = true;
        }
    }
}
