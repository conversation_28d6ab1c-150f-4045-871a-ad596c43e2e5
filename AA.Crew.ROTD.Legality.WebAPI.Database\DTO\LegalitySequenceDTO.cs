﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class LegalitySequenceDTO
    {
        public long SequenceID { get; set; }
        public long RunID { get; set; }
        public Nullable<int> SequenceNumber { get; set; }
        public long SequencePositionDetailsID { get; set; }
        public string SequencePosition { get; set; }
        public Nullable<System.DateTime> OpenTime { get; set; }
        public Nullable<long> ExcludeReasonID { get; set; }
        public Nullable<System.DateTime> SequenceStartDateTime { get; set; }
        public Nullable<int> DurationInDays { get; set; }
        public Nullable<int> TotalCreditCurrentMonth { get; set; }
        public Nullable<System.DateTime> SequenceEndDateTime { get; set; }
        public Nullable<int> TotalDutyPeriod { get; set; }
        public string CoTerminalStation { get; set; }
        public string SatelliteStation { get; set; }
        public Nullable<System.DateTime> OriginationDate { get; set; }
        public Nullable<int> TotalCreditNextMonth { get; set; }
        public Nullable<System.DateTime> SequenceDepartureDateTime { get; set; }
        public string LayOverStations { get; set; }
        public string LegsPerDutyPeriod { get; set; }
        public Nullable<bool> MultipleEquipments { get; set; }
        public string EquipmentGroup { get; set; }
    }
}
