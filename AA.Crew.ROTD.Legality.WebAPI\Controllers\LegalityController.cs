﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.Requests;
using AA.Crew.ROTD.Legality.WebAPI.Model.Response;
using Microsoft.AspNetCore.Mvc;
using AA.Crew.ROMS.LogHelper.Helper;
using System.Text;
using AA.Crew.ROTD.Legality.WebAPI.Business.Operations;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace AA.Crew.ROTD.Legality.WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LegalityController : BaseController
    {
        private readonly ILogger<LegalityController> _logger;
        //private readonly IDataLoader _dataLoader;
        private readonly IRuleOrchestrator _ruleOrchestrator;
        List<AppSettings> _appSettings;

        public LegalityController(ILogger<LegalityController> logger, IRuleOrchestrator ruleOrchestrator, List<AppSettings> appSettings)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _ruleOrchestrator = ruleOrchestrator ?? throw new ArgumentNullException(nameof(ruleOrchestrator));
            _appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        // GET: api/<LegalityController>
        // POST api/<LegalityController>
        [HttpPost]
        public async Task<IActionResult> Post([FromBody] LegalityRequest request)
        {
            string logTitle = "LegalityController.Post()";
            _logger.LogInformation("Begin of Method() {request}", LogSanitizer.SerializeSafely(request));
            try
            {
                //validateRequest
                #region ValidateRequest
                var validationMsg = new StringBuilder();
                bool hasValidationError = false;
                if (request == null)
                {
                    validationMsg.AppendLine(" request value cannot be empty");
                    hasValidationError = true;
                }
                if (hasValidationError)
                {
                    _logger.LogWarning(string.Join(",", validationMsg));
                }
                #endregion ValidateRequest

                #region ProcessRequest
                //Task.Run(() =>
                //{
                //    _ruleOrchestrator.ROTDLegalityLoadData(request.runID, request.runContextID, request.awardProgressStepID);
                //    return Task.CompletedTask;
                //}).Forget();

                Thread workThread = new Thread(() =>
                {
                    try
                    {
                        _ruleOrchestrator.ROTDLegalityLoadData(request.runID, request.runContextID, request.awardProgressStepID);
                        _ruleOrchestrator.ForceGarbageCollection();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "AA.Crew.ROTD.Legality.WebAPI.Controllers.LegalityController - Post - Exception in thread processing Legality: {0}", ex.Message);
                    }
                });
                workThread.Start();
                _logger.LogInformation("Thread initiated to process Legality");
                //_logger.LogInformation("End of Method() {response} ", phase);
                //return BuildActionResult(response);
                //_ruleOrchestrator.ROTDLegalityLoadData(request.runID, request.runContextID, request.awardProgressStepID);
                //_ruleOrchestrator.ForceGarbageCollection();

                #endregion ProcessRequest
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in executing TestMethod() {0},{1},{2}", ex.Message, ex.InnerException?.Message, ex.StackTrace);
                //return HandleException(response, ex, logTitle);
                //throw ex;
                return Problem("Problem in Loading Ddata");
            }
        }
    }
}
