﻿//using AA.Crew.ConfigurationProvider.Json;
//using AA.Crew.ConnectionString.Provider.Json;
//using AA.Crew.ROTD.Legality.WebAPI.ServiceClient;
using AA.Crew.WebClient.Implementation;
using Castle.Core.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests
{
    public static class ConfigFactory
    {
        public static string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != null ? "." + Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") : "";

        private static IConfigurationRoot GetConfigurationRoot()
        {
            var config = new ConfigurationBuilder()
                .AddJsonFile($"appsettings{environment}.json", optional: true, reloadOnChange: true)
                .Build();
            return config;
        }

        //public static IServiceCallFactory GetSetupConfigurationLiveService()
        //{
        //    var config = GetConfigurationRoot();
        //    var jsonConnectionString = new JsonProvider(config);
        //    var jsonProvider = new AA.Crew.ConfigurationProvider.Json.ConfigurationProvider(config, jsonConnectionString);
        //    var loggerMock = new Mock<ILogger<ServiceCallFactory>>();
        //    return new ServiceCallFactory(jsonProvider, new ResourceHandlerProvider(), loggerMock.Object);
        //}

        //public static AA.Crew.ConfigurationProvider.Interface.IConfigurationProvider GetIConfigurationProvider()
        //{
        //    var config = GetConfigurationRoot();
        //    var jsonConnectionString = new JsonProvider(config);
        //    return new AA.Crew.ConfigurationProvider.Json.ConfigurationProvider(config, jsonConnectionString);
        //}


        public static IConfigurationSection GetConfigurationSection(string section)
        {
            var config = GetConfigurationRoot();
            return config.GetSection(section);
        }
    }
}
