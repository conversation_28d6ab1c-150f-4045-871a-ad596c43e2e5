﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class LegalityROTDPhaseDetailDTO
    {
        public Nullable<long> LegalityPhaseId { get; set; }
        public long ReservesCrewSequenceLegalityID { get; set; }
        public long LegalityQLADetailsID { get; set; }
        public long QLARuleID { get; set; }
        public string QLARuleName { get; set; }
        public long ReservesCrewMemberID { get; set; }
        public long SequenceID { get; set; }
        public int SequenceNumber { get; set; }
        public string SequencePosition { get; set; }
        public int StandbyID { get; set; }
        public string FosRAP { get; set; }
        public string CalcRAP { get; set; }
        public int LanguageID { get; set; }
        public long ContractSectionsID { get; set; }
        public string ContractSection { get; set; }
        public long PostQLAStateID { get; set; }
        public string PostQLAState { get; set; }
        public bool IsLegal { get; set; }
    }
}
