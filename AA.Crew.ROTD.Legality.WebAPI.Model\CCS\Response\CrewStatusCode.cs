﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class CrewStatusCode
    {
        public string prtCode { get; set; }
        public string reasonCode { get; set; }
        public string indicator1 { get; set; }
        public string indicator2 { get; set; }
        public string indicator3 { get; set; }
        public string groupReasonCode { get; set; }
        public DateTime sourceTimeStamp { get; set; }
        public int currentCredit { get; set; }
        public int nextCredit { get; set; }
        public CrewStatusCodesKey crewStatusCodesKey { get; set; }

        //_isCreditedForCurrentMonth and _isCreditedForNextMonth and IsCreditedByBitIndex() are not a part of corresponsing CSS entity
        //Being added for RPV only.
        private bool? _isCreditedForCurrentMonth;
        private bool? _isCreditedForNextMonth;
        private bool? _isPlannedAbsenceCode;

        public bool isCreditedForCurrentMonth
        {
            get
            {
                if (!_isCreditedForCurrentMonth.HasValue)
                    _isCreditedForCurrentMonth = IsCreditedByBitIndex(3);
                return _isCreditedForCurrentMonth.Value;
            }
        }
        public bool isCreditedForNextMonth
        {
            get
            {
                if (!_isCreditedForNextMonth.HasValue)
                    _isCreditedForNextMonth = IsCreditedByBitIndex(7);
                return _isCreditedForNextMonth.Value;
            }
        }
        public bool isPlannedAbsenceCode
        {
            get
            {
                if (!_isPlannedAbsenceCode.HasValue)
                    _isPlannedAbsenceCode = IsPlannedAbsenceCodeByBitIndex(0);
                return _isPlannedAbsenceCode.Value;
            }
        }

        public bool IsCreditedByBitIndex(short bitIndex)
        {
            if (string.IsNullOrWhiteSpace(indicator2))
                return false;
            string binaryVal = HexToBin(indicator2.ToCharArray());
            char isCredited = binaryVal.ElementAtOrDefault(bitIndex);
            return isCredited.Equals('1');
        }

        public bool IsPlannedAbsenceCodeByBitIndex(short bitIndex)
        {
            if (string.IsNullOrWhiteSpace(indicator1))
                return false;
            string binaryVal = HexToBin(indicator1.ToCharArray());
            char isPlannedAbsenceCode = binaryVal.ElementAtOrDefault(bitIndex);
            return isPlannedAbsenceCode.Equals('1');
        }

        private string HexToBin(char[] hexdec)
        {
            int i = 0;
            int len = hexdec.Length;
            StringBuilder binaryString = new StringBuilder();
            //while (hexdec[i] != '\u0000')
            while (i < len)
            {

                switch (hexdec[i])
                {
                    case '0':
                        binaryString.Append("0000");
                        break;
                    case '1':
                        binaryString.Append("0001");
                        break;
                    case '2':
                        binaryString.Append("0010");
                        break;
                    case '3':
                        binaryString.Append("0011");
                        break;
                    case '4':
                        binaryString.Append("0100");
                        break;
                    case '5':
                        binaryString.Append("0101");
                        break;
                    case '6':
                        binaryString.Append("0110");
                        break;
                    case '7':
                        binaryString.Append("0111");
                        break;
                    case '8':
                        binaryString.Append("1000");
                        break;
                    case '9':
                        binaryString.Append("1001");
                        break;
                    case 'A':
                    case 'a':
                        binaryString.Append("1010");
                        break;
                    case 'B':
                    case 'b':
                        binaryString.Append("1011");
                        break;
                    case 'C':
                    case 'c':
                        binaryString.Append("1100");
                        break;
                    case 'D':
                    case 'd':
                        binaryString.Append("1101");
                        break;
                    case 'E':
                    case 'e':
                        binaryString.Append("1110");
                        break;
                    case 'F':
                    case 'f':
                        binaryString.Append("1111");
                        break;
                    default:
                        throw new Exception("Invalid hexadecimal digit " + hexdec[i]);
                }
                i++;
            }

            return binaryString.ToString();
        }
    }
}
