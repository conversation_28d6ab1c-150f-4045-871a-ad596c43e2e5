using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class LegalityQLASupportingDataNonVolunteerStandByDTO
    {
        public long RunId { get; set; }
        public long EmployeeId { get; set; }
        public string AffectedType { get; set; }
        public string ContractMonth { get; set; }
        public int ActivityId { get; set; }
        public string Activitycode { get; set; }
        public string ActivityType { get; set; }
        public string ActivityOriginationDate { get; set; }
        public string PositionCode { get; set; }
        public string PickupContractMonth { get; set; }
        public int PickupActivityId { get; set; }
        public string PickupActivityCode { get; set; }
        public string PickupActivityType { get; set; }
        public string PickupActivityOriginationDate { get; set; }
        public string PickupPositionCode { get; set; }
        public DateTime ActivityReportDateTime { get; set; }

    }
}
