using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.DBEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class AllContextualMappedRulesMapper : MapperBase<List<AllContextualMappedRulesDTO>, List<ROTDContextualRule>>
    {
        public override List<ROTDContextualRule> Map(List<AllContextualMappedRulesDTO> allContextualMappedRulesDtoList)
        {
            try
            {
                return allContextualMappedRulesDtoList.Select(allContextualMappedRulesDto => new ROTDContextualRule
                {
                    RuleTypeID = allContextualMappedRulesDto.RuleTypeID,
                    RuleTypeName = allContextualMappedRulesDto.RuleTypeName,
                    RuleID = allContextualMappedRulesDto.RuleID,
                    RuleName = allContextualMappedRulesDto.RuleName,
                    RuleClass = allContextualMappedRulesDto.RuleClass,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<AllContextualMappedRulesDTO> Map(List<ROTDContextualRule> element)
        {
            throw new NotImplementedException();
        }
    }
}
