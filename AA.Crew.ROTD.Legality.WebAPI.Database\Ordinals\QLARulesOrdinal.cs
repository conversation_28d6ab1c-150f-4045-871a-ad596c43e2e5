using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct QLARulesOrdinal
    {
        /* Oridinal variables */

        internal Int32 QLARuleID;
        internal Int32 QLARule;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.QLARuleID = sqlDataReader.GetOrdinal("QLARuleID");
            this.QLARule = sqlDataReader.GetOrdinal("QLARule");


            this.Initialized = true;
        }
    }
}
