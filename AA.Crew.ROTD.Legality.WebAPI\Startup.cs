﻿using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Business;
using AA.Crew.WebClient.Implementation;
using AA.Crew.WebClient.Interface;
using Microsoft.AspNetCore.Builder;
using Microsoft.OpenApi.Models;
using Microsoft.Extensions.Configuration;
using System.Text.Json.Serialization;
using System.Text;
using AA.Crew.ROMS.Client;
using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Database.Factories;
using AA.Crew.ROTD.Legality.WebAPI.Service.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Service;
using AA.Crew.Reserves.QLA.Client;
using IServiceCallFactory = AA.Crew.ROMS.Client.IServiceCallFactory;
using ServiceCallFactory = AA.Crew.ROMS.Client.ServiceCallFactory;
using AA.Crew.Legalities.ROTD;
using AA.Crew.Reserves.TimeConversion;

namespace AA.Crew.ROTD.Legality.WebAPI
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var env = String.Empty;
            var appSettings_Data = String.Empty;
            try
            {
                env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                Console.WriteLine("Fetching Environment Variable ASPNETCORE_ENVIRONMENT =" + env);

                string certPassword = Environment.GetEnvironmentVariable("CERTIFICATE_PASSWORD");
                var certificateName = Environment.GetEnvironmentVariable("CERTIFICATE_NAME");
                var certificateData = Environment.GetEnvironmentVariable("CERTIFICATE_DATA"); // PFx File Encoded as Base 64 String
                appSettings_Data = Environment.GetEnvironmentVariable("APPSETTINGS_DATA");

#if DEBUG
                Console.WriteLine("CERTIFICATE_PASSWORD: " + certPassword);
                Console.WriteLine("CERTIFICATE_NAME: " + certificateName);
                Console.WriteLine("CERTIFICATE_DATA: " + certificateData);
                Console.WriteLine("APPSETTINGS_DATA:" + appSettings_Data);

#endif
                Console.WriteLine("Downloading Certificate for CCS - Start");
                Byte[] rawCertFile = Convert.FromBase64String(certificateData);
                File.WriteAllBytes(certificateName, rawCertFile);
                Console.WriteLine("Downloading Certificate for CCS - End");
            }
            catch (Exception ex)
            {
                Console.WriteLine("Problem Downloading Certificate for CCS. Message: " + ex.Message);
            }
            #region PrintConfigs

            PrintFilesByExtension("*.json");
            PrintFilesByExtension("*.pfx");

            #endregion PrintConfigs

            #region App Settings


            var config = new ConfigurationBuilder()
                      .SetBasePath(Directory.GetCurrentDirectory())
                      .AddJsonStream(new MemoryStream(Encoding.ASCII.GetBytes(appSettings_Data)))
                      //.AddJsonFile($"appsettings." + env + ".json")
                      //.AddJsonStream(new MemoryStream(Encoding.ASCII.GetBytes(connectionString)))
                      .Build();

            var AppSettingsSection = config.GetSection("Values").Get<Dictionary<string, string>>();
            List<AppSettings> appSettings = AppSettingsSection != null ? AppSettingsSection.Select(p => new AppSettings { SettingName = p.Key, SettingValues = p.Value }).ToList() : new List<AppSettings>();
            #endregion

            services.AddControllers().AddJsonOptions(x =>
            {
                // serialize enums as strings in api responses
                x.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            }); ;
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "AA.Crew.ROTD.Legality.WebAPI", Version = "v1" });
            });
            services.AddSingleton<AA.Crew.ConfigurationProvider.Interface.IConfigurationProvider>((x) =>
            {
                return new AA.Crew.ConfigurationProvider.Json.ConfigurationProvider(config,
                    new AA.Crew.ConnectionString.Provider.Json.JsonProvider(config));
            });

            services.AddScoped<IDataProviderFactory, DataProviderFactory>();
            services.AddScoped<IDataAdaptorFactory>((s) => { return new DataAdaptorFactory(config.GetConnectionString("FA_ROMS")); });
            services.AddScoped<IResourceHandlerProvider, ResourceHandlerProvider>();
            services.AddScoped<IServiceCallFactory, ServiceCallFactory>();
            services.AddScoped<AA.Crew.ROMS.ROTA.WebApi.Client.IServiceCallFactory, AA.Crew.ROMS.ROTA.WebApi.Client.ServiceCallFactory>();
            services.AddScoped<IQLAServiceCallFactory, QLAServiceCallFactory>();
            services.AddScoped<IROTDProcessor, ROTDProcessor>();

            //services.AddScoped<IReservesCCSDataProvider, ReservesCCSDataProvider>();
            //services.AddScoped<IDataLoader, DataLoader>();
            services.AddScoped<ILegalityServiceProvider, LegalityServiceProvider>();
            services.AddScoped<IRuleOrchestrator, RuleOrchestrator>();
            services.AddScoped<ICrewDataServiceProvider, CrewDataServiceProvider>();
            services.AddSingleton<List<AppSettings>>(appSettings);
            services.AddScoped<ITimeConversion, TimeConversion>();
            services.AddScoped<IRotaWebApiProvider, RotaWebApiProvider>();

            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
        }

        void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            if (exception != null)
            {
                Console.WriteLine($"BackendAPI - Fatal Unhandled exception: {exception.Message}");
                // You can also log the stack trace or other details if needed
                Console.WriteLine($"Stack Trace: {exception.StackTrace}");
            }
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            bool isDebugInLocal = false;
#if DEBUG
            isDebugInLocal = true;
#endif

            if (env.IsDevelopment() || isDebugInLocal)
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "AA.Crew.ROTD.Legality.WebAPI v1"));
            }

            //app.UseHttpsRedirection();

            app.UseRouting();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }

        private void PrintFilesByExtension(string extension)
        {
            Console.WriteLine($"-----Printing fetched {extension} files-----");
            foreach (var fileName in Directory.GetFiles(Directory.GetCurrentDirectory(), extension))
            {
                Console.WriteLine(fileName);
            }
            Console.WriteLine($"-----End of printing {extension} files-----");
        }
    }
}
