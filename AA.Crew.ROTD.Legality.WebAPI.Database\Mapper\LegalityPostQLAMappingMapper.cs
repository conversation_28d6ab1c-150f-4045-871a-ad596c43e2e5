using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityPostQLAMappingMapper : MapperBase<List<LegalityPostQLAMappingDTO>, List<PostQLAMappingDetails>>
    {
        public override List<PostQLAMappingDetails> Map(List<LegalityPostQLAMappingDTO> legalityPostQLAMappingDtoList)
        {
            try
            {
                return legalityPostQLAMappingDtoList.Select(legalityPostQLAMappingDto => new PostQLAMappingDetails
                {
                    PostQLAMappingID = legalityPostQLAMappingDto.PostQLAMappingID,
                    PostQLAStateID = legalityPostQLAMappingDto.PostQLAStateID,
                    QLARuleID = legalityPostQLAMappingDto.QLARuleID,
                    ContractSectionsID = legalityPostQLAMappingDto.ContractSectionsID,
                    ContractSection = legalityPostQLAMappingDto.ContractSection,
                    LegalityPhaseID = legalityPostQLAMappingDto.LegalityPhaseID,
                    QLARule = legalityPostQLAMappingDto.QLARule,
                    PostQLAState = legalityPostQLAMappingDto.PostQLAState,
                    WaiverTypeID = legalityPostQLAMappingDto.WaiverTypeID,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityPostQLAMappingDTO> Map(List<PostQLAMappingDetails> element)
        {
            throw new NotImplementedException();
        }
    }
}
