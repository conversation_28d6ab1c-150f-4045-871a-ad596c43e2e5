﻿using System;
using System.Text;

namespace AA.Crew.Legalities.ROTD
{
    public static class LogExceptionExtension
    {
        /// <summary>
        /// Returns the exception as a Loggable string that contains the Stack, inner exceptions, and Data from the exception.
        /// </summary>       
        public static string LoggableString(this Exception exception)
        {
            StringBuilder sb = new StringBuilder();
            exception.ExceptionString(sb);
            return sb.ToString();
        }

        /// <summary>
        /// Populates a String build parameter with the details from the current exception
        /// </summary>
        /// <param name="exception"></param>
        /// <param name="sbExcp"></param>
        private static void ExceptionString(this Exception exception, StringBuilder sbExcp)
        {
            sbExcp.AppendFormat("{0}", exception.ToString());          

            if (exception.Data.Count > 0)
            {
                sbExcp.Append("\tException Data:{");
                foreach (System.Collections.DictionaryEntry entry in exception.Data)
                {
                    sbExcp.AppendFormat("{0}:{1};", entry.Key, entry.Value);
                }
                sbExcp.Append("}");
            }

            if (exception.InnerException != null)
            {
                sbExcp.Append("\tInner Exception:");
                exception.InnerException.ExceptionString(sbExcp);
            }
        }
    }
}
