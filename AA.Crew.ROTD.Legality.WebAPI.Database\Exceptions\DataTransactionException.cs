﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Exceptions
{
    public class DataTransactionException : DataProviderException
    {
        /// <summary>
        /// Represents a failure attempting to connect to the Database
        /// </summary>
        public DataTransactionException(String message, Exception innerException) : base(message, innerException)
        {

        }
    }
}
