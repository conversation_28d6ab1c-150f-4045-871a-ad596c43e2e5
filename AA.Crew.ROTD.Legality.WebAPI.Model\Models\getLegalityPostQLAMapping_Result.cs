using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityPostQLAMapping_Result
    {
        public long PostQLAMappingID { get; set; }
        public Nullable<long> PostQLAStateID { get; set; }
        public Nullable<long> QLARuleID { get; set; }
        public Nullable<long> ContractSectionsID { get; set; }
        public string ContractSection { get; set; }
        public Nullable<long> LegalityPhaseID { get; set; }
        public string QLARule { get; set; }
        public string PostQLAState { get; set; }
        public Nullable<int> WaiverTypeID { get; set; }
    }
}
