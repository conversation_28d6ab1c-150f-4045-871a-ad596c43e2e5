using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections;
using System.Linq;
using System.Reflection;
using System.Text;

namespace AA.Crew.ROTD.Legality.WebAPI.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        [HttpGet("/Ping")]
        public string Ping()
        {
            _logger.LogInformation("Ping()");
            return "Hello from API. " + DateTime.Now.ToString();
        }

        [HttpGet("/HealthCheck")]
        public JsonResult HealthCheck()
        {
            var typesAvbl = GetAllNamespaces();

            var data = new { AppStatus = "UP", UTCTimeNow = DateTime.UtcNow.ToString(), Application = "AA.Crew.ROTD.Legality.WebAPI", ApplicationTypes = typesAvbl };
            return Json(data);
        }

        private string GetAllNamespaces()
        {
            StringBuilder sb = new StringBuilder();
            try
            {
                sb.AppendLine("Printing Namespaces");

                //var arr = Assembly.GetExecutingAssembly().GetTypes().Select(x => x.Namespace).Distinct().ToArray();
                var arr = AppDomain.CurrentDomain.GetAssemblies().Select(x => x.FullName);
                foreach (var item in arr)
                {
                    sb.AppendLine(item);
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine("Error fetching assembly names. Message: " + ex.Message);
            }
            return sb.ToString();

        }

        [HttpGet("/EnvironmentName")]
        public string GetEnvironmentName()
        {
            var environmentVariable = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            return environmentVariable;
        }

        [HttpGet("/AppVersion")]
        public string AppVersion()
        {
            return GetType().Assembly.GetName().Version.ToString();
        }

        private string GetAllEnvironmentVariables()
        {
            StringBuilder sb = new StringBuilder();
            try
            {
                sb.AppendLine("Printing Environment variables");
                foreach (DictionaryEntry de in Environment.GetEnvironmentVariables())
                    sb.AppendLine(string.Format(" {0} = {1}", de.Key, de.Value));

            }
            catch (Exception ex)
            {

                sb.AppendLine("Error fetching environment variables. Message: " + ex.Message);
            }
            return sb.ToString();
        }

    }
}