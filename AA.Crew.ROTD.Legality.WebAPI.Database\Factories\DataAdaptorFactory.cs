using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor;
using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace AA.Crew.ROTD.Legality.WebAPI.Database.Factories
{
    public class DataAdaptorFactory : IDataAdaptorFactory
    {
        string connectionString;
        public DataAdaptorFactory(string connectionString)
        {
            this.connectionString = connectionString;
        }

        public string ConnectionString()
        {
            return this.connectionString;
        }

        #region AppSettings
        public IAppSettingsDataAdaptor GetAppSettingsDataAdaptor()
        {
            return new AppSettingsDataAdaptor(connectionString);
        }
        #endregion AppSettings

        #region InterpretiveData
        public IInterpretiveDataAdaptor GetInterpretiveDataAdaptor()
        {
            return new InterpretiveDataAdaptor(connectionString);
        }

        public ILegalityInterpretiveDataAdaptor GetLegalityInterpretiveDataAdaptor()
        {
            return new LegalityInterpretiveDataAdaptor(connectionString);
        }
        #endregion InterpretiveData

    }
}
