using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityContextualRule
    {
        public LegalityContextualRule()
        {
            this.LegalityContextualRuleTypeMappings = new HashSet<LegalityContextualRuleTypeMapping>();
        }
    
        public long LegalityContextualRuleID { get; set; }
        public string LegalityContextualRuleName { get; set; }
        public string LegalityContextualRuleClass { get; set; }
        public string ApplicationType { get; set; }
        public string DisplayName { get; set; }
        public Nullable<bool> IsActive { get; set; }
        public string Phase { get; set; }
    
        public virtual ICollection<LegalityContextualRuleTypeMapping> LegalityContextualRuleTypeMappings { get; set; }
    }
}
