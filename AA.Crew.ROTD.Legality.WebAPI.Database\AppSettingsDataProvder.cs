﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Database.Factories;
using AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.Mapper;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using System;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Provider
{
    public class AppSettingsDataProvder : ProviderBase<IAppSettingsDataAdaptor>, IAppSettingsDataProvider
    {
        #region Properties
        public AppSetttingsMapper Mapper { get; set; }
        #endregion Properties
        #region Constructor
        public AppSettingsDataProvder(IDataAdaptorFactory factory) : base()
        {
            if (factory == null)
                throw new ArgumentNullException("factory", "IDataAdaptorFactory parameter is required");

            this.Adaptor = factory.GetAppSettingsDataAdaptor();
            this.Mapper = new AppSetttingsMapper();
        }
        #endregion Constructor

        /// <summary>
        /// Data Layer to get appsettings from the database table
        /// </summary>
        /// <param name="nameOfSettingToGet">Setting to retrieve</param>
        /// <returns>Mapped AppSettings Model </returns>
        public async Task<AppSettings> GetApplicationSetting(string nameOfSettingToGet)
        {
            AppSettingsDTO response = null;
            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 
                response = await this.Adaptor.GetAppSettingsDTO(nameOfSettingToGet);

            }
            catch (System.Data.Common.DbException ex)
            {
                throw new AA.Crew.ROTD.Legality.WebAPI.Database.Exceptions.DataProcessingException("Failure processing the database procedure.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing GetApplicationSetting()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return this.Mapper.Map(response);
        }
    }
}
