using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityContextualRuleType
    {
        public LegalityContextualRuleType()
        {
            this.LegalityContextualRuleTypeMappings = new HashSet<LegalityContextualRuleTypeMapping>();
        }
    
        public long LegalityContextualRuleTypeID { get; set; }
        public string LegalityContextualRuleTypeName { get; set; }
    
        public virtual ICollection<LegalityContextualRuleTypeMapping> LegalityContextualRuleTypeMappings { get; set; }
    }
}
