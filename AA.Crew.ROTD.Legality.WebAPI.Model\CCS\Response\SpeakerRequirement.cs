﻿using System.Collections.Generic;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class SpeakerRequirement
    {
        /// <summary>
        /// Position is speaker restricted. A speaker on this position will not be considered to fulfill sequence level language requirements.
        /// </summary>
        public bool posSpeakerRestricted { get; set; }


        /// <summary>
        /// If a position is open and the sequence level language requirement
        ///is not met, then this attribute provides the language which can
        ///be used to fill the position. But If a position is already assigned to
        ///a FA, then this attribute provides the language qualification that the
        ///FA on that specific position possess. To validate language requirment
        ///during sequence assignments, service consumers need to consider other key attributes
        ///like "totalMissingForCabin" and "toalMissingForSeq"
        /// </summary>
        public List<string> posLangQuals { get; set; }

        /// <summary>
        /// Number of speakers required for sequence
        /// </summary>
        public int totalRequiredForSeq { get; set; }

        /// <summary>
        /// Number of speakers missing for sequence
        /// </summary>
        public int totalMissingForSeq { get; set; }

        /// <summary>
        /// Number of speakers required in the cabin
        /// </summary>
        public int totalRequiredForCabin { get; set; }

        // @ApiModelProperty(notes = "Number of missing speakers in the cabin")
        public int totalMissingForCabin { get; set; }

        /// <summary>
        /// Provides details about speaker count and corresponding language required for a sequence.
        /// </summary>
        public List<LanguageRequirements> languageRequirements { get; set; }

        public SpeakerRequirement()
        {
            posLangQuals = new List<string>();
            languageRequirements = new List<LanguageRequirements>();
        }

    }
}
