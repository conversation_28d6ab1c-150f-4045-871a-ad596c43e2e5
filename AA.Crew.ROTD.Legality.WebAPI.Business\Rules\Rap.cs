﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    public class Rap : IROTDRules
    {
        private List<string> retRuleName;
        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faCurRap, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate baseDate, ref bool possibleIsLegal, ref string rapCode, ref int isCurrentRAP, List<QLARuleResult> qlaRuleResult, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleName = new List<string>();
            bool flag = true;

            //foreach (var faCurRap in flightAttendant.FACurrentRAPs)
            //{
            flag = true;
            if (faCurRap.StartDate.HasValue && faCurRap.EndDate.HasValue)
            {
                if (sequence != null)
                {
                    if ((sequence.SatelliteStation || !IsBaseCoTerminal))
                    {
                        if (sequence.SequenceStartDateTime < faCurRap.StartDate.Value.Add(TimeSpan.FromHours(2)) || (sequence.SequenceReportDateTime > faCurRap.EndDate.Value.Add(TimeSpan.FromHours(2))))
                        {
                            flag = false;
                        }
                    }
                    else if (IsBaseCoTerminal)
                    {
                        if (sequence.SequenceStartDateTime < faCurRap.StartDate.Value.Add(TimeSpan.FromHours(3)) || (sequence.SequenceReportDateTime > faCurRap.EndDate.Value.Add(TimeSpan.FromHours(2))))
                        {
                            flag = false;
                        }
                    }
                }
                else if (standby != null)
                {
                    if (!IsBaseCoTerminal)
                    {
                        if (standby.ReportTime < faCurRap.StartDate.Value.Add(TimeSpan.FromHours(2)) || (standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration)) > faCurRap.EndDate.Value.Add(TimeSpan.FromHours(2))))
                        {
                            flag = false;
                        }
                    }
                    else if (IsBaseCoTerminal)
                    {
                        if (standby.ReportTime < faCurRap.StartDate.Value.Add(TimeSpan.FromHours(3)) || (standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration)) > faCurRap.EndDate.Value.Add(TimeSpan.FromHours(2))))
                        {
                            flag = false;
                        }
                    }
                }

                //	ROTD is assigning sequence that reports between 00:00-01:59 on their FD day based on the 2 hour RAP window rule.
                //	Since the sequence originates on their FD the 2 hour RAP window rule should not be considered.
                //  “As an exception to G.2., a Reserve with a Flex Day following their RAP may only be assigned a sequence with a sign-in on or before 2359 HBT on the last day of Reserve.”
                if (sequence != null)
                {
                    if (IgnoreRAPPlus2HourRule(flightAttendant.AVLDaysWithFT, sequence.SequenceReportDateTime, qlaRuleResult, baseDate.ProcessingDate))
                    {
                        flag = false;
                    }
                }
            }


            if (!flag)
            {
                possibleIsLegal = false;
                retRuleName.Add(ruleName);
            }
            return retRuleName;
        }

        private bool IgnoreRAPPlus2HourRule(int avlDay, DateTime? seqStartDateTime, List<QLARuleResult> qlaRuleResult, DateTime processDate)
        {
            bool isTouchFD = false;
            bool isLegal = false;
            if (qlaRuleResult != null && qlaRuleResult.Count > 0)
            {
                foreach (QLARuleResult rule in qlaRuleResult)
                {
                    if (rule.Rule.Equals(QLARuleConst.TOUCHFD))
                        isTouchFD = true;
                }
            }

            if (isTouchFD)
            {
                if (avlDay == 1 && isTouchFD && seqStartDateTime > processDate.Date.Add(new TimeSpan(0, 23, 59, 59)))
                    isLegal = true;

            }

            return isLegal;
        }

        public string RuleName { get; set; }

    }
}
