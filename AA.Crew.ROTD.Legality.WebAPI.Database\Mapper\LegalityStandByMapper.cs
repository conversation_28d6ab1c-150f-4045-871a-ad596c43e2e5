using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityStandByMapper : MapperBase<List<LegalityStandByDTO>, List<StandBy>>
    {
        public override List<StandBy> Map(List<LegalityStandByDTO> legalityStandByDtoList)
        {
            try
            {
                return legalityStandByDtoList.Select(legalityStandByDto => new StandBy
                {
                    AirportGate = legalityStandByDto.Airport_Gate,
                    MinAVLDays = legalityStandByDto.MinAVLDays,
                    ReportTime = legalityStandByDto.ReportTime,
                    StandByID = legalityStandByDto.StandByID,
                    ShiftDurationHrs = legalityStandByDto.ShiftDurationHrs,
                    CoTerminalStation = legalityStandByDto.CoTerminalStation,
                    Duration = legalityStandByDto.Duration,
                    Base = legalityStandByDto.BaseStation
                    
                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityStandByDTO> Map(List<StandBy> element)
        {
            throw new NotImplementedException();
        }
    }
}
