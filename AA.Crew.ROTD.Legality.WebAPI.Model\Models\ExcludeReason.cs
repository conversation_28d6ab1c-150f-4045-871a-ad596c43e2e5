using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class ExcludeReason
    {
        public ExcludeReason()
        {
            this.SequencePositionDetails = new HashSet<SequencePositionDetail>();
            this.ReservesCrewMembers = new HashSet<ReservesCrewMember>();
        }
    
        public long ExcludeReasonID { get; set; }
        public string ExcludeReason1 { get; set; }
        public Nullable<long> ExcludeReasonTypeID { get; set; }
    
        public virtual ExcludeReasonType ExcludeReasonType { get; set; }
        public virtual ICollection<SequencePositionDetail> SequencePositionDetails { get; set; }
        public virtual ICollection<ReservesCrewMember> ReservesCrewMembers { get; set; }
    }
}
