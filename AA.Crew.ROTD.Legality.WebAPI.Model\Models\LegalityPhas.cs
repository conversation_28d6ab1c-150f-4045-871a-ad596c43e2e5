using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityPhas
    {
        public LegalityPhas()
        {
            this.LegalityPhaseContractDetails = new HashSet<LegalityPhaseContractDetail>();
            this.PostQLAMappings = new HashSet<PostQLAMapping>();
            this.ReservesCrewSequenceLegalityContractDetails = new HashSet<ReservesCrewSequenceLegalityContractDetail>();
            this.LegalityPostQLAStatus = new HashSet<LegalityPostQLAStatu>();
        }
    
        public long LegalityPhaseID { get; set; }
        public string LegalityPhase { get; set; }
    
        public virtual ICollection<LegalityPhaseContractDetail> LegalityPhaseContractDetails { get; set; }
        public virtual ICollection<PostQLAMapping> PostQLAMappings { get; set; }
        public virtual ICollection<ReservesCrewSequenceLegalityContractDetail> ReservesCrewSequenceLegalityContractDetails { get; set; }
        public virtual ICollection<LegalityPostQLAStatu> LegalityPostQLAStatus { get; set; }
    }
}
