﻿using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces
{
    public interface ITransactionalAdaptor : IDataAdaptor
    {
        /// <summary>
        /// Begins a ADO.Net transaction 
        /// </summary>
        void BeginTransaction();

        /// <summary>
        /// Commits a ADO.Net transaction 
        /// </summary>
        void CommitTransaction();

        /// <summary>
        /// Rollsback a ADO.Net transaction 
        /// </summary>
        void RollBackTransaction();
    }
}
