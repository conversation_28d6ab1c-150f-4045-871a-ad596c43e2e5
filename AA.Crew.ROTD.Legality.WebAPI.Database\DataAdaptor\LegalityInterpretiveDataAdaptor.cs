﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Security.AccessControl;
using System.Reflection;
using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor
{
    public class LegalityInterpretiveDataAdaptor : AdaptorBase, ILegalityInterpretiveDataAdaptor
    {
        private const string GETQLARULELIST_SP = "ROTD.GetQLARuleList";
        private const string GETRESERVESCREWSEQUENCELEGALITYCONTRACTDETAILS_SP = "ROTD.getReservesCrewSequenceLegalityContractDetails";
        private const string GETLEGALITYSTANDBY_SP = "ROTD.getLegalityStandBy";
        private const string GETLEGALITYRESERVESCREWMEMBERS_SP = "ROTD.getLegalityReservesCrewMembers";
        private const string GETLEGALITYRESERVESCREWMEMBERACTIVITY_SP = "ROTD.getLegalityReservesCrewMemberActivity";
        private const string GETLEGALITYSEQUENCE_SP = "ROTD.getLegalitySequence";
        private const string GETLEGALITYBIDCREWWAIVER_SP = "ROTD.getLegalityBidCrewWaiver";
        private const string GETLEGALITYPROCESSBIDCREWWAIVER_SP = "ROTD.getLegalityProcessBidCrewWaiver";
        private const string GETLEGALITYBIDCREWWAIVERSUPPORTINGDATA_SP = "ROTD.getLegalityBidCrewWaiverSupportingData";
        private const string GETLEGALITYPROCESSAGGRESSIVEBIDCREWWAIVER_SP = "ROTD.getLegalityProcessAggressiveBidCrewWaiver";
        private const string GETLEGALITYPOSTQLAMAPPING_SP = "ROTD.getLegalityPostQLAMapping";
        private const string GETLEGALITYRESERVESCREWMEMBERBIDSTATUS_SP = "ROTD.getLegalityReservesCrewMemberBidStatus";
        private const string GETLEGALITYSEQUENCELANGUAGEDETAILS_SP = "ROTD.getLegalitySequenceLanguageDetails";
        private const string GETLEGALITYCREWSEQUENCEBYRUNID_SP = "ROTD.getLegalityCrewSequenceByRunID";
        private const string GETLEGALITYBASEPROCESSINGDATEBYRUNID_SP = "ROTD.getLegalityBaseProcessingDateByRunID";
        private const string GETLEGALITYCREWSEQUENCE_SP = "ROTD.getLegalityCrewSequence";
        private const string GETLEGALITYQLASUPPORTINGDATA_SP = "ROTD.getLegalityQLASupportingData";
        private const string GETRESERVESCREWSEQUENCELEGALITYLIST_SP = "ROTD.getReservesCrewSequenceLegalityList";
        private const string GETLEGALITYGETRUNID_SP = "ROTD.getLegalityGetRunId";
        private const string GETALLCONTEXTUALMAPPEDRULES_SP = "ROTD.getAllContextualMappedRules";

        private const string GETWAIVERTYPE_SP = "ROTD.getWaiverType";
        private const string GETRESERVESCREWSEQUENCELEGALITYBYRUNID_SP = "ROTD.getReservesCrewSequenceLegalitybyRunId";
        private const string GETRESERVESRESERVESLEGALITYVOLUNTEERLIST_SP = "ROTD.getReservesCrewSequenceLegalitybyRunId";
        private const string GETRAPCODE_SP = "ROTD.getRapCode";
        private const string GETCOTERMINAL_SP = "ROTD.getBaseWithCoTerminals";
        private const string GETQLARULESWAIVERDETAILS_SP = "ROTD.getQLARulesWaiverDetails";
        private const string GETQLARULES_SP = "ROTD.getQLARules";
        private const string GETPOSTQLASTATES_SP = "ROTD.getPostQLAStates";
        private const string GETLEGALITYQLASUPPORTINGDATAONLY_SP = "ROTD.getLegalityQLASupportingDataonly";
        private const string GETLEGALITYQLASUPPORTINGDATA_VOLUNTEER_SP = "ROTD.getLegalityQLASupportingData_volunteer";
        private const string GETLEGALITYQLASUPPORTINGDATA_NONVOLUNTEERSTANDBY_SP = "ROTD.getLegalityQLASupportingData_NonVolunteerStandBy";
        private const string GETLEGALITYPHASES_SP = "ROTD.getLegalityPhases";
        private const string GETCONTRACTSECTIONS_SP = "ROTD.getContractSections";
        private const string GETLEGALITIESWAIVERTYPE_SP = "ROTD.getLegalitiesWaiverType";
        private const string GETLEGALITYRESERVESCREWMEMBERLANGUAGEDETAILS_SP = "ROTD.getLegalityReservesCrewMemberLanguageDetails";
        private const string GETRESERVESLEGALITYQLALIST_SP = "ROTD.getReservesLegalityQLAList";
        private const string SAVELEGALITYQLAMESSAGE_SP = "ROTD.SaveLegalityQLAMessage";
        private const string AWARDPROGRESSUPDATEASINPREGESS_SP = "[ROTD].[awardProgressStepUpdateAsInProgressDaily]";
        private const string AWARDPROGRESSUPDATEASCOMPLETE_SP = "[ROTD].[awardProgressStepUpdateAsCompleteDaily]";
        private const string AWARDPROGRESSUPDATEASERROR_SP = "[ROTD].awardProgressStep_UpdateAsErrorDaily";
        private string _connectionstring;

        private SqlDataReader sqlDR = null;
        private readonly object lockObject = new object();
        public LegalityInterpretiveDataAdaptor(string connectionstring)
            : base(connectionstring)
        {
            _connectionstring = connectionstring;
        }

        public async Task<List<QLARuleListDTO>> GetQLARuleList(decimal rulesCount, int phaseID, int runId)
        {
            List<QLARuleListDTO> resultList = new List<QLARuleListDTO>();

            int numberOfRecords = 5000000;
            int pageNumbers = (int)Math.Ceiling(rulesCount / numberOfRecords) + 1;
            try
            {
                Parallel.For(1, pageNumbers,
                  index =>
                  {
                      this.InitalizeGetQLARuleList();

                      this.SqlCommand.Parameters["@RunID"].Value = runId;
                      this.SqlCommand.Parameters["@PhaseID"].Value = phaseID;
                      this.SqlCommand.Parameters["@RowspPage"].Value = numberOfRecords;
                      this.SqlCommand.Parameters["@PageNumber"].Value = index;

                      //Execute the Stored Procedure
                      var sqlDR = this.SqlCommand.ExecuteReaderAsync().Result;

                      //Declare Ordinal stucts to hold the column position while reading data.
                      QLARuleListOrdinal ordinals = new QLARuleListOrdinal();

                      List<QLARuleListDTO> results = new List<QLARuleListDTO>();

                      while (sqlDR.Read()) //For each tCostConfig Record returned
                      {

                          //Determine the the ordinals for the select statement have been assigned.
                          if (!ordinals.Initialized)
                          {
                              //If not then assign them.
                              ordinals.Initialize(sqlDR);
                          }

                          QLARuleListDTO item = new QLARuleListDTO();

                          if (!sqlDR.IsDBNull(ordinals.LegalityQLARulesID))
                              item.LegalityQLARulesID = sqlDR.GetInt64(ordinals.LegalityQLARulesID);

                          if (!sqlDR.IsDBNull(ordinals.LegalityQLADetailsID))
                              item.LegalityQLADetailsID = sqlDR.GetInt64(ordinals.LegalityQLADetailsID);

                          if (!sqlDR.IsDBNull(ordinals.Result))
                              item.Result = sqlDR.GetString(ordinals.Result);

                          if (!sqlDR.IsDBNull(ordinals.QLARuleName))
                              item.QLARuleName = sqlDR.GetString(ordinals.QLARuleName);

                          if (!sqlDR.IsDBNull(ordinals.QLARuleID))
                              item.QLARuleID = sqlDR.GetInt64(ordinals.QLARuleID);

                          if (!sqlDR.IsDBNull(ordinals.Message))
                              item.Message = sqlDR.GetString(ordinals.Message);
                          
                          if (!sqlDR.IsDBNull(ordinals.SequenceID))
                              item.SequenceID = sqlDR.GetInt32(ordinals.SequenceID);

                          results.Add(item);
                      }

                      lock (lockObject)
                      {
                          resultList.AddRange(results);
                      }
                      GC.Collect();
                  });
            }
            catch (Exception)
            {
                throw;
            }

            return resultList.OrderBy(x => x.LegalityQLARulesID).ToList();
        }

        public async Task<List<ReservesCrewSequenceLegalityContractDetailsDTO>> GetReservesCrewSequenceLegalityContractDetails(decimal contractCount, int phaseID, int runId)
        {
            List<ReservesCrewSequenceLegalityContractDetailsDTO> resultList = new List<ReservesCrewSequenceLegalityContractDetailsDTO>();

            int numberOfRecords = 5000000;
            int pageNumbers = (int)Math.Ceiling(contractCount / numberOfRecords) + 1;
            try
            {
                Parallel.For(1, pageNumbers,
                  index =>
                  {
                      this.InitalizeGetReservesCrewSequenceLegalityContractDetails();

                      this.SqlCommand.Parameters["@RunID"].Value = runId;
                      this.SqlCommand.Parameters["@PhaseID"].Value = phaseID;
                      this.SqlCommand.Parameters["@RowspPage"].Value = numberOfRecords;
                      this.SqlCommand.Parameters["@PageNumber"].Value = index;


                      //Execute the Stored Procedure
                      var sqlDR = this.SqlCommand.ExecuteReader();

                      List<ReservesCrewSequenceLegalityContractDetailsDTO> results = new List<ReservesCrewSequenceLegalityContractDetailsDTO>();

                      //Declare Ordinal stucts to hold the column position while reading data.
                      ReservesCrewSequenceLegalityContractDetailsOrdinal ordinals = new ReservesCrewSequenceLegalityContractDetailsOrdinal();

                      while (sqlDR.Read()) //For each tCostConfig Record returned
                      {

                          //Determine the the ordinals for the select statement have been assigned.
                          if (!ordinals.Initialized)
                          {
                              //If not then assign them.
                              ordinals.Initialize(sqlDR);
                          }

                          ReservesCrewSequenceLegalityContractDetailsDTO item = new ReservesCrewSequenceLegalityContractDetailsDTO();

                          if (!sqlDR.IsDBNull(ordinals.ReservesCrewSequenceLegalityContractDetailsID))
                              item.ReservesCrewSequenceLegalityContractDetailsID = sqlDR.GetInt64(ordinals.ReservesCrewSequenceLegalityContractDetailsID);

                          if (!sqlDR.IsDBNull(ordinals.ContractSectionsID))
                              item.ContractSectionsID = sqlDR.GetInt64(ordinals.ContractSectionsID);

                          if (!sqlDR.IsDBNull(ordinals.LegalityPhaseID))
                              item.LegalityPhaseID = sqlDR.GetInt64(ordinals.LegalityPhaseID);

                          if (!sqlDR.IsDBNull(ordinals.ReservesCrewSequenceLegalityID))
                              item.ReservesCrewSequenceLegalityID = sqlDR.GetInt64(ordinals.ReservesCrewSequenceLegalityID);

                          if (!sqlDR.IsDBNull(ordinals.LanguageID))
                              item.LanguageID = sqlDR.GetInt32(ordinals.LanguageID);

                          if (!sqlDR.IsDBNull(ordinals.FosRAP))
                              item.FosRAP = sqlDR.GetInt32(ordinals.FosRAP);

                          if (!sqlDR.IsDBNull(ordinals.IsCurrentRAP))
                              item.IsCurrentRAP = sqlDR.GetBoolean(ordinals.IsCurrentRAP);

                          results.Add(item);
                      }

                      lock (lockObject)
                      {
                          resultList.AddRange(results);
                      }
                      GC.Collect();
                  });

            }
            catch (Exception)
            {
                throw;
            }

            return resultList;
        }

        public async Task<List<LegalityStandByDTO>> GetLegalityStandBy(long runID, DateTime bidOperatingDate)
        {
            List<LegalityStandByDTO> results = new List<LegalityStandByDTO>();
            try
            {
                this.InitalizeGetLegalityStandBy();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityStandByOrdinal ordinals = new LegalityStandByOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityStandByDTO item = new LegalityStandByDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.StandByID))
                        item.StandByID = this.sqlDR.GetInt32(ordinals.StandByID);

                    if (!this.sqlDR.IsDBNull(ordinals.Airport_Gate))
                        item.CoTerminalStation = this.sqlDR.GetString(ordinals.Airport_Gate) == null ? true : false;

                    if (!this.sqlDR.IsDBNull(ordinals.ReportTime))
                        item.ReportTime = bidOperatingDate.Add(this.sqlDR.GetTimeSpan(ordinals.ReportTime));

                    if (!this.sqlDR.IsDBNull(ordinals.CreatedDate))
                        item.CreatedDate = this.sqlDR.GetDateTime(ordinals.CreatedDate);

                    if (!this.sqlDR.IsDBNull(ordinals.MinAVLDays))
                        item.MinAVLDays = this.sqlDR.GetInt32(ordinals.MinAVLDays);

                    if (!this.sqlDR.IsDBNull(ordinals.ShiftDurationHrs))
                        item.ShiftDurationHrs = this.sqlDR.GetString(ordinals.ShiftDurationHrs);

                    if (!this.sqlDR.IsDBNull(ordinals.ShiftDurationHrs))
                        item.Duration = this.sqlDR.GetString(ordinals.ShiftDurationHrs).Length > 0 ? Convert.ToInt32(this.sqlDR.GetString(ordinals.ShiftDurationHrs).Substring(0, 1)) : 0;

                    if (!this.sqlDR.IsDBNull(ordinals.BaseStation))
                        item.BaseStation = this.sqlDR.GetString(ordinals.BaseStation);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityReservesCrewMembersDTO>> GetLegalityReservesCrewMembers(long runID)
        {
            List<LegalityReservesCrewMembersDTO> results = new List<LegalityReservesCrewMembersDTO>();
            try
            {
                this.InitalizeGetLegalityReservesCrewMembers();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityReservesCrewMembersOrdinal ordinals = new LegalityReservesCrewMembersOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityReservesCrewMembersDTO item = new LegalityReservesCrewMembersDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ASGDays))
                        item.ASGDays = this.sqlDR.GetInt32(ordinals.ASGDays);

                    if (!this.sqlDR.IsDBNull(ordinals.ASGDaysWithFT))
                        item.ASGDaysWithFT = this.sqlDR.GetInt32(ordinals.ASGDaysWithFT);

                    if (!this.sqlDR.IsDBNull(ordinals.ASGSequence))
                        item.ASGSequence = this.sqlDR.GetInt32(ordinals.ASGSequence);

                    if (!this.sqlDR.IsDBNull(ordinals.ASGStandby))
                        item.ASGStandby = this.sqlDR.GetInt32(ordinals.ASGStandby);

                    if (!this.sqlDR.IsDBNull(ordinals.AVLDays))
                        item.AVLDays = this.sqlDR.GetInt32(ordinals.AVLDays);

                    if (!this.sqlDR.IsDBNull(ordinals.AVLDaysWithFT))
                        item.AVLDaysWithFT = this.sqlDR.GetInt32(ordinals.AVLDaysWithFT);

                    if (!this.sqlDR.IsDBNull(ordinals.RunID))
                        item.RunID = this.sqlDR.GetInt64(ordinals.RunID);

                    if (!this.sqlDR.IsDBNull(ordinals.IsAggressive))
                        item.IsAggressive = this.sqlDR.GetBoolean(ordinals.IsAggressive);

                    if (!this.sqlDR.IsDBNull(ordinals.RunExcludeReasonID))
                        item.RunExcludeReasonID = this.sqlDR.GetInt64(ordinals.RunExcludeReasonID);

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeID))
                        item.EmployeeID = Convert.ToInt32(this.sqlDR.GetInt64(ordinals.EmployeeID));

                    if (!this.sqlDR.IsDBNull(ordinals.IsSick))
                        item.IsSick = this.sqlDR.GetBoolean(ordinals.IsSick);

                    if (!this.sqlDR.IsDBNull(ordinals.IsVolunteer))
                        item.IsVolunteer = this.sqlDR.GetBoolean(ordinals.IsVolunteer);

                    if (!this.sqlDR.IsDBNull(ordinals.Name))
                        item.Name = this.sqlDR.GetString(ordinals.Name);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberID))
                        item.ReservesCrewMemberID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.SeniorityNumber))
                        item.SeniorityNumber = this.sqlDR.GetInt32(ordinals.SeniorityNumber);
                    if (!this.sqlDR.IsDBNull(ordinals.ETBonFD))
                        item.ETBonFD = this.sqlDR.GetBoolean(ordinals.ETBonFD);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityCrewMemberActivityDTO>> GetLegalityCrewMemberActivity(long runID)
        {
            List<LegalityCrewMemberActivityDTO> results = new List<LegalityCrewMemberActivityDTO>();
            try
            {
                this.InitalizeGetLegalityCrewMemberActivity();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityCrewMemberActivityOrdinal ordinals = new LegalityCrewMemberActivityOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityCrewMemberActivityDTO item = new LegalityCrewMemberActivityDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberActivityID))
                        item.ReservesCrewMemberActivityID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberActivityID);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberActivityTypeID))
                        item.ReservesCrewMemberActivityTypeID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberActivityTypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityCode))
                        item.ActivityCode = this.sqlDR.GetString(ordinals.ActivityCode);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityType))
                        item.ActivityType = this.sqlDR.GetString(ordinals.ActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.StartDateTime))
                        item.StartDateTime = this.sqlDR.GetDateTime(ordinals.StartDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.EndDateTime))
                        item.EndDateTime = this.sqlDR.GetDateTime(ordinals.EndDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.HomeBaseFAReducedRestEndTime))
                        item.HomeBaseFAReducedRestEndTime = this.sqlDR.GetDateTime(ordinals.HomeBaseFAReducedRestEndTime);

                    if (!this.sqlDR.IsDBNull(ordinals.HomeBaseRestEndTime))
                        item.HomeBaseRestEndTime = this.sqlDR.GetDateTime(ordinals.HomeBaseRestEndTime);

                    if (!this.sqlDR.IsDBNull(ordinals.DurationInDays))
                        item.DurationInDays = this.sqlDR.GetInt32(ordinals.DurationInDays);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberID))
                        item.ReservesCrewMemberID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeID))
                        item.EmployeeID = this.sqlDR.GetInt64(ordinals.EmployeeID);





                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalitySequenceDTO>> GetLegalitySequence(long runID)
        {
            List<LegalitySequenceDTO> results = new List<LegalitySequenceDTO>();
            try
            {
                this.InitalizeGetLegalitySequence();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalitySequenceOrdinal ordinals = new LegalitySequenceOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalitySequenceDTO item = new LegalitySequenceDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.SequenceID))
                        item.SequenceID = this.sqlDR.GetInt64(ordinals.SequenceID);

                    if (!this.sqlDR.IsDBNull(ordinals.RunID))
                        item.RunID = this.sqlDR.GetInt64(ordinals.RunID);

                    if (!this.sqlDR.IsDBNull(ordinals.SequenceNumber))
                        item.SequenceNumber = this.sqlDR.GetInt32(ordinals.SequenceNumber);

                    if (!this.sqlDR.IsDBNull(ordinals.SequencePositionDetailsID))
                        item.SequencePositionDetailsID = this.sqlDR.GetInt64(ordinals.SequencePositionDetailsID);

                    if (!this.sqlDR.IsDBNull(ordinals.SequencePosition))
                        item.SequencePosition = this.sqlDR.GetString(ordinals.SequencePosition);

                    if (!this.sqlDR.IsDBNull(ordinals.OpenTime))
                        item.OpenTime = this.sqlDR.GetDateTime(ordinals.OpenTime);

                    if (!this.sqlDR.IsDBNull(ordinals.ExcludeReasonID))
                        item.ExcludeReasonID = this.sqlDR.GetInt64(ordinals.ExcludeReasonID);

                    if (!this.sqlDR.IsDBNull(ordinals.SequenceStartDateTime))
                        item.SequenceStartDateTime = this.sqlDR.GetDateTime(ordinals.SequenceStartDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.DurationInDays))
                        item.DurationInDays = this.sqlDR.GetInt32(ordinals.DurationInDays);

                    if (!this.sqlDR.IsDBNull(ordinals.TotalCreditCurrentMonth))
                        item.TotalCreditCurrentMonth = this.sqlDR.GetInt32(ordinals.TotalCreditCurrentMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.SequenceEndDateTime))
                        item.SequenceEndDateTime = this.sqlDR.GetDateTime(ordinals.SequenceEndDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.TotalDutyPeriod))
                        item.TotalDutyPeriod = this.sqlDR.GetInt32(ordinals.TotalDutyPeriod);

                    if (!this.sqlDR.IsDBNull(ordinals.CoTerminalStation))
                        item.CoTerminalStation = this.sqlDR.GetString(ordinals.CoTerminalStation);

                    if (!this.sqlDR.IsDBNull(ordinals.SatelliteStation))
                        item.SatelliteStation = this.sqlDR.GetString(ordinals.SatelliteStation);

                    if (!this.sqlDR.IsDBNull(ordinals.OriginationDate))
                        item.OriginationDate = this.sqlDR.GetDateTime(ordinals.OriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.TotalCreditNextMonth))
                        item.TotalCreditNextMonth = this.sqlDR.GetInt32(ordinals.TotalCreditNextMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.SequenceDepartureDateTime))
                        item.SequenceDepartureDateTime = this.sqlDR.GetDateTime(ordinals.SequenceDepartureDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.LayOverStations))
                        item.LayOverStations = this.sqlDR.GetString(ordinals.LayOverStations);

                    if (!this.sqlDR.IsDBNull(ordinals.LegsPerDutyPeriod))
                        item.LegsPerDutyPeriod = this.sqlDR.GetString(ordinals.LegsPerDutyPeriod);

                    if (!this.sqlDR.IsDBNull(ordinals.MultipleEquipments))
                        item.MultipleEquipments = this.sqlDR.GetBoolean(ordinals.MultipleEquipments);

                    if (!this.sqlDR.IsDBNull(ordinals.EquipmentGroup))
                        item.EquipmentGroup = this.sqlDR.GetString(ordinals.EquipmentGroup);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityBidCrewWaiverDTO>> GetLegalityBidCrewWaiver(long runID)
        {
            List<LegalityBidCrewWaiverDTO> results = new List<LegalityBidCrewWaiverDTO>();
            try
            {
                this.InitalizeGetLegalityBidCrewWaiver();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityBidCrewWaiverOrdinal ordinals = new LegalityBidCrewWaiverOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityBidCrewWaiverDTO item = new LegalityBidCrewWaiverDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.BidCrewWaiverID))
                        item.BidCrewWaiverID = this.sqlDR.GetInt64(ordinals.BidCrewWaiverID);

                    if (!this.sqlDR.IsDBNull(ordinals.CrewMemberID))
                        item.CrewMemberID = this.sqlDR.GetInt64(ordinals.CrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.CreateDate))
                        item.CreateDate = this.sqlDR.GetDateTime(ordinals.CreateDate);

                    if (!this.sqlDR.IsDBNull(ordinals.EndDate))
                        item.EndDate = this.sqlDR.GetDateTime(ordinals.EndDate);

                    if (!this.sqlDR.IsDBNull(ordinals.BidTypeID))
                        item.BidTypeID = this.sqlDR.GetInt32(ordinals.BidTypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeID))
                        item.WaiverTypeID = this.sqlDR.GetInt32(ordinals.WaiverTypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeDescription))
                        item.WaiverTypeDescription = this.sqlDR.GetString(ordinals.WaiverTypeDescription);

                    if (!this.sqlDR.IsDBNull(ordinals.WTActive))
                        item.WTActive = this.sqlDR.GetBoolean(ordinals.WTActive);

                    if (!this.sqlDR.IsDBNull(ordinals.BDWActive))
                        item.BDWActive = this.sqlDR.GetBoolean(ordinals.BDWActive);

                    if (!this.sqlDR.IsDBNull(ordinals.UpdateDate))
                        item.UpdateDate = this.sqlDR.GetDateTime(ordinals.UpdateDate);





                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityProcessBidCrewWaiverDTO>> GetLegalityProcessBidCrewWaiver(long runID)
        {
            List<LegalityProcessBidCrewWaiverDTO> results = new List<LegalityProcessBidCrewWaiverDTO>();
            try
            {
                this.InitalizeGetLegalityProcessBidCrewWaiver();

                this.SqlCommand.Parameters["@runId"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityProcessBidCrewWaiverOrdinal ordinals = new LegalityProcessBidCrewWaiverOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityProcessBidCrewWaiverDTO item = new LegalityProcessBidCrewWaiverDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.processBidCrewWaiverID))
                        item.processBidCrewWaiverID = this.sqlDR.GetInt64(ordinals.processBidCrewWaiverID);

                    if (!this.sqlDR.IsDBNull(ordinals.CrewMemberID))
                        item.CrewMemberID = this.sqlDR.GetInt64(ordinals.CrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.CreateDate))
                        item.CreateDate = this.sqlDR.GetDateTime(ordinals.CreateDate);

                    if (!this.sqlDR.IsDBNull(ordinals.EndDate))
                        item.EndDate = this.sqlDR.GetDateTime(ordinals.EndDate);

                    if (!this.sqlDR.IsDBNull(ordinals.BidTypeID))
                        item.BidTypeID = this.sqlDR.GetInt32(ordinals.BidTypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeID))
                        item.WaiverTypeID = this.sqlDR.GetInt32(ordinals.WaiverTypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeDescription))
                        item.WaiverTypeDescription = this.sqlDR.GetString(ordinals.WaiverTypeDescription);

                    if (!this.sqlDR.IsDBNull(ordinals.WTActive))
                        item.WTActive = this.sqlDR.GetBoolean(ordinals.WTActive);

                    if (!this.sqlDR.IsDBNull(ordinals.BDWActive))
                        item.BDWActive = this.sqlDR.GetBoolean(ordinals.BDWActive);

                    if (!this.sqlDR.IsDBNull(ordinals.UpdateDate))
                        item.UpdateDate = this.sqlDR.GetDateTime(ordinals.UpdateDate);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityProcessBidCrewWaiverSupportingDataDTO>> GetLegalityProcessBidCrewWaiverSupportingData(long runID)
        {
            List<LegalityProcessBidCrewWaiverSupportingDataDTO> results = new List<LegalityProcessBidCrewWaiverSupportingDataDTO>();
            try
            {
                this.InitalizeGetLegalityProcessBidCrewWaiverSupportingData();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityProcessBidCrewWaiverSupportingDataOrdinal ordinals = new LegalityProcessBidCrewWaiverSupportingDataOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityProcessBidCrewWaiverSupportingDataDTO item = new LegalityProcessBidCrewWaiverSupportingDataDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ProcessBidCrewWaiverID))
                        item.ProcessBidCrewWaiverID = this.sqlDR.GetInt64(ordinals.ProcessBidCrewWaiverID);

                    if (!this.sqlDR.IsDBNull(ordinals.BaseCoTerminal))
                        item.BaseCoTerminal = this.sqlDR.GetString(ordinals.BaseCoTerminal);

                    if (!this.sqlDR.IsDBNull(ordinals.TimeToDeparture))
                        item.TimeToDeparture = this.sqlDR.GetTimeSpan(ordinals.TimeToDeparture);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityProcessAggressiveBidCrewWaiverDTO>> GetLegalityProcessAggressiveBidCrewWaiver(long runID)
        {
            List<LegalityProcessAggressiveBidCrewWaiverDTO> results = new List<LegalityProcessAggressiveBidCrewWaiverDTO>();
            try
            {
                this.InitalizeGetLegalityProcessAggressiveBidCrewWaiver();

                this.SqlCommand.Parameters["@runId"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityProcessAggressiveBidCrewWaiverOrdinal ordinals = new LegalityProcessAggressiveBidCrewWaiverOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityProcessAggressiveBidCrewWaiverDTO item = new LegalityProcessAggressiveBidCrewWaiverDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeID))
                        item.EmployeeID = this.sqlDR.GetInt64(ordinals.EmployeeID);

                    if (!this.sqlDR.IsDBNull(ordinals.FAName))
                        item.FAName = this.sqlDR.GetString(ordinals.FAName);

                    if (!this.sqlDR.IsDBNull(ordinals.BidtypeID))
                        item.BidtypeID = this.sqlDR.GetInt32(ordinals.BidtypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.BidTypeName))
                        item.BidTypeName = this.sqlDR.GetString(ordinals.BidTypeName);

                    if (!this.sqlDR.IsDBNull(ordinals.Runid))
                        item.Runid = this.sqlDR.GetInt64(ordinals.Runid);

                    if (!this.sqlDR.IsDBNull(ordinals.BidCategoryID))
                        item.BidCategoryID = this.sqlDR.GetInt32(ordinals.BidCategoryID);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityPostQLAMappingDTO>> GetLegalityPostQLAMapping(long runID)
        {
            List<LegalityPostQLAMappingDTO> results = new List<LegalityPostQLAMappingDTO>();
            try
            {
                this.InitalizeGetLegalityPostQLAMapping();

                this.SqlCommand.Parameters["@runId"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityPostQLAMappingOrdinal ordinals = new LegalityPostQLAMappingOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityPostQLAMappingDTO item = new LegalityPostQLAMappingDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.PostQLAMappingID))
                        item.PostQLAMappingID = this.sqlDR.GetInt64(ordinals.PostQLAMappingID);

                    if (!this.sqlDR.IsDBNull(ordinals.PostQLAStateID))
                        item.PostQLAStateID = this.sqlDR.GetInt64(ordinals.PostQLAStateID);

                    if (!this.sqlDR.IsDBNull(ordinals.QLARuleID))
                        item.QLARuleID = this.sqlDR.GetInt64(ordinals.QLARuleID);

                    if (!this.sqlDR.IsDBNull(ordinals.ContractSectionsID))
                        item.ContractSectionsID = this.sqlDR.GetInt64(ordinals.ContractSectionsID);

                    if (!this.sqlDR.IsDBNull(ordinals.ContractSection))
                        item.ContractSection = this.sqlDR.GetString(ordinals.ContractSection);

                    if (!this.sqlDR.IsDBNull(ordinals.LegalityPhaseID))
                        item.LegalityPhaseID = this.sqlDR.GetInt64(ordinals.LegalityPhaseID);

                    if (!this.sqlDR.IsDBNull(ordinals.QLARule))
                        item.QLARule = this.sqlDR.GetString(ordinals.QLARule);

                    if (!this.sqlDR.IsDBNull(ordinals.PostQLAState))
                        item.PostQLAState = this.sqlDR.GetString(ordinals.PostQLAState);

                    if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeID))
                        item.WaiverTypeID = this.sqlDR.GetInt32(ordinals.WaiverTypeID).ToString();

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityBidStatusDTO>> GetLegalityBidStatus(long runID)
        {
            List<LegalityBidStatusDTO> results = new List<LegalityBidStatusDTO>();
            try
            {
                this.InitalizeGetLegalityBidStatus();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityBidStatusOrdinal ordinals = new LegalityBidStatusOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityBidStatusDTO item = new LegalityBidStatusDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberBidStatusID))
                        item.ReservesCrewMemberBidStatusID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberBidStatusID);

                    if (!this.sqlDR.IsDBNull(ordinals.CalloutTime))
                        item.CalloutTime = this.sqlDR.GetDecimal(ordinals.CalloutTime);

                    if (!this.sqlDR.IsDBNull(ordinals.MaxCredit))
                        item.MaxCredit = this.sqlDR.GetDecimal(ordinals.MaxCredit);

                    if (!this.sqlDR.IsDBNull(ordinals.ActualPayProjection))
                        item.ActualPayProjection = this.sqlDR.GetDecimal(ordinals.ActualPayProjection);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberID))
                        item.ReservesCrewMemberID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.CrewStatusID))
                        item.CrewStatusID = this.sqlDR.GetInt64(ordinals.CrewStatusID);

                    if (!this.sqlDR.IsDBNull(ordinals.BidMonthIndicatorID))
                        item.BidMonthIndicatorID = this.sqlDR.GetInt64(ordinals.BidMonthIndicatorID);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalitySequenceLanguageDetailsDTO>> GetLegalitySequenceLanguageDetails(long runID)
        {
            List<LegalitySequenceLanguageDetailsDTO> results = new List<LegalitySequenceLanguageDetailsDTO>();
            try
            {
                this.InitalizeGetLegalitySequenceLanguageDetails();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalitySequenceLanguageDetailsOrdinal ordinals = new LegalitySequenceLanguageDetailsOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalitySequenceLanguageDetailsDTO item = new LegalitySequenceLanguageDetailsDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.SequencePositionDetailsID))
                        item.SequencePositionDetailsID = this.sqlDR.GetInt64(ordinals.SequencePositionDetailsID);

                    if (!this.sqlDR.IsDBNull(ordinals.LanguageID))
                        item.LanguageID = this.sqlDR.GetInt32(ordinals.LanguageID);

                    if (!this.sqlDR.IsDBNull(ordinals.IsExcluded))
                        item.IsExcluded = this.sqlDR.GetBoolean(ordinals.IsExcluded);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        //TODO: Not needed. Check again
        public async Task<List<LegalityCrewSequenceByRunIDDTO>> GetLegalityCrewSequenceByRunID(long runID)
        {
            List<LegalityCrewSequenceByRunIDDTO> results = new List<LegalityCrewSequenceByRunIDDTO>();
            try
            {
                this.InitalizeGetLegalityCrewSequenceByRunID();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityCrewSequenceByRunIDOrdinal ordinals = new LegalityCrewSequenceByRunIDOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityCrewSequenceByRunIDDTO item = new LegalityCrewSequenceByRunIDDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewSequenceLegalityID))
                        item.ReservesCrewSequenceLegalityID = this.sqlDR.GetInt64(ordinals.ReservesCrewSequenceLegalityID);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberID))
                        item.ReservesCrewMemberID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.RunID))
                        item.RunID = this.sqlDR.GetInt64(ordinals.RunID);

                    if (!this.sqlDR.IsDBNull(ordinals.SequencePositionDetailsID))
                        item.SequencePositionDetailsID = this.sqlDR.GetInt64(ordinals.SequencePositionDetailsID);

                    if (!this.sqlDR.IsDBNull(ordinals.StandByID))
                        item.StandByID = this.sqlDR.GetInt32(ordinals.StandByID);

                    if (!this.sqlDR.IsDBNull(ordinals.IsOver35By7))
                        item.IsOver35By7 = this.sqlDR.GetBoolean(ordinals.IsOver35By7);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<BaseProcessingDateDTO> GetBaseProcessingDate(long runID)
        {
            BaseProcessingDateDTO result = new BaseProcessingDateDTO();
            try
            {
                this.InitalizeGetBaseProcessingDate();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                BaseProcessingDateOrdinal ordinals = new BaseProcessingDateOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    if (!this.sqlDR.IsDBNull(ordinals.BaseCD))
                        result.BaseCD = this.sqlDR.GetString(ordinals.BaseCD);

                    if (!this.sqlDR.IsDBNull(ordinals.ProcessingDate))
                        result.ProcessingDate = this.sqlDR.GetDateTime(ordinals.ProcessingDate);

                }

            }
            catch (Exception)
            {
                throw;
            }

            return result;
        }

        //TODO: Not needed. Check again
        public async Task<List<LegalityCrewSequenceDTO>> GetLegalityCrewSequence(long runID)
        {
            List<LegalityCrewSequenceDTO> results = new List<LegalityCrewSequenceDTO>();
            try
            {
                this.InitalizeGetLegalityCrewSequence();

                this.SqlCommand.Parameters["@RunID"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityCrewSequenceOrdinal ordinals = new LegalityCrewSequenceOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityCrewSequenceDTO item = new LegalityCrewSequenceDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewSequenceLegalityID))
                        item.ReservesCrewSequenceLegalityID = this.sqlDR.GetInt64(ordinals.ReservesCrewSequenceLegalityID);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberID))
                        item.ReservesCrewMemberID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.RunID))
                        item.RunID = this.sqlDR.GetInt64(ordinals.RunID);

                    if (!this.sqlDR.IsDBNull(ordinals.SequencePositionDetailsID))
                        item.SequencePositionDetailsID = this.sqlDR.GetInt64(ordinals.SequencePositionDetailsID);

                    if (!this.sqlDR.IsDBNull(ordinals.StandByID))
                        item.StandByID = this.sqlDR.GetInt32(ordinals.StandByID);

                    if (!this.sqlDR.IsDBNull(ordinals.IsOver35By7))
                        item.IsOver35By7 = this.sqlDR.GetBoolean(ordinals.IsOver35By7);

                    if (!this.sqlDR.IsDBNull(ordinals.SequenceID))
                        item.SequenceID = this.sqlDR.GetInt64(ordinals.SequenceID);

                    if (!this.sqlDR.IsDBNull(ordinals.SequenceNumber))
                        item.SequenceNumber = this.sqlDR.GetInt32(ordinals.SequenceNumber);

                    if (!this.sqlDR.IsDBNull(ordinals.SequencePosition))
                        item.SequencePosition = this.sqlDR.GetString(ordinals.SequencePosition);

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeID))
                        item.EmployeeID = this.sqlDR.GetInt64(ordinals.EmployeeID);

                    if (!this.sqlDR.IsDBNull(ordinals.ShiftStartTime))
                        item.ShiftStartTime = this.sqlDR.GetTimeSpan(ordinals.ShiftStartTime);

                    if (!this.sqlDR.IsDBNull(ordinals.ShiftDurationHrs))
                        item.ShiftDurationHrs = this.sqlDR.GetString(ordinals.ShiftDurationHrs);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityQLASupportingDataDTO>> GetLegalityQLASupportingData(long runID)
        {
            List<LegalityQLASupportingDataDTO> results = new List<LegalityQLASupportingDataDTO>();
            try
            {
                this.InitalizeGetLegalityQLASupportingData();

                this.SqlCommand.Parameters["@RunId"].Value = runID;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityQLASupportingDataOrdinal ordinals = new LegalityQLASupportingDataOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityQLASupportingDataDTO item = new LegalityQLASupportingDataDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.RunId))
                        item.RunId = this.sqlDR.GetInt64(ordinals.RunId);

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeId))
                        item.EmployeeId = this.sqlDR.GetInt64(ordinals.EmployeeId);

                    if (!this.sqlDR.IsDBNull(ordinals.AffectedType))
                        item.AffectedType = this.sqlDR.GetString(ordinals.AffectedType);

                    if (!this.sqlDR.IsDBNull(ordinals.ContractMonth))
                        item.ContractMonth = this.sqlDR.GetString(ordinals.ContractMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityId))
                        item.ActivityId = this.sqlDR.GetInt32(ordinals.ActivityId);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityCode))
                        item.ActivityCode = this.sqlDR.GetString(ordinals.ActivityCode);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityType))
                        item.ActivityType = this.sqlDR.GetString(ordinals.ActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityOriginationDate))
                        item.ActivityOriginationDate = this.sqlDR.GetString(ordinals.ActivityOriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.PositionCode))
                        item.PositionCode = this.sqlDR.GetString(ordinals.PositionCode);

                    if (!this.sqlDR.IsDBNull(ordinals.StartDateTime))
                        item.StartDateTime = this.sqlDR.GetDateTime(ordinals.StartDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.EndDateTime))
                        item.EndDateTime = this.sqlDR.GetString(ordinals.EndDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupContractMonth))
                        item.PickupContractMonth = this.sqlDR.GetString(ordinals.PickupContractMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityId))
                        item.PickupActivityId = this.sqlDR.GetInt32(ordinals.PickupActivityId);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityCode))
                        item.PickupActivityCode = this.sqlDR.GetString(ordinals.PickupActivityCode);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityType))
                        item.PickupActivityType = this.sqlDR.GetString(ordinals.PickupActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityOriginationDate))
                        item.PickupActivityOriginationDate = this.sqlDR.GetString(ordinals.PickupActivityOriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupPositionCode))
                        item.PickupPositionCode = this.sqlDR.GetString(ordinals.PickupPositionCode);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupStartDateTime))
                        item.PickupStartDateTime = this.sqlDR.GetDateTime(ordinals.PickupStartDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupEndDateTime))
                        item.PickupEndDateTime = this.sqlDR.GetString(ordinals.PickupEndDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityReportDateTime))
                        item.ActivityReportDateTime = this.sqlDR.GetDateTime(ordinals.ActivityReportDateTime);

                    if (!this.sqlDR.IsDBNull(ordinals.LegalityPhaseId))
                        item.LegalityPhaseId = this.sqlDR.GetInt64(ordinals.LegalityPhaseId);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        //TODO: getReservesCrewSequenceLegalityList object 
        //public async Task<List<LegalityROTDPhaseDetailDTO>> GetLegalityROTDPhaseDetail(long runID, int phaseid)
        //{
        //    List<LegalityROTDPhaseDetailDTO> results = new List<LegalityROTDPhaseDetailDTO>();
        //    try
        //    {
        //        this.InitalizeGetLegalityROTDPhaseDetail();

        //        this.SqlCommand.Parameters["@RunID"].Value = runID;


        //        //Execute the Stored Procedure
        //        this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

        //        //Declare Ordinal stucts to hold the column position while reading data.
        //        LegalityROTDPhaseDetailOrdinal ordinals = new LegalityROTDPhaseDetailOrdinal();

        //        while (this.sqlDR.Read()) //For each tCostConfig Record returned
        //        {

        //            //Determine the the ordinals for the select statement have been assigned.
        //            if (!ordinals.Initialized)
        //            {
        //                //If not then assign them.
        //                ordinals.Initialize(this.sqlDR);
        //            }

        //            LegalityROTDPhaseDetailDTO item = new LegalityROTDPhaseDetailDTO();

        //            if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewSequenceLegalityID))
        //                item.ReservesCrewSequenceLegalityID = this.sqlDR.GetInt64(ordinals.ReservesCrewSequenceLegalityID);

        //            if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberID))
        //                item.ReservesCrewMemberID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberID);

        //            if (!this.sqlDR.IsDBNull(ordinals.RunID))
        //                item.RunID = this.sqlDR.GetInt64(ordinals.RunID);

        //            if (!this.sqlDR.IsDBNull(ordinals.SequencePositionDetailsID))
        //                item.SequencePositionDetailsId = this.sqlDR.GetInt64(ordinals.SequencePositionDetailsID);

        //            if (!this.sqlDR.IsDBNull(ordinals.StandbyID))
        //                item.StandbyID = this.sqlDR.GetInt32(ordinals.StandbyID);

        //            if (!this.sqlDR.IsDBNull(ordinals.IsOver35By7))
        //                item.IsOver35By7 = this.sqlDR.GetBoolean(ordinals.IsOver35By7);

        //            results.Add(item);
        //        }

        //    }
        //    catch (Exception)
        //    {
        //        throw;
        //    }

        //    return results;
        //}

        public async Task<long> GetLegalityGetRunId(string baseCD, string processingDate)
        {
            long runId = 0;
            try
            {
                this.InitalizeGetLegalityGetRunId();

                this.SqlCommand.Parameters["@Base"].Value = baseCD;
                this.SqlCommand.Parameters["@ProcessingDate"].Value = processingDate;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityGetRunIdOrdinal ordinals = new LegalityGetRunIdOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    if (!this.sqlDR.IsDBNull(ordinals.RunID))
                        runId = this.sqlDR.GetInt64(ordinals.RunID);

                }

            }
            catch (Exception)
            {
                throw;
            }

            return runId;
        }

        public async Task<List<AllContextualMappedRulesDTO>> GetAllContextualMappedRules(string applicationType)
        {
            List<AllContextualMappedRulesDTO> results = new List<AllContextualMappedRulesDTO>();
            try
            {
                this.InitalizeGetAllContextualMappedRules();

                this.SqlCommand.Parameters["@AppType"].Value = applicationType;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                AllContextualMappedRulesOrdinal ordinals = new AllContextualMappedRulesOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    AllContextualMappedRulesDTO item = new AllContextualMappedRulesDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.RuleTypeID))
                        item.RuleTypeID = this.sqlDR.GetInt64(ordinals.RuleTypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.RuleTypeName))
                        item.RuleTypeName = this.sqlDR.GetString(ordinals.RuleTypeName);

                    if (!this.sqlDR.IsDBNull(ordinals.RuleID))
                        item.RuleID = this.sqlDR.GetInt64(ordinals.RuleID);

                    if (!this.sqlDR.IsDBNull(ordinals.RuleName))
                        item.RuleName = this.sqlDR.GetString(ordinals.RuleName);

                    if (!this.sqlDR.IsDBNull(ordinals.RuleClass))
                        item.RuleClass = this.sqlDR.GetString(ordinals.RuleClass);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        //TODO: Not needed. Check again
        public async Task<List<WaiverTypeDTO>> getWaiverType()
        {
            List<WaiverTypeDTO> results = new List<WaiverTypeDTO>();
            try
            {
                this.InitalizegetWaiverType();

                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                WaiverTypeOrdinal ordinals = new WaiverTypeOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    WaiverTypeDTO item = new WaiverTypeDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeID))
                        item.WaiverTypeID = this.sqlDR.GetInt32(ordinals.WaiverTypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeDescription))
                        item.WaiverTypeDescription = this.sqlDR.GetString(ordinals.WaiverTypeDescription);

                    if (!this.sqlDR.IsDBNull(ordinals.IsActive))
                        item.IsActive = this.sqlDR.GetBoolean(ordinals.IsActive);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<ReservesCrewSequenceLegalitybyRunIdDTO>> getReservesCrewSequenceLegalitybyRunId(long runId)
        {
            List<ReservesCrewSequenceLegalitybyRunIdDTO> results = new List<ReservesCrewSequenceLegalitybyRunIdDTO>();
            try
            {
                this.InitalizegetReservesCrewSequenceLegalitybyRunId();

                this.SqlCommand.Parameters["@RunId"].Value = runId;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                ReservesCrewSequenceLegalitybyRunIdOrdinal ordinals = new ReservesCrewSequenceLegalitybyRunIdOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    ReservesCrewSequenceLegalitybyRunIdDTO item = new ReservesCrewSequenceLegalitybyRunIdDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewSequenceLegalityID))
                        item.ReservesCrewSequenceLegalityID = this.sqlDR.GetInt64(ordinals.ReservesCrewSequenceLegalityID);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberID))
                        item.ReservesCrewMemberID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.RunID))
                        item.RunID = this.sqlDR.GetInt64(ordinals.RunID);

                    if (!this.sqlDR.IsDBNull(ordinals.SequencePositionDetailsID))
                        item.SequencePositionDetailsID = this.sqlDR.GetInt64(ordinals.SequencePositionDetailsID);

                    if (!this.sqlDR.IsDBNull(ordinals.StandByID))
                        item.StandByID = this.sqlDR.GetInt32(ordinals.StandByID);

                    if (!this.sqlDR.IsDBNull(ordinals.IsOver35By7))
                        item.IsOver35By7 = this.sqlDR.GetBoolean(ordinals.IsOver35By7);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<ReservesCrewSequenceLegalitybyRunIdDTO>> getReservesLegalityVolunteerList(long runId)
        {
            List<ReservesCrewSequenceLegalitybyRunIdDTO> results = new List<ReservesCrewSequenceLegalitybyRunIdDTO>();
            try
            {
                this.InitalizegetReservesLegalityVolunteerList();

                this.SqlCommand.Parameters["@RunId"].Value = runId;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                ReservesCrewSequenceLegalitybyRunIdOrdinal ordinals = new ReservesCrewSequenceLegalitybyRunIdOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    ReservesCrewSequenceLegalitybyRunIdDTO item = new ReservesCrewSequenceLegalitybyRunIdDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewSequenceLegalityID))
                        item.ReservesCrewSequenceLegalityID = this.sqlDR.GetInt64(ordinals.ReservesCrewSequenceLegalityID);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewMemberID))
                        item.ReservesCrewMemberID = this.sqlDR.GetInt64(ordinals.ReservesCrewMemberID);

                    if (!this.sqlDR.IsDBNull(ordinals.RunID))
                        item.RunID = this.sqlDR.GetInt64(ordinals.RunID);

                    if (!this.sqlDR.IsDBNull(ordinals.SequencePositionDetailsID))
                        item.SequencePositionDetailsID = this.sqlDR.GetInt64(ordinals.SequencePositionDetailsID);

                    if (!this.sqlDR.IsDBNull(ordinals.StandByID))
                        item.StandByID = this.sqlDR.GetInt32(ordinals.StandByID);

                    if (!this.sqlDR.IsDBNull(ordinals.IsOver35By7))
                        item.IsOver35By7 = this.sqlDR.GetBoolean(ordinals.IsOver35By7);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<RapCodeDTO>> getRapCode()
        {
            List<RapCodeDTO> results = new List<RapCodeDTO>();
            try
            {
                this.InitalizegetRapCode();

                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                RapCodeOrdinal ordinals = new RapCodeOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    RapCodeDTO item = new RapCodeDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.RapCodeID))
                        item.RapCodeID = this.sqlDR.GetInt32(ordinals.RapCodeID);

                    if (!this.sqlDR.IsDBNull(ordinals.RapCode))
                        item.RapCode = this.sqlDR.GetString(ordinals.RapCode);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<CoTerminalDTO>> getBaseWithCoTerminals()
        {
            List<CoTerminalDTO> results = new List<CoTerminalDTO>();
            try
            {
                this.InitalizegetCoTerminals();

                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                CoTerminalOrdinal ordinals = new CoTerminalOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    CoTerminalDTO item = new CoTerminalDTO();
                    if (!this.sqlDR.IsDBNull(ordinals.BaseCD))
                        item.BaseCD = this.sqlDR.GetString(ordinals.BaseCD);

                    if(!this.sqlDR.IsDBNull(ordinals.BaseID))
                        item.BaseID = this.sqlDR.GetInt32(ordinals.BaseID);

                    if (!this.sqlDR.IsDBNull(ordinals.BaseName))
                        item.BaseName = this.sqlDR.GetString(ordinals.BaseName);
                    
                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        //TODO: Check if needed for ROTD -- Not needed
        public async Task<List<QLARulesWaiverDetailsDTO>> getQLARulesWaiverDetails()
        {
            List<QLARulesWaiverDetailsDTO> results = new List<QLARulesWaiverDetailsDTO>();
            try
            {
                this.InitalizegetQLARulesWaiverDetails();

                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                QLARulesWaiverDetailsOrdinal ordinals = new QLARulesWaiverDetailsOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    QLARulesWaiverDetailsDTO item = new QLARulesWaiverDetailsDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.QLARulesWaiverDetailsID))
                        item.QLARulesWaiverDetailsID = this.sqlDR.GetInt64(ordinals.QLARulesWaiverDetailsID);

                    if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeID))
                        item.WaiverTypeID = this.sqlDR.GetInt32(ordinals.WaiverTypeID);

                    if (!this.sqlDR.IsDBNull(ordinals.QLARuleID))
                        item.QLARuleID = this.sqlDR.GetInt64(ordinals.QLARuleID);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<QLARulesDTO>> getQLARules()
        {
            List<QLARulesDTO> results = new List<QLARulesDTO>();
            try
            {
                this.InitalizegetQLARules();

                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                QLARulesOrdinal ordinals = new QLARulesOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    QLARulesDTO item = new QLARulesDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.QLARuleID))
                        item.QLARuleID = this.sqlDR.GetInt64(ordinals.QLARuleID);

                    if (!this.sqlDR.IsDBNull(ordinals.QLARule))
                        item.QLARule = this.sqlDR.GetString(ordinals.QLARule);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<PostQLAStatesDTO>> getPostQLAStates(long runId)
        {
            List<PostQLAStatesDTO> results = new List<PostQLAStatesDTO>();
            try
            {
                this.InitalizegetPostQLAStates();

                this.SqlCommand.Parameters["@RunId"].Value = runId;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                PostQLAStatesOrdinal ordinals = new PostQLAStatesOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    PostQLAStatesDTO item = new PostQLAStatesDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.PostQLAStateID))
                        item.PostQLAStateID = this.sqlDR.GetInt64(ordinals.PostQLAStateID);

                    if (!this.sqlDR.IsDBNull(ordinals.PostQLAState))
                        item.PostQLAState = this.sqlDR.GetString(ordinals.PostQLAState);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityQLASupportingDataonlyDTO>> getLegalityQLASupportingDataonly(long runId)
        {
            List<LegalityQLASupportingDataonlyDTO> results = new List<LegalityQLASupportingDataonlyDTO>();
            try
            {
                this.InitalizegetLegalityQLASupportingDataonly();

                this.SqlCommand.Parameters["@RunId"].Value = runId;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityQLASupportingDataonlyOrdinal ordinals = new LegalityQLASupportingDataonlyOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityQLASupportingDataonlyDTO item = new LegalityQLASupportingDataonlyDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.RunId))
                        item.RunId = this.sqlDR.GetInt64(ordinals.RunId);

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeId))
                        item.EmployeeId = this.sqlDR.GetInt64(ordinals.EmployeeId);

                    if (!this.sqlDR.IsDBNull(ordinals.AffectedType))
                        item.AffectedType = this.sqlDR.GetString(ordinals.AffectedType);

                    if (!this.sqlDR.IsDBNull(ordinals.ContractMonth))
                        item.ContractMonth = this.sqlDR.GetString(ordinals.ContractMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityId))
                        item.ActivityId = this.sqlDR.GetInt32(ordinals.ActivityId);

                    if (!this.sqlDR.IsDBNull(ordinals.Activitycode))
                        item.Activitycode = this.sqlDR.GetString(ordinals.Activitycode);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityType))
                        item.ActivityType = this.sqlDR.GetString(ordinals.ActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityOriginationDate))
                        item.ActivityOriginationDate = this.sqlDR.GetString(ordinals.ActivityOriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.PositionCode))
                        item.PositionCode = this.sqlDR.GetString(ordinals.PositionCode);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupContractMonth))
                        item.PickupContractMonth = this.sqlDR.GetString(ordinals.PickupContractMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityId))
                        item.PickupActivityId = this.sqlDR.GetInt32(ordinals.PickupActivityId);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityCode))
                        item.PickupActivityCode = this.sqlDR.GetString(ordinals.PickupActivityCode);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityType))
                        item.PickupActivityType = this.sqlDR.GetString(ordinals.PickupActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityOriginationDate))
                        item.PickupActivityOriginationDate = this.sqlDR.GetString(ordinals.PickupActivityOriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupPositionCode))
                        item.PickupPositionCode = this.sqlDR.GetString(ordinals.PickupPositionCode);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityReportDateTime))
                        item.ActivityReportDateTime = this.sqlDR.GetDateTime(ordinals.ActivityReportDateTime);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityQLASupportingDataVolunteerDTO>> getLegalityQLASupportingDataVolunteer(long runId)
        {
            List<LegalityQLASupportingDataVolunteerDTO> results = new List<LegalityQLASupportingDataVolunteerDTO>();
            try
            {
                this.InitalizegetLegalityQLASupportingData_volunteer();

                this.SqlCommand.Parameters["@RunId"].Value = runId;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityQLASupportingDataVolunteerOrdinal ordinals = new LegalityQLASupportingDataVolunteerOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityQLASupportingDataVolunteerDTO item = new LegalityQLASupportingDataVolunteerDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.RunId))
                        item.RunId = this.sqlDR.GetInt64(ordinals.RunId);

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeId))
                        item.EmployeeId = this.sqlDR.GetInt64(ordinals.EmployeeId);

                    if (!this.sqlDR.IsDBNull(ordinals.AffectedType))
                        item.AffectedType = this.sqlDR.GetString(ordinals.AffectedType);

                    if (!this.sqlDR.IsDBNull(ordinals.ContractMonth))
                        item.ContractMonth = this.sqlDR.GetString(ordinals.ContractMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityId))
                        item.ActivityId = this.sqlDR.GetInt32(ordinals.ActivityId);

                    if (!this.sqlDR.IsDBNull(ordinals.Activitycode))
                        item.Activitycode = this.sqlDR.GetString(ordinals.Activitycode);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityType))
                        item.ActivityType = this.sqlDR.GetString(ordinals.ActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityOriginationDate))
                        item.ActivityOriginationDate = this.sqlDR.GetString(ordinals.ActivityOriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.PositionCode))
                        item.PositionCode = this.sqlDR.GetString(ordinals.PositionCode);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupContractMonth))
                        item.PickupContractMonth = this.sqlDR.GetString(ordinals.PickupContractMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityId))
                        item.PickupActivityId = this.sqlDR.GetInt32(ordinals.PickupActivityId);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityCode))
                        item.PickupActivityCode = this.sqlDR.GetString(ordinals.PickupActivityCode);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityType))
                        item.PickupActivityType = this.sqlDR.GetString(ordinals.PickupActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityOriginationDate))
                        item.PickupActivityOriginationDate = this.sqlDR.GetString(ordinals.PickupActivityOriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupPositionCode))
                        item.PickupPositionCode = this.sqlDR.GetString(ordinals.PickupPositionCode);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityReportDateTime))
                        item.ActivityReportDateTime = this.sqlDR.GetDateTime(ordinals.ActivityReportDateTime);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityQLASupportingDataNonVolunteerStandByDTO>> getLegalityQLASupportingDataNonVolunteerStandBy(long runId)
        {
            List<LegalityQLASupportingDataNonVolunteerStandByDTO> results = new List<LegalityQLASupportingDataNonVolunteerStandByDTO>();
            try
            {
                this.InitalizegetLegalityQLASupportingData_NonVolunteerStandBy();

                this.SqlCommand.Parameters["@RunId"].Value = runId;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityQLASupportingDataNonVolunteerStandByOrdinal ordinals = new LegalityQLASupportingDataNonVolunteerStandByOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityQLASupportingDataNonVolunteerStandByDTO item = new LegalityQLASupportingDataNonVolunteerStandByDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.RunId))
                        item.RunId = this.sqlDR.GetInt64(ordinals.RunId);

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeId))
                        item.EmployeeId = this.sqlDR.GetInt64(ordinals.EmployeeId);

                    if (!this.sqlDR.IsDBNull(ordinals.AffectedType))
                        item.AffectedType = this.sqlDR.GetString(ordinals.AffectedType);

                    if (!this.sqlDR.IsDBNull(ordinals.ContractMonth))
                        item.ContractMonth = this.sqlDR.GetString(ordinals.ContractMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityId))
                        item.ActivityId = this.sqlDR.GetInt32(ordinals.ActivityId);

                    if (!this.sqlDR.IsDBNull(ordinals.Activitycode))
                        item.Activitycode = this.sqlDR.GetString(ordinals.Activitycode);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityType))
                        item.ActivityType = this.sqlDR.GetString(ordinals.ActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityOriginationDate))
                        item.ActivityOriginationDate = this.sqlDR.GetString(ordinals.ActivityOriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.PositionCode))
                        item.PositionCode = this.sqlDR.GetString(ordinals.PositionCode);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupContractMonth))
                        item.PickupContractMonth = this.sqlDR.GetString(ordinals.PickupContractMonth);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityId))
                        item.PickupActivityId = this.sqlDR.GetInt32(ordinals.PickupActivityId);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityCode))
                        item.PickupActivityCode = this.sqlDR.GetString(ordinals.PickupActivityCode);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityType))
                        item.PickupActivityType = this.sqlDR.GetString(ordinals.PickupActivityType);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupActivityOriginationDate))
                        item.PickupActivityOriginationDate = this.sqlDR.GetString(ordinals.PickupActivityOriginationDate);

                    if (!this.sqlDR.IsDBNull(ordinals.PickupPositionCode))
                        item.PickupPositionCode = this.sqlDR.GetString(ordinals.PickupPositionCode);

                    if (!this.sqlDR.IsDBNull(ordinals.ActivityReportDateTime))
                        item.ActivityReportDateTime = this.sqlDR.GetDateTime(ordinals.ActivityReportDateTime);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<LegalityPhasesDTO>> getLegalityPhases()
        {
            List<LegalityPhasesDTO> results = new List<LegalityPhasesDTO>();
            try
            {
                this.InitalizegetLegalityPhases();

                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityPhasesOrdinal ordinals = new LegalityPhasesOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityPhasesDTO item = new LegalityPhasesDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.LegalityPhase))
                        item.LegalityPhase = this.sqlDR.GetString(ordinals.LegalityPhase);

                    if (!this.sqlDR.IsDBNull(ordinals.LegalityPhaseID))
                        item.LegalityPhaseID = this.sqlDR.GetInt64(ordinals.LegalityPhaseID);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        //TODO: Check with Shankar on the data
        public async Task<List<ContractSectionsDTO>> getContractSections()
        {
            List<ContractSectionsDTO> results = new List<ContractSectionsDTO>();
            try
            {
                this.InitalizegetContractSections();

                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                ContractSectionsOrdinal ordinals = new ContractSectionsOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    ContractSectionsDTO item = new ContractSectionsDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.ContractSectionsID))
                        item.ContractSectionsID = this.sqlDR.GetInt64(ordinals.ContractSectionsID);

                    if (!this.sqlDR.IsDBNull(ordinals.ContractSection))
                        item.ContractSection = this.sqlDR.GetString(ordinals.ContractSection);

                    if (!this.sqlDR.IsDBNull(ordinals.DisplayName))
                        item.DisplayName = this.sqlDR.GetString(ordinals.DisplayName);

                    if (!this.sqlDR.IsDBNull(ordinals.ReportType))
                        item.ReportType = this.sqlDR.GetString(ordinals.ReportType);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        //public async Task<List<LegalitiesWaiverTypeDTO>> GetLegalitiesWaiverType(<ParameterList>)
        //{
        //    List<LegalitiesWaiverTypeDTO> results = new List<LegalitiesWaiverTypeDTO>();
        //    try
        //    {
        //        this.InitalizeGetLegalitiesWaiverType();



        //        //Execute the Stored Procedure
        //        this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

        //        //Declare Ordinal stucts to hold the column position while reading data.
        //        LegalitiesWaiverTypeOrdinal ordinals = new LegalitiesWaiverTypeOrdinal();

        //        while (this.sqlDR.Read()) //For each tCostConfig Record returned
        //        {

        //            //Determine the the ordinals for the select statement have been assigned.
        //            if (!ordinals.Initialized)
        //            {
        //                //If not then assign them.
        //                ordinals.Initialize(this.sqlDR);
        //            }

        //            LegalitiesWaiverTypeDTO item = new LegalitiesWaiverTypeDTO();

        //            if (!this.sqlDR.IsDBNull(ordinals.QLARule))
        //                item.QLARule = this.sqlDR.GetString(ordinals.QLARule);

        //            if (!this.sqlDR.IsDBNull(ordinals.WaiverTypeDescription))
        //                item.WaiverTypeDescription = this.sqlDR.GetString(ordinals.WaiverTypeDescription);





        //            results.Add(item);
        //        }

        //    }
        //    catch (Exception)
        //    {
        //        throw;
        //    }

        //    return results;
        //}

        public async Task<List<LegalityReservesCrewMemberLanguageDetailsDTO>> getLegalityReservesCrewMemberLanguageDetails(long runId)
        {
            List<LegalityReservesCrewMemberLanguageDetailsDTO> results = new List<LegalityReservesCrewMemberLanguageDetailsDTO>();
            try
            {
                this.InitalizeLegalityReservesCrewMemberLanguageDetails();

                this.SqlCommand.Parameters["@RunID"].Value = runId;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                LegalityReservesCrewMemberLanguageDetailsOrdinal ordinals = new LegalityReservesCrewMemberLanguageDetailsOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    LegalityReservesCrewMemberLanguageDetailsDTO item = new LegalityReservesCrewMemberLanguageDetailsDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.LanguageID))
                        item.LanguageID = this.sqlDR.GetInt32(ordinals.LanguageID);

                    if (!this.sqlDR.IsDBNull(ordinals.IsExcluded))
                        item.IsExcluded = this.sqlDR.GetBoolean(ordinals.IsExcluded);

                    if (!this.sqlDR.IsDBNull(ordinals.EmployeeID))
                        item.EmployeeID = this.sqlDR.GetInt64(ordinals.EmployeeID);


                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public async Task<List<ReservesLegalityQLAListDTO>> GetReservesLegalityQLAList(long runId, int phaseId)
        {
            List<ReservesLegalityQLAListDTO> results = new List<ReservesLegalityQLAListDTO>();
            try
            {
                this.InitalizeReservesLegalityQLAList();

                this.SqlCommand.Parameters["@RunID"].Value = runId;
                this.SqlCommand.Parameters["@PhaseID"].Value = phaseId;


                //Execute the Stored Procedure
                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                ReservesLegalityQLAListOrdinal ordinals = new ReservesLegalityQLAListOrdinal();

                while (this.sqlDR.Read()) //For each tCostConfig Record returned
                {

                    //Determine the the ordinals for the select statement have been assigned.
                    if (!ordinals.Initialized)
                    {
                        //If not then assign them.
                        ordinals.Initialize(this.sqlDR);
                    }

                    ReservesLegalityQLAListDTO item = new ReservesLegalityQLAListDTO();

                    if (!this.sqlDR.IsDBNull(ordinals.LegalityQLADetailsID))
                        item.LegalityQLADetailsID = this.sqlDR.GetInt64(ordinals.LegalityQLADetailsID);

                    if (!this.sqlDR.IsDBNull(ordinals.IsLegal))
                        item.IsLegal = this.sqlDR.GetBoolean(ordinals.IsLegal);

                    if (!this.sqlDR.IsDBNull(ordinals.IsContractual))
                        item.IsContractual = this.sqlDR.GetBoolean(ordinals.IsContractual);

                    if (!this.sqlDR.IsDBNull(ordinals.IsQualified))
                        item.IsQualified = this.sqlDR.GetBoolean(ordinals.IsQualified);

                    if (!this.sqlDR.IsDBNull(ordinals.Request))
                        item.Request = this.sqlDR.GetString(ordinals.Request);

                    if (!this.sqlDR.IsDBNull(ordinals.Response))
                        item.Response = this.sqlDR.GetString(ordinals.Response);

                    if (!this.sqlDR.IsDBNull(ordinals.CreatedBy))
                        item.CreatedBy = this.sqlDR.GetInt64(ordinals.CreatedBy);

                    if (!this.sqlDR.IsDBNull(ordinals.CreateDate))
                        item.CreateDate = this.sqlDR.GetDateTime(ordinals.CreateDate);

                    if (!this.sqlDR.IsDBNull(ordinals.UpdatedBy))
                        item.UpdatedBy = this.sqlDR.GetInt64(ordinals.UpdatedBy);

                    if (!this.sqlDR.IsDBNull(ordinals.UpdatedDate))
                        item.UpdatedDate = this.sqlDR.GetDateTime(ordinals.UpdatedDate);

                    if (!this.sqlDR.IsDBNull(ordinals.ReservesCrewSequenceLegalityID))
                        item.ReservesCrewSequenceLegalityID = this.sqlDR.GetInt64(ordinals.ReservesCrewSequenceLegalityID);

                    if (!this.sqlDR.IsDBNull(ordinals.LegalityPhaseID))
                        item.LegalityPhaseID = this.sqlDR.GetInt32(ordinals.LegalityPhaseID);

                    if (!this.sqlDR.IsDBNull(ordinals.FosRAP))
                        item.FosRAP = this.sqlDR.GetInt32(ordinals.FosRAP);

                    if (!this.sqlDR.IsDBNull(ordinals.IsCurrentRAP))
                        item.IsCurrentRAP = this.sqlDR.GetBoolean(ordinals.IsCurrentRAP);

                    if (!this.sqlDR.IsDBNull(ordinals.LanguageID))
                        item.LanguageID = this.sqlDR.GetInt32(ordinals.LanguageID);

                    results.Add(item);
                }

            }
            catch (Exception)
            {
                throw;
            }

            return results;
        }

        public void SaveLegalityQLAMessage(long runId, long legalityPhaseId, string rawRequest, string rawResponse, long updatedById, DateTime updatedDate)
        {
            this.InitializeSaveLegalityMessage();
            this.SqlCommand.Parameters["@RunID"].Value = runId;
            this.SqlCommand.Parameters["@LegalityPhaseID"].Value = legalityPhaseId;
            this.SqlCommand.Parameters["@QLARequest"].Value = rawRequest;
            this.SqlCommand.Parameters["@QLAResponse"].Value = rawResponse;
            this.SqlCommand.Parameters["@CreatedBy"].Value = updatedById;
            this.SqlCommand.Parameters["@CreatedDate"].Value = updatedDate;

            this.SqlCommand.ExecuteNonQuery();
        }

        public async Task<bool> UpdateAwardProgressStepAsInProgressDaily(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime startDateTime)
        {
            bool isSuccessResponse = false;
            using (SqlConnection connection = new SqlConnection(_connectionstring))
            {
                await connection.OpenAsync();
                using (SqlCommand command = InitializeUpdateAwardProgressStepAsInProgressDailyCommand(baseCode, processingDate, awardProgressStepID, startDateTime))
                {
                    command.Connection = connection;
                    var rowsAffected = await command.ExecuteNonQueryAsync().ConfigureAwait(false);
                    isSuccessResponse = rowsAffected > 0;
                }
            }
            return isSuccessResponse;
        }

        public async Task<bool> UpdateAwardProgressStepAsCompleteDaily(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime endDateTime)
        {
            bool isSuccessResponse = false;
            using (SqlConnection connection = new SqlConnection(_connectionstring))
            {
                await connection.OpenAsync();
                using (SqlCommand command = InitializeUpdateAwardProgressStepAsCompleteDailyCommand(baseCode, processingDate, awardProgressStepID, endDateTime))
                {
                    command.Connection = connection;
                    var rowsAffected = await command.ExecuteNonQueryAsync().ConfigureAwait(false);
                    isSuccessResponse = rowsAffected > 0;
                }
            }
            return isSuccessResponse;
        }

        public async Task<bool> UpdateAwardProgressStepAsErrorDaily(string baseCode, DateTime processingDate, long awardProgressStepID)
        {
            //this.InitializeUpdateAwardProgressStepAsErrorDaily(baseCode, processingDate, awardProgressStepID);
            //await this.SqlCommand.ExecuteNonQueryAsync();
            bool isSuccessResponse = false;
            using (SqlConnection connection = new SqlConnection(_connectionstring))
            {
                await connection.OpenAsync();

                using (SqlCommand command = InitializeUpdateAwardProgressStepAsErrorDaily(baseCode, processingDate, awardProgressStepID))
                {
                    command.Connection = connection;
                    
                    // Execute the command asynchronously using ExecuteNonQueryAsync
                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    isSuccessResponse = rowsAffected > 0;
                }
            }

            return isSuccessResponse;
        }

        private SqlCommand InitializeUpdateAwardProgressStepAsErrorDaily(string baseCode, DateTime processingDate, long awardProgressStepID)
        {
            SqlCommand cmd = new SqlCommand
            {
                CommandType = CommandType.StoredProcedure
            };
            cmd.CommandText = AWARDPROGRESSUPDATEASERROR_SP;
            cmd.Parameters.Clear();

            // Add the parameters for the stored procedure here
            cmd.Parameters.Add(new SqlParameter("@BaseCode", SqlDbType.VarChar, 20) { Value = baseCode });
            cmd.Parameters.Add(new SqlParameter("@ProcessingDate", SqlDbType.DateTime) { Value = processingDate });
            cmd.Parameters.Add(new SqlParameter("@AwardProgressStepID", SqlDbType.BigInt) { Value = awardProgressStepID });

            return cmd;
        }

        private SqlCommand InitializeUpdateAwardProgressStepAsInProgressDailyCommand(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime startDateTime)
        {
            SqlCommand cmd = new SqlCommand
            {
                CommandType = CommandType.StoredProcedure,
                CommandText = AWARDPROGRESSUPDATEASINPREGESS_SP
            };
            cmd.Parameters.Clear();
            cmd.Parameters.Add(new SqlParameter("@BaseCode", SqlDbType.VarChar, 20) { Value = baseCode });
            cmd.Parameters.Add(new SqlParameter("@ProcessingDate", SqlDbType.DateTime) { Value = processingDate });
            cmd.Parameters.Add(new SqlParameter("@AwardProgressStepID", SqlDbType.BigInt) { Value = awardProgressStepID });
            cmd.Parameters.Add(new SqlParameter("@startDateTime", SqlDbType.DateTime) { Value = startDateTime });
            return cmd;
        }

        private SqlCommand InitializeUpdateAwardProgressStepAsCompleteDailyCommand(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime endDateTime)
        {
            SqlCommand cmd = new SqlCommand
            {
                CommandType = CommandType.StoredProcedure,
                CommandText = AWARDPROGRESSUPDATEASCOMPLETE_SP
            };
            cmd.Parameters.Clear();
            cmd.Parameters.Add(new SqlParameter("@BaseCode", SqlDbType.VarChar, 20) { Value = baseCode });
            cmd.Parameters.Add(new SqlParameter("@ProcessingDate", SqlDbType.DateTime) { Value = processingDate });
            cmd.Parameters.Add(new SqlParameter("@AwardProgressStepID", SqlDbType.BigInt) { Value = awardProgressStepID });
            cmd.Parameters.Add(new SqlParameter("@endDateTime", SqlDbType.DateTime) { Value = endDateTime });
            return cmd;
        }


        private void InitalizeReservesLegalityQLAList()
        {
            this.SqlCommand.CommandText = GETRESERVESLEGALITYQLALIST_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@PhaseID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }

        private void InitalizeLegalityReservesCrewMemberLanguageDetails()
        {
            this.SqlCommand.CommandText = GETLEGALITYRESERVESCREWMEMBERLANGUAGEDETAILS_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);



        }
        private void InitalizeGetLegalitiesWaiverType()
        {
            this.SqlCommand.CommandText = GETLEGALITIESWAIVERTYPE_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;



        }
        private void InitalizegetContractSections()
        {
            this.SqlCommand.CommandText = GETCONTRACTSECTIONS_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;



        }
        private void InitalizegetLegalityPhases()
        {
            this.SqlCommand.CommandText = GETLEGALITYPHASES_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;



        }
        private void InitalizegetLegalityQLASupportingData_NonVolunteerStandBy()
        {
            this.SqlCommand.CommandText = GETLEGALITYQLASUPPORTINGDATA_NONVOLUNTEERSTANDBY_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunId", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);



        }
        private void InitalizegetLegalityQLASupportingData_volunteer()
        {
            this.SqlCommand.CommandText = GETLEGALITYQLASUPPORTINGDATA_VOLUNTEER_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunId", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);



        }
        private void InitalizegetLegalityQLASupportingDataonly()
        {
            this.SqlCommand.CommandText = GETLEGALITYQLASUPPORTINGDATAONLY_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunId", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);



        }
        private void InitalizegetPostQLAStates()
        {
            this.SqlCommand.CommandText = GETPOSTQLASTATES_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunId", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);



        }
        private void InitalizegetQLARules()
        {
            this.SqlCommand.CommandText = GETQLARULES_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;



        }
        private void InitalizegetQLARulesWaiverDetails()
        {
            this.SqlCommand.CommandText = GETQLARULESWAIVERDETAILS_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;



        }
        private void InitalizegetRapCode()
        {
            this.SqlCommand.CommandText = GETRAPCODE_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;



        }
        private void InitalizegetCoTerminals()
        {
            this.SqlCommand.CommandText = GETCOTERMINAL_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;



        }
        private void InitalizegetReservesCrewSequenceLegalitybyRunId()
        {
            this.SqlCommand.CommandText = GETRESERVESCREWSEQUENCELEGALITYBYRUNID_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunId", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);



        }
        private void InitalizegetReservesLegalityVolunteerList()
        {
            this.SqlCommand.CommandText = GETRESERVESRESERVESLEGALITYVOLUNTEERLIST_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunId", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);



        }
        private void InitalizegetWaiverType()
        {
            this.SqlCommand.CommandText = GETWAIVERTYPE_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;



        }
        private void InitalizeGetAllContextualMappedRules()
        {
            this.SqlCommand.CommandText = GETALLCONTEXTUALMAPPEDRULES_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@AppType", SqlDbType.VarChar);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityGetRunId()
        {
            this.SqlCommand.CommandText = GETLEGALITYGETRUNID_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@Base", SqlDbType.VarChar);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@ProcessingDate", SqlDbType.VarChar);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityROTDPhaseDetail()
        {
            this.SqlCommand.CommandText = GETRESERVESCREWSEQUENCELEGALITYLIST_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityQLASupportingData()
        {
            this.SqlCommand.CommandText = GETLEGALITYQLASUPPORTINGDATA_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunId", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityCrewSequence()
        {
            this.SqlCommand.CommandText = GETLEGALITYCREWSEQUENCE_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetBaseProcessingDate()
        {
            this.SqlCommand.CommandText = GETLEGALITYBASEPROCESSINGDATEBYRUNID_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityCrewSequenceByRunID()
        {
            this.SqlCommand.CommandText = GETLEGALITYCREWSEQUENCEBYRUNID_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalitySequenceLanguageDetails()
        {
            this.SqlCommand.CommandText = GETLEGALITYSEQUENCELANGUAGEDETAILS_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityBidStatus()
        {
            this.SqlCommand.CommandText = GETLEGALITYRESERVESCREWMEMBERBIDSTATUS_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityPostQLAMapping()
        {
            this.SqlCommand.CommandText = GETLEGALITYPOSTQLAMAPPING_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@runId", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityProcessAggressiveBidCrewWaiver()
        {
            this.SqlCommand.CommandText = GETLEGALITYPROCESSAGGRESSIVEBIDCREWWAIVER_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@runId", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityProcessBidCrewWaiverSupportingData()
        {
            this.SqlCommand.CommandText = GETLEGALITYBIDCREWWAIVERSUPPORTINGDATA_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityProcessBidCrewWaiver()
        {
            this.SqlCommand.CommandText = GETLEGALITYPROCESSBIDCREWWAIVER_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@runId", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityBidCrewWaiver()
        {
            this.SqlCommand.CommandText = GETLEGALITYBIDCREWWAIVER_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalitySequence()
        {
            this.SqlCommand.CommandText = GETLEGALITYSEQUENCE_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityCrewMemberActivity()
        {
            this.SqlCommand.CommandText = GETLEGALITYRESERVESCREWMEMBERACTIVITY_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityReservesCrewMembers()
        {
            this.SqlCommand.CommandText = GETLEGALITYRESERVESCREWMEMBERS_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetLegalityStandBy()
        {
            this.SqlCommand.CommandText = GETLEGALITYSTANDBY_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetReservesCrewSequenceLegalityContractDetails()
        {
            this.SqlCommand.CommandText = GETRESERVESCREWSEQUENCELEGALITYCONTRACTDETAILS_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@PhaseID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@RowspPage", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@PageNumber", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
        private void InitalizeGetQLARuleList()
        {
            this.SqlCommand.CommandText = GETQLARULELIST_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@PhaseID", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@RowspPage", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@PageNumber", SqlDbType.Int);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }

        private void InitializeSaveLegalityMessage()
        {
            this.SqlCommand.CommandText = SAVELEGALITYQLAMESSAGE_SP;

            this.SqlCommand.Parameters.Clear();

            SqlParameter newParam;

            newParam = new SqlParameter("@RunID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@LegalityPhaseID", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@QLARequest", SqlDbType.VarChar);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@QLAResponse", SqlDbType.VarChar);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@CreatedBy", SqlDbType.BigInt);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

            newParam = new SqlParameter("@CreatedDate", SqlDbType.DateTime);
            newParam.Direction = ParameterDirection.Input;
            this.SqlCommand.Parameters.Add(newParam);

        }
    }
}
