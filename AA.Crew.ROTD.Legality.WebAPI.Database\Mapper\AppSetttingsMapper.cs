﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class AppSetttingsMapper : MapperBase<AppSettingsDTO, AppSettings>
    {
        public override AppSettings Map(AppSettingsDTO Dto)
        {
            try
            {
                return new AppSettings
                {
                    SettingName = Dto.SettingName,
                    SettingValues = Dto.SettingValues
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public override AppSettingsDTO Map(AppSettings element)
        {
            throw new NotImplementedException();
        }
    }
}
