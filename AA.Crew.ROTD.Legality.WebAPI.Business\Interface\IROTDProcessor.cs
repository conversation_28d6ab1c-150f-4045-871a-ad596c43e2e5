﻿using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BaseDate = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.BaseDate;
using Sequence = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Interface
{
    public interface IROTDProcessor
    {
        bool PossibleIsLegal { get; set; }
        string Base { get; set; }
        DateTime BidOriginationDate { get; set; }
        int RunId { get; set; }
        string RawRequest { get; set; }
        string RawResponse { get; set; }
        List<Model.BusinessObject.LegalityQLASupportingData> QLASupportingData { get; set; }
        List<ContextualResult> LegalityContextualResult { get; set; }
        List<getLegalityCrewSequence_Result> LegalityCrewSequence { get; set; }
        List<LegalityErrors> LegalityErrorsException { get; set; }
        void InitializeParameters(int phaseId, List<QLAResponse> lstQlaResponse, List<QLARequest> lstQlaRequest, List<QLARuleResult> lstQlaRuleResult, List<BidCrewWaiver> waiver, List<PostQLAMappingDetails> mappedDetails, int runID);
        ROTDPhase ExecuteProcessor(int subPhase, List<Sequence> sequence, List<Bid> bids, List<FlightAttendant> flightAttendant, List<Activity> faActivity, List<QLAResponse> qlaResponse, List<QLARequest> qlaRequest, List<QLARuleResult> lstQlaRuleResult, List<PostQLAMappingDetails> mappedDetails, List<BidCrewWaiver> crewmemberWaiver, BaseDate Base, List<StandBy> standby = null, List<RAPShifts> rapShifts = null);
        ROTDPhase CheckSequenceStandbyLegal(List<Sequence> sequence, List<Bid> bids, List<FlightAttendant> flightAttendant, List<Activity> faActivity, List<ContractMonth> contractMonthList, List<StandByCredit> standByCreditList, List<BidCrewWaiver> bidCrewWaiver, List<FALanguageDetails> falanguage, bool IsTheProcessInvokedFromDBUI, BaseDate Base, List<RAPShifts> RAPShift,bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShift, int runContextId, List<StandBy> standby = null);
        void SaveROTDLegality(List<Sequence> sequence, List<StandBy> standBy);
        RAPShifts FilterCurrentRAPShifts(Sequence sequence, StandBy standBy, List<RAPShifts> rapShifts, DateTime processingDate);
        ROTDPhaseDetail CreatePhaseDetail(Nullable<int> langID);
        ROTDPhaseDetail ContextualSpeakerQual(int subPhase, ROTDPhaseDetail phaseDetail, List<PostQLAMappingDetails> mappedPostDetails);
        string CheckWaiverAgainstRule(string rule, FlightAttendant fa, QLARequest req, long ruleStateId, List<BidCrewWaiver> lstWaiver, string waiverType, int fdCount, ref int waiverStatusId);
    }
}
