using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class RunStatu
    {
        public RunStatu()
        {
            this.Runs = new HashSet<Run>();
        }
    
        public long RunStatusID { get; set; }
        public string RunStatus { get; set; }
    
        public virtual ICollection<Run> Runs { get; set; }
    }
}
