﻿using AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.Factories;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using System.Data.Common;
using AA.Crew.ROTD.Legality.WebAPI.Database.Exceptions;
using System.Data.SqlClient;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Provider
{
    public class InterpretiveDataProvider : ProviderBase<IInterpretiveDataAdaptor>, IInterpretiveDataProvider
    {
        #region Constructor

        public InterpretiveDataProvider(IDataAdaptorFactory factory) : base()
        {
            this.Adaptor = factory.GetInterpretiveDataAdaptor();
        }

        #endregion Constructor    

        #region GetValues from DataBase
        
        public async Task<getLegalityBaseProcessingDateByRunID_Result> GetBaseProcessingDate(Int64 runId)
        {
            var response = new getLegalityBaseProcessingDateByRunID_Result();
            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 
                response = await this.Adaptor.GetBaseProcessingDate(runId);

            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure getLegalityBaseProcessingDateByRunID.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing GetBaseProcessingDate()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return response;
        }


        public async Task<List<Activity>> GetLegalityCrewMemberActivity(Int64 runId)
        {
            var response = new List<Activity>();
            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 
                

            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure getLegalityReservesCrewMemberActivity.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing GetLegalityCrewMemberActivity()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return response;
        }

        public async Task<List<FlightAttendantBidStatus>> GetLegalityBidStatus()
        {
            var lstBidStatus = new List<getLegalityReservesCrewMemberBidStatus_Result>();
            var fltBidStatus = new List<FlightAttendantBidStatus>();
            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure getLegalityReservesCrewMemberBidStatus.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing GetLegalityBidStatus()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return fltBidStatus;
        }

        public async Task<List<FALanguageDetails>> GetLegalityReservesCrewMemberLanguageDetails()
        {
            var lstFALangResult = new List<getLegalityReservesCrewMemberLanguageDetails_Result>();
            var lstFALanguageDetail = new List<FALanguageDetails>();
            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure getLegalityReservesCrewMemberLanguageDetails.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing GetLegalityReservesCrewMemberLanguageDetails()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }
            return lstFALanguageDetail;
        }

        public async Task<List<OperatingLanguage>> GetOperatingLanguagesForRun(long runID)
        {
            List<OperatingLanguage> operatingLanguages = new List<OperatingLanguage>();
            
            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure [RAPA].[GetOperatingLanguages].", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing GetOperatingLanguagesForRun()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return operatingLanguages;
        }

        public async Task<List<getLegalityReservesCrewMembers_Result>> GetLegalityReservesCrewMembers()
        {
            var lstCrewmember = new List<getLegalityReservesCrewMembers_Result>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure [RAPA].[GetOperatingLanguages].", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing GetOperatingLanguagesForRun()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return lstCrewmember;
        }

        public async Task<List<FlightAttendant>> getReservesCrewMemberDetails(List<Activity> lstFaActivity, long runId)
        {

            var tempBidStatus = GetLegalityBidStatus().Result;
            var tempCrememberLanguages = GetLegalityReservesCrewMemberLanguageDetails().Result;
            var operatingLanguages = GetOperatingLanguagesForRun(runId).Result;
            var lstFlightAttendant = (from lst in GetLegalityReservesCrewMembers().Result
                                  select new FlightAttendant
                                  {
                                      Name = lst.Name,
                                      EmployeeNumber = Convert.ToInt32(lst.EmployeeID),
                                      SeniorityNumber = lst.SeniorityNumber == null ? 0 : Convert.ToInt32(lst.SeniorityNumber),
                                      AvailableDays = lst.AVLDays == null ? 0 : Convert.ToInt32(lst.AVLDays),
                                      IsVolunteer = lst.IsVolunteer == null ? false : Convert.ToBoolean(lst.IsVolunteer),
                                      IsSick = lst.IsSick == null ? false : Convert.ToBoolean(lst.IsSick),
                                      ReservesCrewMemberID = lst.ReservesCrewMemberID,
                                      ASGSequence = lst.ASGSequence == null ? 0 : Convert.ToInt32(lst.ASGSequence),
                                      ASGDays = lst.ASGDays == null ? 0 : Convert.ToInt32(lst.ASGDays),
                                      AVLDaysWithFT = lst.AVLDaysWithFT == null ? 0 : Convert.ToInt32(lst.AVLDaysWithFT),
                                      ASGDaysWithFT = lst.ASGDaysWithFT == null ? 0 : Convert.ToInt32(lst.ASGDaysWithFT),
                                      ASGStandby = lst.ASGStandby == null ? 0 : Convert.ToInt32(lst.ASGStandby),
                                      FAStatus = tempBidStatus.Where(a => a.ReservesCrewMemberID == lst.ReservesCrewMemberID).ToList(),
                                      FARAPActivity = lstFaActivity.Where(a => a.Employeenumber == lst.EmployeeID).FirstOrDefault(),
                                      //below line in future will come from BSM

                                      fALanguageDetails = tempCrememberLanguages.Where(x => x.FA.EmployeeNumber == lst.EmployeeID).ToList()
                                  }).ToList();

            if (lstFlightAttendant != null && lstFlightAttendant.Count > 0)
            {
                foreach (FlightAttendant fa in lstFlightAttendant)
                {
                    if (operatingLanguages != null)
                    {
                        var languages = operatingLanguages.FindAll(x => x.RapDaysID == fa.AvailableDays).Select(l => l.LanguageID).ToList();
                        foreach (FALanguageDetails lang in fa.fALanguageDetails)
                        {
                            if (languages != null && languages.Count > 0 && languages.Contains(lang.FALang.LanguageID))
                            {
                                fa.hasOperationalLanguage = true;
                            }
                        }
                    }
                }
            }

            return lstFlightAttendant;
        }

        public async Task<List<Model.BusinessObject.Sequence>> getSequenceDetails(string baseCD)
        {
            var lstSequenceLanguageDetailResult = new List<getLegalitySequenceLanguageDetails_Result>();
            var lstSequence = new List<Model.BusinessObject.Sequence>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure getLegalitySequenceLanguageDetails.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing GetLegalitySequenceLanguageDetails()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }


            return lstSequence;
        }

        public async Task<List<StandBy>> getStandByDetails(DateTime bidOperatingDate)
        {
            var lstStandByResult = new List<getLegalityStandBy_Result>();
            List<StandBy> lstStandBy = new List<StandBy>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure getLegalityStandBy.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing getStandByDetails()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return lstStandBy;
        }

        public async Task<List<BidCrewWaiver>> getLegalityProcessBidCrewWaiver()
        {
            List<BidCrewWaiverSupportingData> lstBidCrewWaiverSupportingData = new List<BidCrewWaiverSupportingData>();
            List<BidCrewWaiver> lstBidCrewWaiver = new List<BidCrewWaiver>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                //come back on how to handle two SP's here
                throw new DataProcessingException("Failure processing the database procedure getLegalityBidCrewWaiverSupportingData.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing getLegalityProcessBidCrewWaiver()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return lstBidCrewWaiver;
        }

        public async Task<List<AggressiveBidCrewWaiver>> getLegalityProcessAggressiveBidCrewWaiver()
        {
            var lstAggressiveBidCrewWaiver = new List<AggressiveBidCrewWaiver>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure getLegalityProcessAggressiveBidCrewWaiver.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing getLegalityProcessAggressiveBidCrewWaiver()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return lstAggressiveBidCrewWaiver;
        }

        public async Task<List<AggressiveBidCrewWaiver>> GetCoterminalBases()
        {
            var lstAggressiveBidCrewWaiver = new List<AggressiveBidCrewWaiver>();

            try
            {
                //Open Connection
                await this.Adaptor.Connect();

                //Get the results 


            }
            catch (DbException ex)
            {
                throw new DataProcessingException("Failure processing the database procedure getLegalityProcessAggressiveBidCrewWaiver.", ex);
            }
            catch (Exception ex)
            {
                //Add log
                throw new Exception("Exception occured in executing getLegalityProcessAggressiveBidCrewWaiver()");
            }
            finally
            {
                //Close the Connection
                this.Adaptor.Close();
            }

            return lstAggressiveBidCrewWaiver;
        }

        #endregion GetValues from DataBase

        #region Bulk Insert ROTD

        #endregion Bulk Insert ROTD
    }
}