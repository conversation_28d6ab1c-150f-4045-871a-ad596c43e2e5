﻿namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Sequence
{

    public class PayCredit
    {
        /// <summary>
        /// Total of greater time corresponding to all flight legs in the duty period sequence. Greater Time is 
        /// the larger of scheduled vs actual flying/block time.
        /// </summary>
        public int greaterTime { get; set; }

        /// <summary>
        /// Total of scheduled fly/block time corresponding to all flight legs in the duty period"
        /// </summary>
        public int scheduledBlockTime { get; set; }

        /// <summary>
        /// Total of actual fly/block time corresponding to all flight legs in the duty period
        /// </summary>
        public int actualBlockTime { get; set; }

        /// <summary>
        /// Total of scheduled pay credit minutes corresponding to a duty period 
        /// </summary>
        public int scheduledTotalCredit { get; set; }

        /// <summary>
        /// Total actual pay credit minutes(Post duty period / sequence completion)
        /// </summary>
        public int actualTotalCredit { get; set; }

        /// <summary>
        /// Credit minutes corresponding to deadHead flight legs in the duty period 
        /// </summary>
        public int deadheadCredit { get; set; }

        /// <summary>
        /// Scheduled minimum guaranteed pay credit minutes corresponding to a duty period . Applicable for duty periods which 
        /// has only short duration flight legs like DFW to AUS 
        /// </summary>
        public int scheduledRig { get; set; }

        /// <summary>
        /// Actual minimum guaranteed pay credit minutes (Post duty period / sequence completion). Applicable for duty periods
        /// which has only short duration flight legs like DFW to AUS
        /// </summary>
        public int actualRig { get; set; }

        /// <summary>
        /// Pay Protected Value in minutes corresponding to a sequence
        /// </summary>
        public int payProtectedValue { get; set; }

        /// <summary>
        /// Total sequence credit minutes corresponding to a sequence
        /// </summary>
        public int totalSequenceCredit { get; set; }


    }
}
