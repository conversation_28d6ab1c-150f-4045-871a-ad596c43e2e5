﻿using AA.Crew.ROMS.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Service.Mapper
{
    public class RapShiftResponseMapper: CCSMapperBase<RapShift, RAPShifts>
    {
        public override RAPShifts Map(RapShift source)
        {
            try
            {
                return new RAPShifts
                {
                    ContractMonth = source.contractMonth,
                    StartDateTime = Convert.ToDateTime(source.startDateTime),
                    EndDateTime = Convert.ToDateTime(source.endDateTime),
                    Shift = source.shift
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
