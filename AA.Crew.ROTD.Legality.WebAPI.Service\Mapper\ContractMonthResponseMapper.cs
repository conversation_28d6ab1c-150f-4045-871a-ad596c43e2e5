﻿using AA.Crew.ROTD.Legality.WebAPI.Model;
using CCSModel = AA.Crew.ROMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Service.Mapper
{
    public class ContractMonthResponseMapper : CCSMapperBase<CCSModel.ContractMonth, ContractMonth>
    {
        public override ContractMonth Map(CCSModel.ContractMonth source)
        {
            try
            {
                return new ContractMonth
                {
                    Month = source.contractMonth,
                    StartDate = source.startDate,
                    EndDate = source.endDate
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
