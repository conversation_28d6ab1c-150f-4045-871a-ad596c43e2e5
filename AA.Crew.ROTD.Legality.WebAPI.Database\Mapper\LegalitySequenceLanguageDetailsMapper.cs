using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalitySequenceLanguageDetailsMapper : MapperBase<List<LegalitySequenceLanguageDetailsDTO>, List<SequenceLanguageDetails>>
    {
        public override List<SequenceLanguageDetails> Map(List<LegalitySequenceLanguageDetailsDTO> legalitySequenceLanguageDetailsDtoList)
        {
            try
            {
                return legalitySequenceLanguageDetailsDtoList.Select(legalitySequenceLanguageDetailsDto => new SequenceLanguageDetails
                {
                    SequencePosDetailID = Convert.ToInt32(legalitySequenceLanguageDetailsDto.SequencePositionDetailsID),
                    LanguageID = legalitySequenceLanguageDetailsDto.LanguageID,
                    IsExcluded = Convert.ToBoolean(legalitySequenceLanguageDetailsDto.IsExcluded)

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalitySequenceLanguageDetailsDTO> Map(List<SequenceLanguageDetails> element)
        {
            throw new NotImplementedException();
        }
    }
}
