﻿using AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Common;
using AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Sequence;
using System.Collections.Generic;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class FlightDutyPeriod
    {

        private static long serialVersionUID = 1L;

        /// <summary>
        /// DutyPeriod startDateTime Start Date and Time of the given sequence 
        /// </summary>
        public CCSDateTime startDateTime { get; set; }

        /// <summary>
        /// Denotes the number of DutyPeriod
        /// </summary>
        public int dutyPeriodNumber { get; set; }

        /// <summary>
        /// DutyPeriod End Date and Time of the given sequence 
        /// </summary>
        public CCSDateTime endDateTime { get; set; }

        /// <summary>
        /// On Duty minutes
        /// </summary>
        public int odMinutes { get; set; }

        /// <summary>
        /// Provides the count of number of legs for each duty period
        /// </summary>
        public int numberOfLegs { get; set; }

        /// <summary>
        /// stopover airport for the given sequence
        /// </summary>
        public string layOverAirport { get; set; }

        /// <summary>
        /// caluclate time taken for each duty period
        /// </summary>
        public int duration { get; set; }

        /// <summary>
        /// stopover station for the given sequence
        /// </summary>
        public string layoverStation { get; set; }

        /// <summary>
        /// layover time taken for each dutyPeriod
        /// </summary>
        public int layoverInMinutes { get; set; }

        /// <summary>
        /// List of Flight Legs
        /// </summary>
        public List<FlightLeg> flightLegs { get; set; }

        /// <summary>
        /// Summary of all pay credits and flight leg block times corresponding to a duty period
        /// </summary>
        public PayCredit payCredit { get; set; }

        /// <summary>
        /// Duty Period Status
        /// </summary>
        public string dutyPeriodStatus { get; set; }

        /// <summary>
        /// Boolean flag to identify if the DP is ODAN or not.
        /// </summary>
        public bool odan { get; set; }

        /// <summary>
        /// Boolean flag to identify if the DP is REDEYE or not
        /// </summary>
        public bool redEye { get; set; }

        /// <summary>
        /// Boolean flag to identify if the DP is hawaii DutyPeriod
        /// </summary>
        public bool hawaiiDutyPeriod { get; set; }

        /// <summary>
        /// Boolean flag to identify if the DP is medium range
        /// </summary>
        public bool mediumRangeDP { get; set; }

        /// <summary>
        /// Boolean flag to identify if the DP is non long range
        /// </summary>
        public bool nonLongRangeDP { get; set; }

        /// <summary>
        /// Boolean flag to identify if the DP is domestic
        /// </summary>
        public bool domesticDP { get; set; }

        /// <summary>
        /// Boolean flag to identify if the DP is IPD
        /// </summary>
        public bool ipdDP { get; set; }

        /// <summary>
        /// DutyPeriod Start Date and Time in the Pilot’s acclimated station time
        /// </summary>
        public CCSDateTime fdpStartTime { get; set; }

        /// <summary>
        /// DutyPeriod End Date and Time in the Pilot’s acclimated station time
        /// </summary>
        public CCSDateTime fdpEndTime { get; set; }

        /// <summary>
        /// The calculated time taken for each flight duty period in minutes
        /// </summary>
        public int fdpDuration { get; set; }

        /// <summary>
        /// The station where the pilot is currently acclimated
        /// </summary>
        public string currentAcclimatedStation { get; set; }

        /// <summary>
        /// /The geographical area where local time at the pilot’s flight duty period departure point and arrival point differ by more than 60 degrees longitude
        /// </summary>
        public string currentTheatreStation { get; set; }

        /// <summary>
        /// The adjustment between the pilot’s home base time zone and acclimated time zone in minutes
        /// </summary>
        public int fdpAcclimatedStnTZAdjustment { get; set; }

    }
}
