﻿using AA.Crew.Reserves.QLA.Model.Response.RapShift;
using AA.Crew.ROMS.Client;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Request;
using AA.Crew.ROTD.Legality.WebAPI.Service.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Service.Mapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using ContractMonth = AA.Crew.ROTD.Legality.WebAPI.Model.ContractMonth;

namespace AA.Crew.ROTD.Legality.WebAPI.Service
{
    public class CrewDataServiceProvider : ICrewDataServiceProvider
    {
        private IServiceCallFactory _svcfactory = null;

        public CrewDataServiceProvider(IServiceCallFactory svcfactory)
        {
            _svcfactory = svcfactory ?? throw new ArgumentNullException(nameof(svcfactory));
        }
        
        public async Task<ContractMonth> GetContractMonthForGivenDate(DateTime processDate)
        {
            try
            {
                ContractMonthResponseMapper responseMapper = new ContractMonthResponseMapper();
                var client = this._svcfactory.GetROMSService(Constants.ServiceConfigurationName);
                ROMS.Model.ContractMonth response = client.getContractMonthForGivenDate(processDate).Result;
                var transResponse = responseMapper.Map(response);

                return transResponse;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<List<RAPShifts>> GetRAPShift(string BaseCD, string ContractMonth)
        {
            try
            {
                RapShiftResponseMapper responseMapper = new RapShiftResponseMapper();
                var client = this._svcfactory.GetROMSService(Constants.ServiceConfigurationName);
                IList<ROMS.Model.AirportDetails> response = client.GetBaseAirportList(ContractMonth, BaseCD).Result;
                var rapShifts = response.Where(x => x.iataCode == BaseCD).FirstOrDefault().rapShifts;
                var transResponse = responseMapper.Map(rapShifts);

                return transResponse;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
