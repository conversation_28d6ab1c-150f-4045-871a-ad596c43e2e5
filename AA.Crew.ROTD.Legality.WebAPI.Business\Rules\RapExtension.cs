﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    public class RapExtension : IROTDRules
    {
        private List<string> retRuleName;
        private bool retRuleNameStatus;

        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faActivity, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate baseDate, ref bool possibleIsLegal, ref string rapCode, ref int isCurrentRAP, List<QLARuleResult> qlaRuleResult, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleNameStatus = false;
            retRuleName = new List<string>();
          
            
            DateTime ShiftEndDate = new DateTime();
            RAPShifts currentRapShift = null;

            int hours = 2;

            if (faActivity != null)
            {
                if (faActivity.StartDate.HasValue && faActivity.EndDate.HasValue)
                {
                    if ((previousMonthRAPShifts != null && previousMonthRAPShifts.Count > 0) && faActivity.EndDate.GetValueOrDefault().Date == baseDate.ProcessingDate.Date)
                    {
                        currentRapShift = previousMonthRAPShifts.Where(s => s.Shift == faActivity.ActivityCode).Select(x => new RAPShifts
                        {
                            ContractMonth = x.ContractMonth,
                            EndDateTime = x.EndDateTime,
                            IsCurrentStatus = x.IsCurrentStatus,
                            Shift = x.Shift,
                            StartDateTime = x.StartDateTime
                        }).FirstOrDefault();
                    }
                    else
                    {                        
                        currentRapShift = rapShifts.Where(s => s.Shift == faActivity.ActivityCode).Select(x => new RAPShifts
                        {
                            ContractMonth = x.ContractMonth,
                            EndDateTime = x.EndDateTime,
                            IsCurrentStatus = x.IsCurrentStatus,
                            Shift = x.Shift,
                            StartDateTime = x.StartDateTime
                        }).FirstOrDefault();
                    }
                    //Appending date component to standard rap shift
                    currentRapShift.StartDateTime = Convert.ToDateTime(faActivity.StartDate.Value.ToShortDateString() + " " + currentRapShift.StartDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);

                    if (currentRapShift.StartDateTime.TimeOfDay > currentRapShift.EndDateTime.TimeOfDay)
                        //currentRapShift.EndDateTime = Convert.ToDateTime(faActivity.EndDate.Value.ToShortDateString() + " " + currentRapShift.EndDateTime.ToLongTimeString());
                        currentRapShift.EndDateTime = Convert.ToDateTime(currentRapShift.StartDateTime.AddDays(1).ToShortDateString() + " " + currentRapShift.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                    else
                        currentRapShift.EndDateTime = Convert.ToDateTime(faActivity.StartDate.Value.ToShortDateString() + " " + currentRapShift.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);

                    if (currentRapShift.EndDateTime < faActivity.EndDate)//RAP Extension
                    {
                        if (sequence != null)
                        {
                            ShiftEndDate = currentRapShift.EndDateTime;

                            if (sequence.SequenceReportDateTime > ShiftEndDate.AddHours(2) && sequence.SequenceReportDateTime <= faActivity.EndDate.Value.Add(TimeSpan.FromHours(2)))
                            {
                                if (baseDate.CurrentSysTime < ShiftEndDate)
                                {
                                    possibleIsLegal = false;
                                    retRuleNameStatus = true;
                                }

                            }
                        }
                        if (standby != null)
                        {
                            if (IsBaseCoTerminal)
                                hours = 3;
                            var standByEndTime = standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration));

                            ShiftEndDate = currentRapShift.EndDateTime;

                            if (standby.ReportTime >= faActivity.StartDate.Value.Add(TimeSpan.FromHours(hours)) &&
                               (standByEndTime > ShiftEndDate.AddHours(2) & standByEndTime <= faActivity.EndDate.Value.Add(TimeSpan.FromHours(2))))
                            {
                                if (baseDate.CurrentSysTime < ShiftEndDate)
                                {
                                    possibleIsLegal = false;
                                    retRuleNameStatus = true;
                                }
                            }
                        }
                    }
                }
                else
                {
                    possibleIsLegal = false;
                    retRuleNameStatus = true;
                }
            }

            if (retRuleNameStatus)
            {
                retRuleName.Add(ruleName);
            }
            return retRuleName;
        }

        public string RuleName { get; set; }
    }
}