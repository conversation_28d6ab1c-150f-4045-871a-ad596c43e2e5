﻿using AA.Crew.ROTD.Legality.WebAPI.Tests;
using AA.Crew.ROTD.Legality.WebAPI.Tests.Helpers;
using AA.Crew.ROTD.Legality.WebAPI.Tests.Models;
using AA.Crew.ROTD.Legality.WebAPI.Tests.SoftAssert;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Text.RegularExpressions;
using TechTalk.SpecFlow;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests
{
    [Binding]
    /// <summary>
    /// https://docs.microsoft.com/en-us/visualstudio/test/using-microsoft-visualstudio-testtools-unittesting-members-in-unit-tests?view=vs-2019
    /// </summary>
    public class TestBase
    {
        public static ReportingHelpers reportsHelper;

        #region Contexts & Initialize RunSettings Parameters

        // TestContext is available via Context Injection Since SpecFlow 2.2.1
        private readonly TestContext _testContext;
        private static TestDataContext _testDataContext;
        private readonly ScenarioContext _scenarioContext;
        public static FeatureContext _featureContext;

        public TestBase(TestContext testContext, TestDataContext testDataContext, ScenarioContext scenarioContext, FeatureContext featureContext)
        {
            _testContext = testContext;
            _testDataContext = testDataContext;
            _scenarioContext = scenarioContext;
            _testDataContext.currentStepData = new Dictionary<string, string>();
            _featureContext = featureContext;

            //Parameters.InitializeParameters(testContext);
            Parameters.InitializeReportsVariables();
            TestDataPath = Parameters.TestDataPath;
            TestResultsPath = Parameters.TestResultsPath;

        }

        #endregion

        string testDataNotFoundText = "TEST DATA NOT FOUND for the Test";
        static string dateFormatReportsPath = "yyyyMMMdd_HHmmsstt";

        public static bool IsChildTestInProgress = false;
        public static string ChildTestTitle;
        public static string TestDataPath;
        public static string TestResultsPath;


        [BeforeTestRun]
        public static void AssemblyInit()
        {
            InitializeReportsVariables();
            reportsHelper = new ReportingHelpers(TestResultsPath);

            //// Listen for Exceptions
            //AppDomain.CurrentDomain.FirstChanceException += (source, e) => {
            //    LogHelpers.WriteException(e.Exception);
            //};


        }
        /// <summary>
        /// Populate the parameters required to run the test cases, earlier it was from run settings. Now, its from app.config
        /// </summary>
        private static void InitializeReportsVariables()
        {
            var configSection = ConfigFactory.GetConfigurationSection("Values");
            if (configSection != null)
            {
                if (TestDataPath == null)
                {
                    TestDataPath = configSection["RPVTestDataPath"];
                }
                if (TestResultsPath == null)
                {
                    TestResultsPath = configSection["RPVTestResultsPath"];
                }
                TestDataContext.ShortCallCredit = Convert.ToInt16(configSection["SC"]);
                TestDataContext.LongCallCredit = Convert.ToInt16(configSection["LC"]);
            }
        }

        [AfterTestRun]
        public static void AssemblyCleanup()
        {
            InitializeReportsVariables();
            //// Close the driver
            //DriverContext.driver.Close();
            var TestResultsReportFilePath = Path.Combine(TestResultsPath, "index.html");
            // Flush Extent Reports
            reportsHelper.FlushReports();
            Console.WriteLine("Flushed Reports to " + TestResultsReportFilePath);

            string reportBackupFolder = Path.Combine(TestResultsPath, "Backups", DateTime.Now.ToString(dateFormatReportsPath));
            Directory.CreateDirectory(reportBackupFolder);
            Console.WriteLine(reportBackupFolder);

            File.Copy(TestResultsReportFilePath, Path.Combine(reportBackupFolder, "index.html"));
            Console.WriteLine("Backed up report to " + Path.Combine(reportBackupFolder, "index.html"));
        }

        /// <summary>
        /// Reset Every thing - First Entry point to feature file
        /// </summary>
        [BeforeFeature]
        public static void BeforeFeature()
        {
            SoftAssert.SoftAssert.ResetTotalStepErrorCount();
            //This has to be done after Assert all / Review all
            //SoftAssert.ResetTotalStepErrorCountForParentCount();
            //SoftAssert.ResetScenarioErrors();
            SoftAssert.SoftAssert.ResetTotalStepErrorCount();
            SoftAssert.SoftAssert.ResetStepErrors();
        }

        [AfterFeature]
        public static void AfterFeature()
        {

        }

        [BeforeScenario]
        public void BeforeScenario()
        {
            //Reset All the Scenario and Step Errors
            if (!IsChildTestInProgress)
            {
                SoftAssert.SoftAssert.ResetScenarioErrors();
                //SoftAssert.ResetScenarioErrorCount();
                SoftAssert.SoftAssert.ResetTotalStepErrorCount();
                SoftAssert.SoftAssert.ResetStepErrors();
            }
            if (reportsHelper is null)
            {
                reportsHelper = new ReportingHelpers(TestResultsPath);
            }
            var scenarioInfo = _scenarioContext.ScenarioInfo;
            var testName = _testContext.TestName;


            // Create Test (Or Child test) in Extent Report

            if (string.Join(";", scenarioInfo.Tags).Contains("ChildTest"))
            {
                // Create Child Test (Nested Node under Parent test)
                reportsHelper.CreateNode(scenarioInfo.Title + ChildTestTitle, scenarioInfo.Description);
            }
            else
            {
                reportsHelper.CreateTest(scenarioInfo.Title, testName.Replace('_', ' '), scenarioInfo.Description);
                // Assign Tags
                for (int i = 0; i < scenarioInfo.Tags.Length; i++)
                {
                    reportsHelper.AssignCategory(scenarioInfo.Tags[i]);
                }
            }
        }

        [AfterScenario]
        public void AfterScenario()
        {
            try
            {
                var testOutcome = _testContext.CurrentTestOutcome;
                var testOutcomeText = _testContext.CurrentTestOutcome.ToString().Replace("Inconclusive", "SKIPPED");
                if (SoftAssert.SoftAssert.GetTotalStepErrorCount() > 0)
                {
                    testOutcome = UnitTestOutcome.Failed;
                    testOutcomeText = $"There are <strong>{SoftAssert.SoftAssert.GetTotalStepErrorCount()}</strong> Soft Asserts were failed. \n Please review above steps.";
                }
                var testStatus = ConvertCurrentTestOutcomeToTestStatus(testOutcome);
                //string testErrorMsg = "";
                //#region WRITE TEST ERRORS TO LOG & REPORT
                //if (_scenarioContext.TestError != null)
                //{
                //    testErrorMsg = WriteTestErrorsToLogAndReport(_scenarioContext.TestError);
                //}
                //#endregion



                switch (testOutcome)
                {
                    case UnitTestOutcome.Error:
                    case UnitTestOutcome.Inconclusive:

                    case UnitTestOutcome.Failed:
                        if (SoftAssert.SoftAssert.GetTotalStepErrorCountForParent() > 0)
                            reportsHelper.WriteLog(string.Format("<span style=\"color:red\">Test Result:<br> <b>{0}</b></span>", testOutcomeText), testStatus);
                        break;

                    case UnitTestOutcome.Passed:
                        if (SoftAssert.SoftAssert.GetTotalStepErrorCountForParent() == 0)
                            reportsHelper.WriteLog(string.Format("<span style=\"color:limegreen\">Test Result:<br> <b>{0}</b></span>", testOutcomeText), testStatus); break;
                    default:
                        reportsHelper.WriteLog(string.Format("<b>Final Test Summary: {0}</b>", testOutcomeText));
                        break;
                }

                //Clear everything after the Feature file is completed -
                // we Can't use After Feature as it is called again after completing Child loop
                SoftAssert.SoftAssert.ResetTotalStepErrorCountForParentCount();
                SoftAssert.SoftAssert.ResetScenarioErrors();
                SoftAssert.SoftAssert.ResetTotalStepErrorCount();
                SoftAssert.SoftAssert.ResetTotalStepErrorCountForParentCount();
            }
            catch (Exception ex)
            {
                reportsHelper.WriteLog(string.Format("<span style=\"color:red\">Error:<br> <b>{0}</b></span>", ex.Message), TestStatus.Fail);
                throw;
            }


        }

        private string WriteTestErrorsToLogAndReport(Exception testError)
        {
            var lstExceptions = new List<Exception>();
            lstExceptions.Add(testError);
            return WriteTestErrorsToLogAndReport(lstExceptions);
        }
        private string WriteTestErrorsToLogAndReport(List<Exception> testErrors)
        {
            string testErrorMsg = "";
            if (testErrors.Count > 0)
            {
                bool printTable;
                bool printDataAndTable;
                foreach (var testError in testErrors)
                {
                    printTable = printDataAndTable = false;
                    //LogHelpers.WriteException(testError);
                    if (testError.Message.Contains("<PRINTDATA>"))
                    {
                        printTable = true;
                        testErrorMsg = testError.Message.Replace("<PRINTDATA>", string.Empty);
                    }
                    else if (testError.Message.Contains("<PRINT_DATA_AND_TABLE>"))
                    {
                        printDataAndTable = true;
                        testErrorMsg = testError.Message.Replace("<PRINT_DATA_AND_TABLE>", string.Empty);
                    }


                    bool includeScreenshot = true;
                    if (testError.Message.Contains("<DO_NOT_INCLUDE_SCREENSHOT>"))
                    {
                        includeScreenshot = false;
                        testErrorMsg = testError.Message.Replace("<DO_NOT_INCLUDE_SCREENSHOT>", string.Empty);
                    }
                    else
                    {
                        testErrorMsg = testError.Message;
                    }

                    if (testErrorMsg.Contains("Assert.Inconclusive failed."))
                    {
                        includeScreenshot = false;
                        testErrorMsg = testErrorMsg.Replace("Assert.Inconclusive failed.", "");
                    }
                    testErrorMsg = testErrorMsg.Replace(". Expected:<", ". \nExpected: <");
                    testErrorMsg = testErrorMsg.Replace(". Actual:<", ". \nActual  : <");

                    if (testError is AssertFailedException)
                    {
                        if (printDataAndTable)
                        {
                            testErrorMsg = $@"<br>" + testErrorMsg + $@"<br><br>";
                            foreach (DictionaryEntry item in testError.Data)
                            {
                                testErrorMsg += $@"<b> {item.Key.ToString()} </b> <br> <span>{item.Value} </span><br>";
                            }
                            // Write TestError Message into Report with Screenshot
                            reportsHelper.WriteLog(testErrorMsg, TestStatus.Fail);
                        }
                        else
                        {
                            //testErrorMsg = testError.Message;
                            reportsHelper.WriteLog("<xmp>" + testErrorMsg + "</xmp>", TestStatus.Fail);
                        }
                    }
                    else
                    {
                        // Write TestError StackTrace, Exception Type
                        reportsHelper.WriteLog("<xmp>" + testErrorMsg + "</xmp>", TestStatus.Fail);
                        reportsHelper.WriteFormattedLog(testError.StackTrace, TestStatus.Debug);
                        reportsHelper.WriteLog(">>> " + testError.GetType().FullName, TestStatus.Debug);
                    }

                    // Write Test Errors Data

                    foreach (DictionaryEntry item in testError.Data)
                    {
                        //LogHelpers.Write("Exception Data.");
                        //LogHelpers.WriteHorizontalLine();
                        //LogHelpers.Append($"{item.Key}  \n{item.Value}");
                        if (printTable)
                        {
                            reportsHelper.WriteLog($@"<b style=""color: #007bff;""> {item.Key.ToString().ToUpper()} </b> <br> {item.Value}", TestStatus.Info);
                        }
                    }
                }

                // Write Test Data
                foreach (var item in _testDataContext.currentStepData)
                {
                    //LogHelpers.Write($"{item.Key} ==> \n{item.Value}");
                    reportsHelper.WriteLog($"{item.Key} ==> \n{item.Value}", TestStatus.Info);
                }




            }

            return testErrorMsg;
        }

        private static TestStatus ConvertCurrentTestOutcomeToTestStatus(UnitTestOutcome currentTestOutcome)
        {
            TestStatus testStatus;
            switch (currentTestOutcome)
            {
                case UnitTestOutcome.Passed: testStatus = TestStatus.Pass; break;
                case UnitTestOutcome.Failed: testStatus = TestStatus.Fail; break;
                case UnitTestOutcome.Error: testStatus = TestStatus.Error; break;
                case UnitTestOutcome.NotRunnable: testStatus = TestStatus.Skip; break;
                case UnitTestOutcome.Inconclusive: testStatus = TestStatus.Skip; break;
                case UnitTestOutcome.InProgress: testStatus = TestStatus.Info; break;
                case UnitTestOutcome.Unknown: testStatus = TestStatus.Info; break;
                case UnitTestOutcome.Timeout: testStatus = TestStatus.Fatal; break;
                case UnitTestOutcome.Aborted: testStatus = TestStatus.Fatal; break;
                default: testStatus = TestStatus.Warning; break;
            }

            return testStatus;
        }


        [BeforeStep]
        public void BeforeStepLog()
        {
            SoftAssert.SoftAssert.ResetStepErrors();
            string stepName = _scenarioContext.StepContext.StepInfo.Text;
            //LogHelpers.Append($"{DateTime.Now.ToString(dateFormat)} \t ***** {stepName}.");

            if (string.IsNullOrWhiteSpace(stepName))
            {
                stepName = @"<b style=""color: #8297a2;"">" + _scenarioContext.StepContext.StepInfo.StepDefinitionType.ToString().ToUpper() + "</b>";
            }

            // Single quoted Pramaters - Bold text
            string stepName_formatted = Regex.Replace(stepName, "(?'start'')(?'highlight'[^\\']*)(?'end'')", m => string.Format("{0}{1}{2}",
                m.Groups["start"].Value.Replace("'", "<b>'"),
                m.Groups["highlight"].Value,
                m.Groups["end"].Value.Replace("'", "'</b>")));


            reportsHelper.WriteLog(stepName_formatted, TestStatus.Info);
            _testDataContext.currentStepData.Clear();
        }

        [AfterStep]
        public void AfterStepLog()
        {
            var stepStatus = TestStatus.Pass;

            if (SoftAssert.SoftAssert.GetStepErrors().Count > 0)
            {
                stepStatus = TestStatus.Fail;
                WriteTestErrorsToLogAndReport(SoftAssert.SoftAssert.GetStepErrors());

                //This code clears step errors to be propogated back
                // SoftAssert.SoftAssert.ResetStepErrors();
            }
            else if (_scenarioContext.TestError != null)
            {
                stepStatus = TestStatus.Fail;
                WriteTestErrorsToLogAndReport(_scenarioContext.TestError);
            }

            //reportsHelper.WriteLog("Screenshot: ",  stepStatus);

        }





        public void WriteLog(string infoMessage, TestStatus status = TestStatus.Info)
        {

            reportsHelper.WriteLog(infoMessage, status);
        }


    }
}
