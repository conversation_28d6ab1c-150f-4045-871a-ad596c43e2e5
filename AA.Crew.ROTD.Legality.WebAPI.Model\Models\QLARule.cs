using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class QLARule
    {
        public QLARule()
        {
            this.PostQLAMappings = new HashSet<PostQLAMapping>();
            this.QLARulesWaiverDetails = new HashSet<QLARulesWaiverDetail>();
            this.LegalityQLARules = new HashSet<LegalityQLARule>();
        }
    
        public long QLARuleID { get; set; }
        public string QLARule1 { get; set; }
    
        public virtual ICollection<PostQLAMapping> PostQLAMappings { get; set; }
        public virtual ICollection<QLARulesWaiverDetail> QLARulesWaiverDetails { get; set; }
        public virtual ICollection<LegalityQLARule> LegalityQLARules { get; set; }
    }
}
