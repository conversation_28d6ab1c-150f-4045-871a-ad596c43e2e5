﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct CoTerminalOrdinal
    {
        /* Oridinal variables */

        internal Int32 BaseID;
        internal Int32 BaseCD;
        internal Int32 BaseName;

        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.BaseID = sqlDataReader.GetOrdinal("BaseID");
            this.BaseCD = sqlDataReader.GetOrdinal("BaseCD");
            this.BaseName = sqlDataReader.GetOrdinal("BaseName");

            this.Initialized = true;
        }
    }
}
