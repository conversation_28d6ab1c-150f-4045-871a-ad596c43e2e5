using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityProcessAggressiveBidCrewWaiverOrdinal
    {
        /* Oridinal variables */

        internal Int32 EmployeeID;
        internal Int32 FAName;
        internal Int32 BidtypeID;
        internal Int32 BidTypeName;
        internal Int32 Runid;
        internal Int32 BidCategoryID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.EmployeeID = sqlDataReader.GetOrdinal("EmployeeID");
            this.FAName = sqlDataReader.GetOrdinal("FAName");
            this.BidtypeID = sqlDataReader.GetOrdinal("BidtypeID");
            this.BidTypeName = sqlDataReader.GetOrdinal("BidTypeName");
            this.Runid = sqlDataReader.GetOrdinal("Runid");
            this.BidCategoryID = sqlDataReader.GetOrdinal("BidCategoryID");


            this.Initialized = true;
        }
    }
}
