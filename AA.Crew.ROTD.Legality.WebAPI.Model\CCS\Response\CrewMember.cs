﻿using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class CrewMember
    {

        public string airlineCode { get; set; }

        public long employeeID { get; set; }

        public string firstName { get; set; }

        public string middleInitial { get; set; }

        public string lastName { get; set; }

        public string nickName { get; set; }

        public string gender { get; set; }

        public string crewType { get; set; }

        public List<BidStatus> bidStatuses { get; set; }

        public List<EmployeeActivity> employeeActivities { get; set; }


        public List<CrewRap> reserveAvailabilities { get; set; }

        public List<SequenceDetailsResponse> sequences { get; set; }

        public List<string> languageQuals { get; set; }

        public List<EquipmentQuals> equipmentQuals { get; set; }

        public List<ServiceQuals> serviceQuals { get; set; }

        public List<EmployeeLicenseDetails> passportOrVisaInfos { get; set; }

        public bool usEast { get; set; }

        public bool usWest { get; set; }

        public string seniorityListInd { get; set; }

        public bool voluntaryPurser { get; set; }

        public bool? purser { get; set; }

        public bool onDuty { get; set; }

        public bool onRest { get; set; }

        public bool isSick { get; set; }

        public DateTime? restEndTime { get; set; }

        public DateTime? restStatusReportedAt { get; set; }

        public List<TempDutyActivity> temporaryDutyActivities { get; set; }

        public List<CockpitQualification> cockpitQualifications { get; set; }

        public SickTime sickTime { get; set; }


        /// <summary>
        /// CAUTION: THIS FIELD IS NOT USED BY DATASERVICES(CCS), BUT USED IN REPORTS PROJECT.
        /// </summary>
        [Obsolete]
        public bool isVolunteer { get; set; }

        public CrewMember()
        {
            bidStatuses = new List<BidStatus>();
            employeeActivities = new List<EmployeeActivity>();
            sequences = new List<SequenceDetailsResponse>();
        }


    }
}
