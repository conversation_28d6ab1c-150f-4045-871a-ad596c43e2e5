﻿using AA.Crew.ROMS.ROTA.WebApi.Model.Requests;
using AA.Crew.ROMS.ROTA.WebApi.Model.Responses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Service
{
    public interface IRotaWebApiProvider
    {
        Task<GetOperatingLanguageResponse> GetOperatingLanguages(GetOperatingLanguageRequest request);
    }
}
