using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class WaiverType
    {
        public WaiverType()
        {
            this.QLARulesWaiverDetails = new HashSet<QLARulesWaiverDetail>();
        }
    
        public int WaiverTypeID { get; set; }
        public string WaiverTypeDescription { get; set; }
        public bool IsActive { get; set; }
    
        public virtual ICollection<QLARulesWaiverDetail> QLARulesWaiverDetails { get; set; }
    }
}
