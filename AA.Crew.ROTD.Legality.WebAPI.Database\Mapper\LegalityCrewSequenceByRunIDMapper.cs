using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    //public class LegalityCrewSequenceByRunIDMapper : MapperBase<List<LegalityCrewSequenceByRunIDDTO>, List<LegalityCrewSequenceByRunID>>
    //{
    //    public override List<LegalityCrewSequenceByRunID> Map(List<LegalityCrewSequenceByRunIDDTO> legalityCrewSequenceByRunIDDtoList)
    //    {
    //        try
    //        {
    //            return legalityCrewSequenceByRunIDDtoList.Select(legalityCrewSequenceByRunIDDto => new LegalityCrewSequenceByRunID
    //            {
    //                ReservesCrewSequenceLegalityID = legalityCrewSequenceByRunIDDto.ReservesCrewSequenceLegalityID,
    //                ReservesCrewMemberID = legalityCrewSequenceByRunIDDto.ReservesCrewMemberID,
    //                RunID = legalityCrewSequenceByRunIDDto.RunID,
    //                SequencePositionDetailsID = legalityCrewSequenceByRunIDDto.SequencePositionDetailsID,
    //                StandByID = legalityCrewSequenceByRunIDDto.StandByID,
    //                IsOver35By7 = legalityCrewSequenceByRunIDDto.IsOver35By7,


    //            }).ToList();
    //        }
    //        catch (Exception)
    //        {
    //            throw;
    //        }
    //    }

    //    public override List<LegalityCrewSequenceByRunIDDTO> Map(List<LegalityCrewSequenceByRunID> element)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
}
