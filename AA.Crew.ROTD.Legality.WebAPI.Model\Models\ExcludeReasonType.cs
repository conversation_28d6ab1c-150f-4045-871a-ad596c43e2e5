using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class ExcludeReasonType
    {
        public ExcludeReasonType()
        {
            this.ExcludeReasons = new HashSet<ExcludeReason>();
        }
    
        public long ExcludeReasonTypeID { get; set; }
        public string ExcludeReasonTypes { get; set; }
    
        public virtual ICollection<ExcludeReason> ExcludeReasons { get; set; }
    }
}
