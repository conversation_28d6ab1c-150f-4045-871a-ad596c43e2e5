﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class EquipmentQuals
    {

        public string equipmentGroup { get; set; }

        public List<string> needsDifferencesTraining { get; set; }

        public string domesticQuals { get; set; }

        public string internationalQuals { get; set; }

        [J<PERSON><PERSON>onverter(typeof(JsonDateConverter))]
        public DateTime? recurGrdSchTrng { get; set; }

        [JsonConverter(typeof(JsonDateConverter))]
        public DateTime? recurDrillTrng { get; set; }

        [JsonConverter(typeof(JsonDateConverter))]
        public DateTime? recurDitchTrng { get; set; }

        public EquipmentQuals()
        {
            needsDifferencesTraining = new List<string>();
        }
    }
}
