﻿namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class CheckAirmanStatus // implements Serializable
    {

        private static long serialVersionUID = -8438196674363533225L;

        /// <summary>
        /// Hex Value of CurrentMonth or OtherMonth
        /// example = "88,A0,C0"
        /// </summary>
        public string checkAirmanType { get; set; }

        /// <summary>
        /// bool flag to Indicate whether check<PERSON><PERSON><PERSON> is active
        /// example = "true,false"
        /// </summary>
        public bool onActive { get; set; }

        /// <summary>
        /// bool flag to Indicate whether check<PERSON>irman is onSupplemental
        /// example = "true,false"
        /// </summary>
        public bool onSupplemental { get; set; }

        /// <summary>
        /// bool flag to Indicate whether checkAirman is onLine
        /// example = "true,false"
        /// </summary>
        public bool onLine { get; set; }

        /// <summary>
        /// bool flag to Indicate whether check<PERSON><PERSON><PERSON> is onRotate
        /// example = "true,false"
        /// </summary>
        public bool onRotate { get; set; }

        /// <summary>
        /// bool flag to Indicate whether check<PERSON><PERSON><PERSON> is onSchoolhouse 
        /// example = "true,false"
        /// </summary>
        public bool onSchoolhouse { get; set; }

        /// <summary>
        /// bool flag to Indicate whether checkAirman is onEagleIOECapt 
        /// example = "true,false"
        /// </summary>
        public bool onEagleIOECapt { get; set; }


    }
}
