using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityReservesCrewMembersMapper : MapperBase<List<LegalityReservesCrewMembersDTO>, List<FlightAttendant>>
    {
        public override List<FlightAttendant> Map(List<LegalityReservesCrewMembersDTO> legalityReservesCrewMembersDtoList)
        {
            try
            {
                return legalityReservesCrewMembersDtoList.Select(legalityReservesCrewMembersDto => new FlightAttendant
                {
                    Name = legalityReservesCrewMembersDto.Name,
                    EmployeeNumber = legalityReservesCrewMembersDto.EmployeeID,
                    SeniorityNumber = legalityReservesCrewMembersDto.SeniorityNumber,
                    AvailableDays = legalityReservesCrewMembersDto.AVLDays,
                    IsVolunteer = legalityReservesCrewMembersDto.IsVolunteer,
                    IsSick = legalityReservesCrewMembersDto.IsSick,
                    ReservesCrewMemberID = legalityReservesCrewMembersDto.ReservesCrewMemberID,
                    ASGSequence = legalityReservesCrewMembersDto.ASGSequence,
                    ASGDays = legalityReservesCrewMembersDto.ASGDays,
                    AVLDaysWithFT = legalityReservesCrewMembersDto.AVLDaysWithFT,
                    ASGDaysWithFT = legalityReservesCrewMembersDto.ASGDaysWithFT,
                    ASGStandby = legalityReservesCrewMembersDto.ASGStandby,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityReservesCrewMembersDTO> Map(List<FlightAttendant> element)
        {
            throw new NotImplementedException();
        }
    }
}
