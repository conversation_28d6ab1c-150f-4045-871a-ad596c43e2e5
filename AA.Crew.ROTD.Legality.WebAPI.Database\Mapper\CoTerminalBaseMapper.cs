using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System.Collections.Generic;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class CoTerminalBaseMapper : MapperBase<List<CoTerminalDTO>, List<CoTerminalBase>>
    {
        public override List<CoTerminalBase> Map(List<CoTerminalDTO> source)
        {
            List<CoTerminalBase> destination = new List<CoTerminalBase>();

            foreach (CoTerminalDTO dto in source)
            {
                CoTerminalBase coTerminalBase = new CoTerminalBase
                {
                    BaseID = dto.BaseID,
                    BaseCD = dto.BaseCD,
                    BaseName = dto.BaseName
                };

                destination.Add(coTerminalBase);
            }

            return destination;
        }

        // Add missing implementation here
        public override List<CoTerminalDTO> Map(List<CoTerminalBase> source)
        {
            throw new NotImplementedException();
        }
    }
}
