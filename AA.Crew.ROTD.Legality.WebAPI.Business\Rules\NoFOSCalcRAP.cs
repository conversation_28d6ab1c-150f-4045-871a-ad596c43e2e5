﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    class NoFOSCalcRAP : IROTDRules
    {
        private List<string> retRuleName;
        public string RuleName { get; set; }

        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faActivity, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate baseDate, ref bool possibleIsLegal, ref string rapCode, ref int isCurrentRAP, List<QLARuleResult> qlaRuleResult, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleName = new List<string>();
            //possibleIsLegal = false;
            CalcRAP objCalcRAP = new CalcRAP();
            List<RAPShifts> objRAPShift = new List<RAPShifts>();
            DateTime date = baseDate.ProcessingDate.AddDays(1);
            DateTime proDate = baseDate.ProcessingDate;
            DateTime coTerminalDate = baseDate.ProcessingDate;
            DateTime priorDate = baseDate.ProcessingDate.AddDays(-1);
            List<RAPShifts> allRapShifts = null;
            //Prior Status = 0 and Current status = 1
            if (sequence != null)
            {
                allRapShifts = rapShifts.Select(x => (RAPShifts)x.Clone()).ToList();

                if (previousMonthRAPShifts!=null && previousMonthRAPShifts.Count > 0)
                {
                    var prevShifs = previousMonthRAPShifts.Where(x => x.StartDateTime > x.EndDateTime).ToList();
                    allRapShifts.AddRange(prevShifs);
                }

                foreach (RAPShifts item in allRapShifts)
                {
                    if ((sequence.SatelliteStation || !IsBaseCoTerminal))
                    {
                        DateTime priorEndDate = new DateTime();
                        if (item.StartDateTime > item.EndDateTime)
                        {
                            priorEndDate = Convert.ToDateTime(priorDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            if (sequence.SequenceStartDateTime >= Convert.ToDateTime(priorDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(2)).ToLongTimeString(), CultureInfo.CurrentCulture) && sequence.SequenceReportDateTime <= priorEndDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = priorEndDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = priorEndDate, Shift = item.Shift, IsCurrentStatus = 0 });

                            }
                        }
                        proDate = baseDate.ProcessingDate;
                        while (proDate <= date)
                        {
                            DateTime endDate = new DateTime();
                            if (item.StartDateTime > item.EndDateTime)
                            {
                                endDate = Convert.ToDateTime(proDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            }
                            else
                            {
                                endDate = Convert.ToDateTime(proDate.ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            }
                            if (sequence.SequenceStartDateTime >= Convert.ToDateTime(proDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(2)).ToLongTimeString(), CultureInfo.CurrentCulture) && sequence.SequenceReportDateTime <= endDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = endDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = endDate, Shift = item.Shift, IsCurrentStatus = 1 });
                            }

                            proDate = proDate.AddDays(1);
                        }
                    }
                    else if (IsBaseCoTerminal)
                    {
                        DateTime priorEndDate = new DateTime();
                        if (item.StartDateTime > item.EndDateTime)
                        {
                            priorEndDate = Convert.ToDateTime(priorDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            if (sequence.SequenceStartDateTime >= Convert.ToDateTime(priorDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(3)).ToLongTimeString(), CultureInfo.CurrentCulture) && sequence.SequenceReportDateTime <= priorEndDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = priorEndDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = priorEndDate, Shift = item.Shift, IsCurrentStatus = 0 });
                            }
                        }
                        coTerminalDate = baseDate.ProcessingDate;
                        while (coTerminalDate <= date)
                        {
                            DateTime endDate = new DateTime();
                            if (item.StartDateTime > item.EndDateTime)
                            {
                                endDate = Convert.ToDateTime(coTerminalDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            }
                            else
                            {
                                endDate = Convert.ToDateTime(coTerminalDate.ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            }
                            if (sequence.SequenceStartDateTime >= Convert.ToDateTime(coTerminalDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(3)).ToLongTimeString(), CultureInfo.CurrentCulture) && sequence.SequenceReportDateTime <= endDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = endDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = endDate, Shift = item.Shift, IsCurrentStatus = 1 });
                            }

                            coTerminalDate = coTerminalDate.AddDays(1);
                        }
                    }
                }
            }
            else if (standby != null)
            {
                foreach (RAPShifts item in rapShifts)
                {
                    if (!IsBaseCoTerminal)
                    {
                        DateTime priorEndDate = new DateTime();
                        if (item.StartDateTime > item.EndDateTime)
                        {
                            priorEndDate = Convert.ToDateTime(priorDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            if (standby.ReportTime >= Convert.ToDateTime(priorDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(2)).ToLongTimeString(), CultureInfo.CurrentCulture) && standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration)) <= priorEndDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = priorEndDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = priorEndDate, Shift = item.Shift, IsCurrentStatus = 0 });
                            }
                        }
                        proDate = baseDate.ProcessingDate;
                        while (proDate <= date)
                        {
                            DateTime endDate = new DateTime();
                            if (item.StartDateTime > item.EndDateTime)
                            {
                                endDate = Convert.ToDateTime(proDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            }
                            else
                            {
                                endDate = Convert.ToDateTime(proDate.ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            }
                            if (standby.ReportTime >= Convert.ToDateTime(proDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(2)).ToLongTimeString(), CultureInfo.CurrentCulture) && standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration)) <= endDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = endDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = endDate, Shift = item.Shift, IsCurrentStatus = 1 });
                            }

                            proDate = proDate.AddDays(1);
                        }
                    }
                    else if (IsBaseCoTerminal)
                    {
                        DateTime priorEndDate = new DateTime();
                        if (item.StartDateTime > item.EndDateTime)
                        {
                            priorEndDate = Convert.ToDateTime(priorDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            if (standby.ReportTime >= Convert.ToDateTime(priorDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(3)).ToLongTimeString(), CultureInfo.CurrentCulture) && standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration)) <= priorEndDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = priorEndDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = priorEndDate, Shift = item.Shift, IsCurrentStatus = 0 });
                            }
                        }
                        coTerminalDate = baseDate.ProcessingDate;
                        while (coTerminalDate <= date)
                        {
                            DateTime endDate = new DateTime();
                            if (item.StartDateTime > item.EndDateTime)
                            {
                                endDate = Convert.ToDateTime(coTerminalDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(), CultureInfo.CurrentCulture);
                            }
                            else
                            {
                                endDate = Convert.ToDateTime(coTerminalDate.ToShortDateString() + " " + item.EndDateTime.ToLongTimeString(),CultureInfo.CurrentCulture);
                            }
                            if (standby.ReportTime >= Convert.ToDateTime(coTerminalDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(3)).ToLongTimeString(), CultureInfo.CurrentCulture) && standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration)) <= endDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = endDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = endDate, Shift = item.Shift, IsCurrentStatus = 1 });
                            }

                            coTerminalDate = coTerminalDate.AddDays(1);
                        }
                    }
                }
            }
            //Set FOS RAP and Calculated RAP are [blank]; All ROTD Interpreter Context = False 
            var objResult = objRAPShift.OrderBy(x => x.EndDateTime).FirstOrDefault();

            if (objResult == null)
            {
                rapCode = "";
                isCurrentRAP = 1;
                possibleIsLegal = false;
                retRuleName.Add(ruleName);
            }
            else
            {
                rapCode = objResult.Shift;
                isCurrentRAP = objResult.IsCurrentStatus;
                objCalcRAP = new CalcRAP();
                objCalcRAP.RAPShift = objResult.Shift;
                objCalcRAP.IsCurrentStatus = objResult.IsCurrentStatus;
                if (flightAttendant != null)
                    flightAttendant.CALCRapStatus = objCalcRAP;
            }
            return retRuleName;
        }
    }
}
