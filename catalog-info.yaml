apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: roms-rotd-legality-api
  description: 'This api handles all legality interpretation for ROTD application'
  annotations:
    github.com/project-slug: 'AAInternal/roms-rotd-legality-api'
    argocd/app-selector: 'backstage-name=roms-rotd-legality-api'
    backstage.io/kubernetes-label-selector: 'backstage.io/kubernetes-id=roms-rotd-legality-api'
  tags:
    - ok8
spec:
  type: website
  lifecycle: experimental
  owner: "roms-gdfd-admins"
