using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityPhasesMapper : MapperBase<List<LegalityPhasesDTO>, List<LegalityPhases>>
    {
        public override List<LegalityPhases> Map(List<LegalityPhasesDTO> legalityPhasesDtoList)
        {
            try
            {
                return legalityPhasesDtoList.Select(legalityPhasesDto => new LegalityPhases
                {
                    LegalityPhase = legalityPhasesDto.LegalityPhase,
                    LegalityPhaseID = Convert.ToInt32(legalityPhasesDto.LegalityPhaseID)

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityPhasesDTO> Map(List<LegalityPhases> element)
        {
            throw new NotImplementedException();
        }
    }
}
