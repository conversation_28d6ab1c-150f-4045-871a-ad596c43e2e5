﻿using AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Sequence;
using AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Sequence.Response;
using System;
using System.Collections.Generic;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Sequence
{
    public class FlightLeg
    {

        private static long serialVersionUID = 7218652261755039393L;

        public string airlineCode { get; set; }

        public string legType { get; set; }

        public List<LegStatus> legStatuses { get; set; }

        public int flightNumber { get; set; }

        public int legIndex { get; set; }

        public string departureStation { get; set; }

        public DateTime flightOriginationDate { get; set; }

        public string visaIndicator { get; set; }

        public string visaCountry { get; set; }

        public EquipmentQuals equipmentQuals { get; set; }

        public int blockTime { get; set; }

        public int groundTime { get; set; }

        public bool deadHead { get; set; }

        public string deadHeadAirlineCode { get; set; }

        public FlightTimes scheduled { get; set; }

        public FlightTimes actual { get; set; }

        public FlightTimes reScheduled { get; set; }

        public DelayMinutes delayMinutes { get; set; }

        public string arrivalStation { get; set; }

        public int departureDuplicateCode { get; set; }

        public int arrivalDuplicateCode { get; set; }

        public string status { get; set; }

        public bool noShowDHD { get; set; }

        public bool active { get; set; }

        public bool cancelled { get; set; }

        public bool removed { get; set; }

        public int greaterTime { get; set; }

        public int diversionTime { get; set; }

        public string actualSeat { get; set; }

        public bool signedIn { get; set; }

        public bool isIPD { get; set; }

        public bool endOfDutyPeriod { get; set; }

        public bool endOfSequence { get; set; }

        public string legAssignmentCode { get; set; }

        public string legRemovalCode { get; set; }

        public bool international { get; set; }

        public bool startOfDutyPeriod { get; set; }

        public bool posLangPremPay { get; set; }

        public List<string> posLangQuals { get; set; }

        public string crewMeal { get; set; }

    }
}
