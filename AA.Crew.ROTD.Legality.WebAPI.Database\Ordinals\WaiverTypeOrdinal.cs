using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct WaiverTypeOrdinal
    {
        /* Oridinal variables */

        internal Int32 WaiverTypeID;
        internal Int32 WaiverTypeDescription;
        internal Int32 IsActive;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.WaiverTypeID = sqlDataReader.GetOrdinal("WaiverTypeID");
            this.WaiverTypeDescription = sqlDataReader.GetOrdinal("WaiverTypeDescription");
            this.IsActive = sqlDataReader.GetOrdinal("IsActive");

            this.Initialized = true;
        }
    }
}
