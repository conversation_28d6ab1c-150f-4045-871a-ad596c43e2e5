apiVersion: runway.aa.com/v1alpha1
kind: WebApp
metadata:
  name: roms-rotd-legality-api-prod
  labels:
    aa-app-shortname: FARsrvMgmt    
    runway.aa.com/disablePssRestricted: 'true'
    runway.aa.com/secrets-generation: '2'
  annotations:
    enableCloudIngress: "true"
    backstageName: roms-rotd-legality-api  
spec:
  containerName: packages.aa.com/docker-prod/roms-rotd-legality-api
  containerTag: '1.25.10205.102'
  port: 8080
  secrets: roms-rotd-legality-api-secrets-prod
  healthCheck: # Optional
    enabled: True
    endpoint: /ping
    port: 8080
    readiness: # Optional
      initialDelaySeconds: 180 # Defaults to 45
    liveness:
      initialDelaySeconds: 240 # Defaults to 15
  autoscale:
     minimum: 2
     maximum: 3
  trafficPermissions:
    type: api
    apiEnvironment: prod
    excludes:
    - /ping
    - /healthcheck
    - /environmentname
    - /appversion   
  resourceLimits: # Optional Modifications of resources for your pod
    mem: 4Gi # 256Mi or 1Gi. Defaults to 256Mi mem. Max is 4Gi or 4096Mi
    cpu: 4000m # Defaults to 250m cpu. Max is 4000m or 4.
