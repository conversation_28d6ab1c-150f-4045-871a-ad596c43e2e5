﻿using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct AppSettingsOrdinals
    {
        /* Oridinal variables */
        internal Int32 SettingValue;
        internal Boolean Initalized;

        /// <summary>
        /// Determines the ordinal positions for each of the columns in the App Settings
        /// </summary>
        /// <param name="sqlDataReader">The DataReader object to determine the ordinal positions from.</param>
        internal void Initalize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.SettingValue = sqlDataReader.GetOrdinal("SettingValue");

            this.Initalized = true;
        }
    }
}
