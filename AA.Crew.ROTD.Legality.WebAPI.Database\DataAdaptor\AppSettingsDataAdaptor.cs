﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor
{
    internal class AppSettingsDataAdaptor : AdaptorBase, IAppSettingsDataAdaptor
    {
        #region Private data types
        private const string GETAPPSETTINGS_SP = "[ROTD].[ApplicationSettings_GetSetting]";
        private SqlDataReader sqlDR = null;
        #endregion

        public AppSettingsDataAdaptor(string connectionString) : base(connectionString)
        {
        }

        public void InitalizeAppSettingsSP()
        {
            this.SqlCommand.CommandText = GETAPPSETTINGS_SP;
            this.SqlCommand.Parameters.Clear();
        }
        public async Task<AppSettingsDTO> GetAppSettingsDTO(string appSettingKey)
        {
            AppSettingsDTO appSettings = new AppSettingsDTO();
            try
            {
                //Initalize the DataReader
                this.InitalizeAppSettingsSP();
                this.SqlCommand.Parameters.Add(new SqlParameter("NameOfSettingToGet", appSettingKey));

                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                Ordinals.AppSettingsOrdinals appSettingsOrdinals = new Ordinals.AppSettingsOrdinals();
                appSettings.SettingName = appSettingKey;
                appSettings.SettingValues = "";
                while (this.sqlDR.Read()) //For each Record returned
                {
                    //Determine the the ordinals for the select statement have been assigned.
                    if (!appSettingsOrdinals.Initalized)
                    {
                        //If not then assign them.
                        appSettingsOrdinals.Initalize(this.sqlDR);
                    }

                    appSettings.SettingValues = this.sqlDR.GetString(appSettingsOrdinals.SettingValue);
                }
            }
            catch (SqlException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return appSettings;
        }
    }
}
