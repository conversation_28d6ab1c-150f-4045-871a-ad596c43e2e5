using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct QLARulesWaiverDetailsOrdinal
    {
        /* Oridinal variables */

        internal Int32 QLARulesWaiverDetailsID;
        internal Int32 WaiverTypeID;
        internal Int32 QLARuleID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.QLARulesWaiverDetailsID = sqlDataReader.GetOrdinal("QLARulesWaiverDetailsID");
            this.WaiverTypeID = sqlDataReader.GetOrdinal("WaiverTypeID");
            this.QLARuleID = sqlDataReader.GetOrdinal("QLARuleID");


            this.Initialized = true;
        }
    }
}
