﻿namespace AA.Crew.ROTD.Legality.WebAPI.Business.Operations
{

    /// <summary>
    /// Sometimes you just want to start the task and don't wait for it nor want to know if it succeeds or not. 
    /// If you just use Task.Run and the task fails, you still need to handle the exception to avoid the UnobservedTaskException event to be raised
    /// </summary>
    public static class TaskExtensions
    {
        public static void Forget(this Task task)
        {
            if (!task.IsCompleted || task.IsFaulted)
            {
                _ = ForgetAwaited(task);
            }

            async static Task ForgetAwaited(Task task)
            {
                await task.ConfigureAwait(ConfigureAwaitOptions.SuppressThrowing);
            }
        }
    }
}
