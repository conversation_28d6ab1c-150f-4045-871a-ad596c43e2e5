using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityPhasesOrdinal
    {
        /* Oridinal variables */

        internal Int32 LegalityPhase;
        internal Int32 LegalityPhaseID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.LegalityPhase = sqlDataReader.GetOrdinal("LegalityPhase");
            this.LegalityPhaseID = sqlDataReader.GetOrdinal("LegalityPhaseID");

            this.Initialized = true;
        }
    }
}
