﻿using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.Response;
using AA.Crew.ROTD.Legality.WebAPI.Tests.Helpers;
using Microsoft.Extensions.Logging.Abstractions;
using System.Collections.Generic;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests.Models
{
    public class TestDataContext
    {
        public ReportingHelpers reportsHelper { get; set; }
        public Dictionary<string, string> currentStepData { get; set; }

        public ContractMonth contractMonth { get; set; }
        public ContractMonth otherContractMonth { get; set; }
        public List<ContractMonth> contractMonths { get; set; }
        public static int LongCallCredit;
        public static int ShortCallCredit;
        public int MaxAvlDays;
        public TestDataContext()
        {
            contractMonth = new ContractMonth();
            otherContractMonth = new ContractMonth();
            contractMonths = new List<ContractMonth>();
        }

        public NullLoggerFactory testLoggerFactory = new NullLoggerFactory();
    }
}
