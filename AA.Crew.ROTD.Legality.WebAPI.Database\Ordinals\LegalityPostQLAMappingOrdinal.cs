using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityPostQLAMappingOrdinal
    {
        /* Oridinal variables */

        internal Int32 PostQLAMappingID;
        internal Int32 PostQLAStateID;
        internal Int32 QLARuleID;
        internal Int32 ContractSectionsID;
        internal Int32 ContractSection;
        internal Int32 LegalityPhaseID;
        internal Int32 QLARule;
        internal Int32 PostQLAState;
        internal Int32 WaiverTypeID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.PostQLAMappingID = sqlDataReader.GetOrdinal("PostQLAMappingID");
            this.PostQLAStateID = sqlDataReader.GetOrdinal("PostQLAStateID");
            this.QLARuleID = sqlDataReader.GetOrdinal("QLARuleID");
            this.ContractSectionsID = sqlDataReader.GetOrdinal("ContractSectionsID");
            this.ContractSection = sqlDataReader.GetOrdinal("ContractSection");
            this.LegalityPhaseID = sqlDataReader.GetOrdinal("LegalityPhaseID");
            this.QLARule = sqlDataReader.GetOrdinal("QLARule");
            this.PostQLAState = sqlDataReader.GetOrdinal("PostQLAState");
            this.WaiverTypeID = sqlDataReader.GetOrdinal("WaiverTypeID");


            this.Initialized = true;
        }
    }
}
