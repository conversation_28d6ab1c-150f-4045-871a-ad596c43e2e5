﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    public class ALMCOR : IROTDRules
    {
        public string RuleName { get; set; }

        private List<string> retRuleName;
        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faActivity, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate baseDate, ref bool possibleIsLegal, ref string rapCode, ref int IsCurrentRAP, List<QLARuleResult> qlaRuleResult, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleName = new List<string>();
            bool flag = true;
            TimeSpan seqCallOutTime = new TimeSpan();
            TimeSpan stbyCallOutTime = new TimeSpan();
            DateTime targetTime = new DateTime();

            if (bidCrewWaiver != null)
            {
                var waiverData = bidCrewWaiver.Where(e => e.CrewMemberId == flightAttendant.EmployeeNumber && e.WaiverTypeID == (int)WaiverTypes.LessThanMinimumCallout).Select(x => x.WaiverSupportingData).FirstOrDefault();

                if (waiverData != null && waiverData.Count > 0)
                {
                    if (sequence != null)
                    {
                        if (waiverData.Where(y => y.BaseCoTerminal == sequence.StartBase).Count() > 0)
                            seqCallOutTime = waiverData.Where(y => y.BaseCoTerminal == sequence.StartBase).Select(t => t.TimeToDeparture).FirstOrDefault();
                        else
                            flag = false;
                    }

                    if (standby != null)
                    {
                        if (waiverData.Where(y => y.BaseCoTerminal == standby.Base).Count() > 0)
                            stbyCallOutTime = waiverData.Where(y => y.BaseCoTerminal == standby.Base).Select(t => t.TimeToDeparture).FirstOrDefault();
                        else
                            flag = false;
                    }
                }

                else
                    flag = false;
            }
            else
                flag = false;


            if (flag && sequence != null)
            {
                targetTime = baseDate.CurrentSysTime > sequence.SequenceStartDateTime ? baseDate.CurrentSysTime : sequence.SequenceStartDateTime;

                if ((sequence.SatelliteStation || !IsBaseCoTerminal))
                {
                    if (baseDate.CurrentSysTime > targetTime.Add(-seqCallOutTime))
                    {
                        flag = false;
                    }
                }
                else if (IsBaseCoTerminal)
                {
                    if (baseDate.CurrentSysTime > targetTime.Add(-seqCallOutTime))
                    {
                        flag = false;
                    }
                }
            }
            else if (flag && standby != null)
            {
                if (IsBaseCoTerminal)
                {
                    if (baseDate.CurrentSysTime > standby.ReportTime.Add(-stbyCallOutTime))
                    {
                        flag = false;
                    }
                }
                else
                {
                    if (baseDate.CurrentSysTime > standby.ReportTime.Add(-stbyCallOutTime))
                    {
                        flag = false;
                    }
                }
            }
            if (!flag)
            {
                possibleIsLegal = false;
                retRuleName.Add(ruleName);
            }
            return retRuleName;
        }
    }
}
