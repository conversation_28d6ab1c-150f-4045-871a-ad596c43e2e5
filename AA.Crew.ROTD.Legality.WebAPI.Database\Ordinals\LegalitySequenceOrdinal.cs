using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalitySequenceOrdinal
    {
        /* Oridinal variables */

        internal Int32 SequenceID;
        internal Int32 RunID;
        internal Int32 SequenceNumber;
        internal Int32 SequencePositionDetailsID;
        internal Int32 SequencePosition;
        internal Int32 OpenTime;
        internal Int32 ExcludeReasonID;
        internal Int32 SequenceStartDateTime;
        internal Int32 DurationInDays;
        internal Int32 TotalCreditCurrentMonth;
        internal Int32 SequenceEndDateTime;
        internal Int32 TotalDutyPeriod;
        internal Int32 CoTerminalStation;
        internal Int32 SatelliteStation;
        internal Int32 OriginationDate;
        internal Int32 TotalCreditNextMonth;
        internal Int32 SequenceDepartureDateTime;
        internal Int32 LayOverStations;
        internal Int32 LegsPerDutyPeriod;
        internal Int32 MultipleEquipments;
        internal Int32 EquipmentGroup;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.SequenceID = sqlDataReader.GetOrdinal("SequenceID");
            this.RunID = sqlDataReader.GetOrdinal("RunID");
            this.SequenceNumber = sqlDataReader.GetOrdinal("SequenceNumber");
            this.SequencePositionDetailsID = sqlDataReader.GetOrdinal("SequencePositionDetailsID");
            this.SequencePosition = sqlDataReader.GetOrdinal("SequencePosition");
            this.OpenTime = sqlDataReader.GetOrdinal("OpenTime");
            this.ExcludeReasonID = sqlDataReader.GetOrdinal("ExcludeReasonID");
            this.SequenceStartDateTime = sqlDataReader.GetOrdinal("SequenceStartDateTime");
            this.DurationInDays = sqlDataReader.GetOrdinal("DurationInDays");
            this.TotalCreditCurrentMonth = sqlDataReader.GetOrdinal("TotalCreditCurrentMonth");
            this.SequenceEndDateTime = sqlDataReader.GetOrdinal("SequenceEndDateTime");
            this.TotalDutyPeriod = sqlDataReader.GetOrdinal("TotalDutyPeriod");
            this.CoTerminalStation = sqlDataReader.GetOrdinal("CoTerminalStation");
            this.SatelliteStation = sqlDataReader.GetOrdinal("SatelliteStation");
            this.OriginationDate = sqlDataReader.GetOrdinal("OriginationDate");
            this.TotalCreditNextMonth = sqlDataReader.GetOrdinal("TotalCreditNextMonth");
            this.SequenceDepartureDateTime = sqlDataReader.GetOrdinal("SequenceDepartureDateTime");
            this.LayOverStations = sqlDataReader.GetOrdinal("LayOverStations");
            this.LegsPerDutyPeriod = sqlDataReader.GetOrdinal("LegsPerDutyPeriod");
            this.MultipleEquipments = sqlDataReader.GetOrdinal("MultipleEquipments");
            this.EquipmentGroup = sqlDataReader.GetOrdinal("EquipmentGroup");


            this.Initialized = true;
        }
    }
}
