using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    //public class LegalitiesWaiverTypeMapper : MapperBase<List<LegalitiesWaiverTypeDTO>, List<LegalitiesWaiverType>>
    //{
    //    public override List<LegalitiesWaiverType> Map(List<LegalitiesWaiverTypeDTO> legalitiesWaiverTypeDtoList)
    //    {
    //        try
    //        {
    //            return legalitiesWaiverTypeDtoList.Select(legalitiesWaiverTypeDto => new LegalitiesWaiverType
    //            {
    //                QLARule = legalitiesWaiverTypeDto.QLARule,
    //                WaiverTypeDescription = legalitiesWaiverTypeDto.WaiverTypeDescription,


    //            }).ToList();
    //        }
    //        catch (Exception)
    //        {
    //            throw;
    //        }
    //    }

    //    public override List<LegalitiesWaiverTypeDTO> Map(List<LegalitiesWaiverType> element)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
}
