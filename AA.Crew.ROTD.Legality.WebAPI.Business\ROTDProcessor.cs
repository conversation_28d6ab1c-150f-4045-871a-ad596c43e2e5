﻿using AA.Crew.Reserves.TimeConversion;
using AA.Crew.ROTD.Legality.WebAPI.Business;
using AA.Crew.ROTD.Legality.WebAPI.Business.Enums;
using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Database;
using AA.Crew.ROTD.Legality.WebAPI.Database.Factories;
using AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.DBEntities;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using BaseDate = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.BaseDate;
using LegalityQLASupportingData = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData;
using Sequence = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence;

namespace AA.Crew.Legalities.ROTD
{
    public class ROTDProcessor : LegalityProcessor, IROTDProcessor
    {
        #region [Private Declaration(s)]
        private ILogger<LegalityProcessor> _loggingProvider;
        private ROTDPhaseDetail phaseDetail;
        private List<ROTDPhaseDetail> phaseDetailList;
        private ROTDPhase phase;
        private List<ROTDPhase> phaseList;
        private ROTDContractSections contract;
        private ILegalityInterpretiveDataProvider _interpretiveDataProvider;



        //private StandBy qlaStdby;
        private List<ROTDRuleDetails> ruleDetailsList;
        private List<BidCrewWaiver> crewWaiverList;

        private List<LegalityRules> ruleDetails;
        int FDcount = 0;
        private string waiver_value;
        private IRuleOrchestrator objRuleOrchestrator;
        private int rowCount;
        private IList<IROTDRules> Rules;
        private List<Activity> filteredActivities;
        private List<string> ruleName;
        private bool _possibleIsLegal;

        private List<ROTDLegalityContextDetails> contextDetails;
        public bool PossibleIsLegal
        {
            get
            {
                return _possibleIsLegal;
            }
            set
            {
                _possibleIsLegal = value;
            }
        }
        private List<QLAResponse> lstQLAResponse = null;
        private List<QLARequest> lstQLARequest = null;
        private List<QLARuleResult> lstQLARuleResult = null;
        private List<BidCrewWaiver> lstBidCrewWaiver = null;
        private List<PostQLAMappingDetails> mappedDetails = null;
        int iPhaseID = 0;

        private string _base;
        private string _rawRequest;
        private string _rawResponse;

        public string Base
        {
            get { return _base; }
            set { _base = value; }
        }
        private DateTime bidOrigination;
        public DateTime BidOriginationDate
        {
            get { return bidOrigination; }
            set { bidOrigination = value; }
        }

        private int runid;

        public int RunId
        {
            get { return runid; }
            set { runid = value; }
        }

        public string RawRequest
        {
            get
            {
                return _rawRequest;
            }
            set
            {
                _rawRequest = value;
            }
        }

        public string RawResponse
        {
            get
            {
                return _rawResponse;
            }
            set
            {
                _rawResponse = value;
            }
        }

        private List<ContextualResult> legalityContextualResult = null;
        public List<ContextualResult> LegalityContextualResult
        {
            get
            {
                return legalityContextualResult;
            }
            set
            {
                legalityContextualResult = value;
            }
        }

        private List<LegalityQLASupportingData> qlaSupportingData = null;
        public List<LegalityQLASupportingData> QLASupportingData
        {
            get
            {
                return qlaSupportingData;
            }
            set
            {
                qlaSupportingData = value;
            }
        }

        private List<getLegalityCrewSequence_Result> legalityCrewSequence = null;
        public List<getLegalityCrewSequence_Result> LegalityCrewSequence
        {
            get
            {
                return legalityCrewSequence;
            }
            set
            {
                legalityCrewSequence = value;
            }
        }
        private bool _executeUnConfiguredRule;

        private List<LegalityErrors> legalityErrorsException;

        public List<LegalityErrors> LegalityErrorsException
        {
            get { return legalityErrorsException; }
            set { legalityErrorsException = value; }
        }

        private ROTDContextualRuleSet _legalityRules;

        public ROTDContextualRuleSet LegalityRules
        {
            get { return _legalityRules; }
            set { _legalityRules = value; }
        }
        #endregion

        #region [Protected Declaration(s)]
        protected new ILogger<LegalityProcessor> Logs
        {
            get
            {
                return _loggingProvider;
            }
        }
        //private IROTDInterpretiveRepository rotdInterpretiveRepository;
        #endregion

        #region [Constructor(s)]
        //public ROTDProcessor(ILogger<ROTDProcessor> logger)
        //{
        //    _loggingProvider = logger;
        //    LegalityErrorsException = new List<LegalityErrors>();
        //    rotdInterpretiveRepository = _container.Resolve<IROTDInterpretiveRepository>(new ParameterOverrides { { "BaseCD", "" }, { "ProcessingDate", "" } });
        //}
        //public ROTDProcessor(int phaseId, List<QLAResponse> lstQlaResponse, List<QLARequest> lstQlaRequest, List<QLARuleResult> lstQlaRuleResult, List<BidCrewWaiver> waiver, List<PostQLAMappingDetails> mappedDetails, int runID)
        //{
        //    _container = IocContainer.Instance;
        //    iPhaseID = phaseId;
        //    this.mappedDetails = mappedDetails;
        //    lstQLARequest = lstQlaRequest;
        //    lstQLAResponse = lstQlaResponse;
        //    lstQLARuleResult = lstQlaRuleResult;
        //    lstBidCrewWaiver = waiver;
        //    LegalityErrorsException = new List<LegalityErrors>();
        //    _loggingProvider = _container.Resolve<ILoggingProvider>();
        //    rotdInterpretiveRepository = _container.Resolve<IROTDInterpretiveRepository>(new ParameterOverrides { { "BaseCD", "" }, { "ProcessingDate", "" } });
        //    rotdInterpretiveRepository.RunId = runID;
        //}

        public ROTDProcessor(ILogger<LegalityProcessor> loggingProvider, IDataProviderFactory dataProviderFactory) : base(loggingProvider)
        {
            _loggingProvider = loggingProvider ?? throw new ArgumentNullException(nameof(loggingProvider));
            _interpretiveDataProvider = dataProviderFactory.GetLegalityInterpretiveDataProvider();
        }

        #endregion

        #region [Method(s)]

        public void InitializeParameters(int phaseId, List<QLAResponse> lstQlaResponse, List<QLARequest> lstQlaRequest, List<QLARuleResult> lstQlaRuleResult, List<BidCrewWaiver> waiver, List<PostQLAMappingDetails> mappedDetails, int runID)
        {
            iPhaseID = phaseId;
            this.mappedDetails = mappedDetails;
            lstQLARequest = lstQlaRequest;
            lstQLAResponse = lstQlaResponse;
            lstQLARuleResult = lstQlaRuleResult;
            lstBidCrewWaiver = waiver;
            LegalityErrorsException = new List<LegalityErrors>();
            phaseDetailList = null;
            //removed as it is not required
            //_interpretiveDataProvider.RunId = runID;
        }

        public ROTDPhase ExecuteProcessor(int subPhase, List<Sequence> sequence, List<Bid> bids, List<FlightAttendant> flightAttendant, List<Activity> faActivity, List<QLAResponse> qlaResponse, List<QLARequest> qlaRequest, List<QLARuleResult> lstQlaRuleResult, List<PostQLAMappingDetails> mappedPostDetails, List<BidCrewWaiver> crewmemberWaiver, BaseDate baseDate, List<StandBy> standby = null, List<RAPShifts> rapShifts = null)
        {
            try
            {
                Logs.LogInformation("Begin ExecuteProcessor-RuleOrchestrator"+ "RuleOrchestrator"+"Legality ROTD Interpretive Process. Phase Id :{0}" , iPhaseID);
                //var CurrentSysTime = _container.Resolve<ITimeConversion>().ConvertUTCToStation(baseDate.BaseName, DateTime.UtcNow);
                var CurrentSysTime = new TimeConversion().ConvertUTCToStation(baseDate.BaseName, DateTime.UtcNow);
                DateTime stationLocalDate = CurrentSysTime.StationLocalTIme;

                //var crewStaticdataService = new CommonMisc.Client.CrewStaticDataService();
                //var stationLocal = crewStaticdataService.ConvertUTCToStation(baseDate.BaseName, DateTime.UtcNow);
                //DateTime stationLocalDate = stationLocal.StationLocalTIme;

                if (phaseDetailList == null)
                {
                    phaseDetailList = new List<ROTDPhaseDetail>();
                }
                phaseList = new List<ROTDPhase>();
                ruleDetails = new List<LegalityRules>();
                //come back on this to check if this works in core
                _executeUnConfiguredRule = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["ExecuteUnConfiguredRule"]);
                if (qlaResponse == null)
                    qlaResponse = new List<QLAResponse>();

                phase = new ROTDPhase();

                var subPhaseMappedPostDetails = mappedPostDetails.Where(m => m.LegalityPhaseID == subPhase).ToList();

                var contractSections = (from con in subPhaseMappedPostDetails
                                        select new
                                        {
                                            ContractSection = con.ContractSection,
                                            ContractSectionsID = con.ContractSectionsID,
                                            LeaglityPhaseID = subPhase
                                        }).Distinct()
                                        .Select(x => new ContractSections
                                        {
                                            ContractSection = x.ContractSection,
                                            ContractSectionsID = x.ContractSectionsID,
                                            LeaglityPhaseID = x.LeaglityPhaseID
                                        }).ToList();

                var missingResponse = (from req in qlaRequest
                                       join res in qlaResponse on req.RequestId equals res.RequestId into misResponse
                                       from res in misResponse.DefaultIfEmpty()
                                       where res == null
                                       select new LegalityErrors()
                                       {
                                           activityId = req.ActivityID,
                                           activityType = req.ActivityType,
                                           appType = "Missing ROTD Requests from CCS",
                                           employeeId = req.EmployeeID,
                                           errorMessage = "Some responses are missing from CCS while compare with requests."
                                       });

                if (missingResponse != null && missingResponse.Count() > 0)
                {
                    LegalityErrorsException.AddRange(missingResponse);
                }

                List<FlightAttendant> flightAttendants;

                if ((ROTDLegalityPhase)subPhase == ROTDLegalityPhase.IsVolunteer)
                {
                    flightAttendants = flightAttendant.Where(fa => fa.IsVolunteer == true).ToList();
                }
                else
                {
                    flightAttendants = flightAttendant.Where(fa => fa.IsVolunteer == false).ToList();
                }

                var groupQlaRuleResult = (from t in lstQlaRuleResult
                                          group t by new { t.RequestId } into grp
                                          select new
                                          {
                                              RequestId = grp.Key.RequestId,
                                              RulesResult = grp.ToList()
                                          }).ToList();

                var interpretiveRequestResponse = (from req in qlaRequest
                                                   join res in qlaResponse on req.RequestId equals res.RequestId
                                                   join fa in flightAttendants on Convert.ToInt32(res.EmployeeID) equals fa.EmployeeNumber
                                                   join rule in groupQlaRuleResult on req.RequestId equals rule.RequestId into qlaR
                                                   from r in qlaR.DefaultIfEmpty()
                                                   join seq in sequence on new { A = req.ActivityID, B = req.PositionCode, C = req.ActivityOriginationDate.Date } equals new { A = seq.SequenceNumber, B = seq.SequencePosition, C = seq.OriginationDate.Date } into ps
                                                   from p in ps.DefaultIfEmpty()
                                                   select new
                                                   {
                                                       qlaResponse = res,
                                                       qlaRequest = req,
                                                       qlaFA = fa,
                                                       qlaSequence = p,
                                                       qlaRuleResult = r != null ? r.RulesResult.Where(b => b.ruleIdentity == res.ruleIdentity || b.isContext == false).ToList() : new List<QLARuleResult>()
                                                   }).ToList();

                foreach (var qlaItem in interpretiveRequestResponse)
                {
                    try
                    {
                        phaseDetail = new ROTDPhaseDetail();
                        phaseDetail.ContractSections = new List<ROTDContractSections>();
                        crewWaiverList = new List<BidCrewWaiver>();

                        if (qlaItem.qlaFA != null && qlaItem.qlaFA.EmployeeNumber > 0)
                        {
                            phase.phaseId = subPhase;
                            phaseDetail.RequestID = qlaItem.qlaRequest.RequestId;
                            phaseDetail.EmployeeNo = qlaItem.qlaResponse.EmployeeID;
                            phaseDetail.ActivityType = qlaItem.qlaRequest.ActivityType;
                            phaseDetail.ActivityID = qlaItem.qlaRequest.ActivityID;
                            phaseDetail.SequencePosition = qlaItem.qlaRequest.ActivityType.ToLower() == QLARequestActivityType.Seq.ToLower() ? qlaItem.qlaRequest.PositionCode : "0";
                            phaseDetail.IsCurrentRAP = qlaItem.qlaResponse.IsCurrentRAP;
                            phaseDetail.FosRAP = qlaItem.qlaResponse.FosRAPActivityCode;
                            phaseDetail.CalculateRAP = qlaItem.qlaResponse.CalRAPActivityCode;
                            phaseDetail.SequenceOriginationDate = qlaItem.qlaRequest.ActivityType.ToLower() == QLARequestActivityType.Seq.ToLower() ? qlaItem.qlaRequest.ActivityOriginationDate : DateTime.MinValue;

                            //Filter waivers using bidCategory
                            if (qlaItem.qlaSequence != null && qlaItem.qlaSequence.SequenceLanguageDetails != null && qlaItem.qlaSequence.SequenceLanguageDetails.Count > 0)
                            {
                                int bidCategoryID = qlaItem.qlaSequence.SequenceLanguageDetails.Any(x => x.LanguageID > 1) ? (int)BidCategory.Speaker : (int)BidCategory.NonSpeaker;
                                crewWaiverList = crewmemberWaiver.Where(bidCW => bidCW.CrewMemberId.ToString() == qlaItem.qlaResponse.EmployeeID && bidCW.BidCategoryID == bidCategoryID).ToList();
                            }
                            if (qlaItem.qlaSequence == null)
                            {
                                crewWaiverList = crewmemberWaiver.Where(bidCW => bidCW.CrewMemberId.ToString() == qlaItem.qlaResponse.EmployeeID && bidCW.BidCategoryID == (int)BidCategory.NonSpeaker).ToList();
                            }

                            FDcount = qlaItem.qlaRuleResult.Where(s => s.Rule.Trim().Equals(QLARuleConst.TOUCHFD) | s.Rule.Trim().Equals(QLARuleConst.TOUCHLH)).Count();

                            List<QLARuleResult> tempQLARuleResult = qlaItem.qlaRuleResult;

                            if (FDcount != 0)
                            {
                                QLARuleResult temp = new QLARuleResult();
                                List<QLARuleResult> tempList = new List<QLARuleResult>();
                                if (qlaItem.qlaRuleResult.Where(s => s.Rule.Trim().Equals(QLARuleConst.TOUCHFD)).Count() > 0)
                                {
                                    temp = qlaItem.qlaRuleResult.Where(s => s.Rule.Trim().Equals(QLARuleConst.TOUCHFD)).Distinct().FirstOrDefault();
                                    tempList.Add(temp);
                                }
                                if (qlaItem.qlaRuleResult.Where(s => s.Rule.Trim().Equals(QLARuleConst.TOUCHLH)).Count() > 0)
                                {
                                    temp = new QLARuleResult();
                                    temp = qlaItem.qlaRuleResult.Where(s => s.Rule.Trim().Equals(QLARuleConst.TOUCHLH)).Distinct().FirstOrDefault();
                                    tempList.Add(temp);
                                }
                                tempQLARuleResult = qlaItem.qlaRuleResult.Where(s => s.Rule.Trim() != QLARuleConst.TOUCHFD && s.Rule.Trim() != QLARuleConst.TOUCHLH).ToList();
                                tempQLARuleResult.AddRange(tempList);
                            }

                            var qlaRuleResultStopCheck = qlaItem.qlaRuleResult.Where(x => x.isContext == false).ToList();
                            ruleName = RefreshingAssignAward(qlaItem.qlaResponse, qlaRuleResultStopCheck);

                            if (ruleName.Count == 0)
                            {
                                #region ContractSection
                                contractSections.ForEach(con =>
                                {

                                    try
                                    {
                                        contract = new ROTDContractSections();
                                        contract.contractSection = con.ContractSection;
                                        contract.contractSectionId = con.ContractSectionsID;

                                        ruleDetailsList = new List<ROTDRuleDetails>();

                                        foreach (QLARuleResult objRuleResult in tempQLARuleResult)
                                        {
                                            var ruleResult = subPhaseMappedPostDetails.Where(x => x.QLARule.Trim() == objRuleResult.Rule.Trim() && x.ContractSectionsID == con.ContractSectionsID)
                                                                                  .Select(x => new
                                                                                  {
                                                                                      QLARule = x.QLARule,
                                                                                      QLARuleID = x.QLARuleID,
                                                                                      PostQLAStateID = x.PostQLAStateID,
                                                                                      WaiverType = x.WaiverTypeID
                                                                                  }).FirstOrDefault();

                                            if (ruleResult != null && ruleResult.QLARule != null)
                                            {
                                                int waiverStatusID = 0;
                                                string state = string.Empty;
                                                // checking FosRAP and CalcRAP both are empty or not
                                                ruleDetailsList.Add(new ROTDRuleDetails
                                                {
                                                    ruleId = ruleResult.QLARuleID,
                                                    ruleName = objRuleResult.Result == ruleResult.QLARule ? ruleResult.QLARule : ruleResult.QLARule + "(" + objRuleResult.Result + ")",
                                                    value = state = CheckWaiverAgainstRule(ruleResult.QLARule, qlaItem.qlaFA, qlaItem.qlaRequest, ruleResult.PostQLAStateID, crewWaiverList, ruleResult.WaiverType, FDcount, ref waiverStatusID),
                                                    isLegal = (phaseDetail.FosRAP == null && phaseDetail.CalculateRAP == null) ? false : (waiverStatusID.Equals((int)ROTDPostQLAStates.NotLegal) ? false : true),
                                                    isOver35By7 = ((ruleResult.QLARule.ToUpper() == QLARuleConst.BLOC35X7.ToString() && waiverStatusID == (int)ROTDPostQLAStates.TrueAlways) ? true : false),
                                                    isOver35By7LH = ((ruleResult.QLARule.ToUpper() == QLARuleConst.BLOC35X7LH.ToString() && waiverStatusID == (int)ROTDPostQLAStates.TrueAlways) ? true : false),
                                                });
                                            }
                                            else if (ruleResult == null && _executeUnConfiguredRule == false)
                                            {
                                                ruleDetailsList.Add(new ROTDRuleDetails
                                                {
                                                    ruleName = objRuleResult.Rule + "(" + objRuleResult.Result + ")",
                                                    isLegal = false
                                                });
                                            }
                                        }




                                        if (ruleDetailsList.Count() > 0)
                                            contract.ruleDetails = ruleDetailsList;

                                        if (contract.ruleDetails != null)
                                            contract.isLegal = contract.ruleDetails.Where(s => s.isLegal == false).Count() > 0 ? false : true;
                                        else
                                            contract.isLegal = true;

                                        phaseDetail.ContractSections.Add(contract);
                                    }
                                    catch (Exception ex)
                                    {
                                        LegalityPhase phaseValues;
                                        phaseValues = ((LegalityPhase)iPhaseID);
                                        LegalityErrorsException.Add(new LegalityErrors { employeeId = (qlaItem == null ? "" : qlaItem.qlaResponse.EmployeeID.ToString()), activityId = con.ContractSectionsID, activityType = "Contract Section ID", appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                                    }
                                });
                                #endregion
                            }
                            else
                            {
                                //Hard Stop Failed Rules
                                ruleDetailsList = new List<ROTDRuleDetails>
                                {
                                    new ROTDRuleDetails
                                    {
                                        ruleName = string.Join("\n", ruleName),
                                        isLegal = false
                                    }
                                };

                                contractSections.ForEach(con =>
                                {
                                    contract = new ROTDContractSections
                                    {
                                        contractSection = con.ContractSection,
                                        contractSectionId = con.ContractSectionsID,
                                        ruleDetails = ruleDetailsList,
                                        isLegal = false
                                    };
                                    phaseDetail.ContractSections.Add(contract);
                                });
                            }

                            bool isFaLangReleased;
                            isFaLangReleased = IsFALanguageReleased(stationLocalDate, qlaItem.qlaFA, qlaItem.qlaRequest, qlaItem.qlaResponse, qlaItem.qlaRuleResult, qlaItem.qlaSequence);

                            // Sequence  scenario
                            if (qlaItem.qlaSequence != null && qlaItem.qlaFA.fALanguageDetails != null && qlaItem.qlaSequence.SequenceLanguageDetails != null)
                            {
                                var matchingRecords = (from lan in qlaItem.qlaFA.fALanguageDetails
                                                       join seqLanguage in qlaItem.qlaSequence.SequenceLanguageDetails on lan.FALang.LanguageID equals seqLanguage.LanguageID
                                                       where seqLanguage.LanguageID != 1
                                                       select new { LanguageID = seqLanguage.LanguageID }
                                                       ).ToList();

                                if (matchingRecords.Count > 0)
                                {
                                    foreach (var i in matchingRecords)
                                    {
                                        phaseDetailList.Add(ContextualSpeakerQual(subPhase, CreatePhaseDetail(i.LanguageID), subPhaseMappedPostDetails));//matching language id
                                    }
                                }
                                bool isSeqLangReleased = qlaItem.qlaSequence.SequenceLanguageDetails.Count > 0 ? qlaItem.qlaSequence.SequenceLanguageDetails.All(x => x.IsExcluded == true) : true;

                                if (qlaItem.qlaSequence.SequenceLanguageDetails.Count == 1 && qlaItem.qlaSequence.SequenceLanguageDetails[0].LanguageID == 1)
                                    isSeqLangReleased = true;

                                //Sick FA, to be considered as both speaker and non-speaker
                                if ((isFaLangReleased && qlaItem.qlaSequence.SequenceLanguageDetails.Count == 0) || (isSeqLangReleased && qlaItem.qlaFA.fALanguageDetails.Count == 0) || (isFaLangReleased && isSeqLangReleased))
                                {
                                    var nonSpeakerPhaseDetail = CreatePhaseDetail(1);
                                    if (ruleName.Count == 0 &&
                                        qlaItem.qlaSequence.SequenceLanguageDetails.Any(x => x.LanguageID > 1) &&
                                        qlaItem.qlaSequence.SequenceLanguageDetails.All(x => x.IsExcluded == true))
                                    {
                                        NonSpeakerInterpretation(nonSpeakerPhaseDetail, qlaItem.qlaFA, qlaItem.qlaRequest, qlaItem.qlaResponse, tempQLARuleResult, subPhaseMappedPostDetails, crewmemberWaiver, qlaItem.qlaSequence, contractSections, subPhase);
                                    }
                                    phaseDetailList.Add(ContextualSpeakerQual(subPhase, nonSpeakerPhaseDetail, subPhaseMappedPostDetails));//non speaker
                                }
                                else if (matchingRecords.Count == 0)
                                {
                                    phaseDetailList.Add(ContextualSpeakerQual(subPhase, phaseDetail, subPhaseMappedPostDetails));//null
                                }
                            }
                            else// StandBy  scenario
                            {
                                if (qlaItem.qlaFA.fALanguageDetails.Count > 0 && qlaItem.qlaFA.fALanguageDetails.Any(x => x.FALang.LanguageID > 1) && !isFaLangReleased)//speaker FA
                                {
                                    phaseDetail.ContractSections.ForEach(x => x.isLegal = false);
                                    phaseDetailList.Add(ContextualSpeakerQual(subPhase, phaseDetail, subPhaseMappedPostDetails));
                                }
                                else
                                {
                                    phaseDetailList.Add(CreatePhaseDetail(1));
                                }
                            }

                        }

                    }
                    catch (Exception ex)
                    {
                        LegalityPhase phaseValues;
                        phaseValues = ((LegalityPhase)iPhaseID);
                        LegalityErrorsException.Add(new LegalityErrors { employeeId = (qlaItem == null ? "" : qlaItem.qlaResponse.EmployeeID.ToString()), activityId = Convert.ToInt64(qlaItem.qlaResponse.RequestId), activityType = "QLAResponse", appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                    }
                }
                phase.QLAResponse = phaseDetailList;

                Logs.LogInformation("End ExecuteProcessor-RuleOrchestrator"+ "RuleOrchestrator"+ "Legality ROTD Interpretive Process. Phase Id :" + iPhaseID + ", Run Id : " + RunId);
            }
            catch (Exception ex)
            {
                Logs.LogError("AA.Crew.Legalities.ROTD", "ROTDProcessor.CheckSequenceStandbyLegal", "Error occurred while executing ROTD Interpretive Process. " , ex.GetAllException());
                throw new Exception("Error occurred while executing ROTD Interpretive Process. " + ex.GetAllException());
            }

            return phase;
        }

        private bool IsFALanguageReleased(DateTime stationLocalDate, FlightAttendant qlaFA, QLARequest qlaRequest, QLAResponse qlaResponse, List<QLARuleResult> qlaRuleResult, Sequence qlaSequence)
        {
            bool isFaLangReleased,isLanguageExcluded, isFANonSpeaker = false;

            // Determine Speaker/Non-Speaker - Check if FA is a Speaker and/or Language has been excluded on UI
            isLanguageExcluded = qlaFA.fALanguageDetails.Count > 0 ? qlaFA.fALanguageDetails.All(x => x.IsExcluded == true) : true;
            if (qlaFA.fALanguageDetails.Count == 1 && qlaFA.fALanguageDetails[0].FALang.LanguageID == 1)
            {
                isFANonSpeaker = true;
                isLanguageExcluded = true;
            }

            // If FA is a non speaker then no need to determine the language release for FA
            if (isFANonSpeaker || !qlaFA.hasOperationalLanguage) return true;

            // If FA is a non speaker then no need to determine the language release for FA
            if (isLanguageExcluded) return true;

            // Determine if process time is before Current RAP time
            if (qlaFA.FACurrentRAPs != null && (stationLocalDate < qlaFA.FACurrentRAPs.Where(f => f.ActivityTypeID == 3 && f.ActivityType == "RAP").Select(d => d.StartDate.Value).FirstOrDefault()))
            {
                // Determine if process time is during Prior RAP time
                if ((stationLocalDate > qlaFA.FACurrentRAPs.Where(f => f.ActivityTypeID == 1 && f.ActivityType == "RAP").Select(d => d.StartDate.Value).FirstOrDefault()) && (stationLocalDate < qlaFA.FACurrentRAPs.Where(f => f.ActivityTypeID == 1 && f.ActivityType == "RAP").Select(d => d.EndDate.Value.AddHours(2)).FirstOrDefault()))
                {
                    // Determine if Activity starts during the Current RAP time
                    if (qlaSequence != null && qlaSequence.SequenceStartDateTime >= qlaFA.FACurrentRAPs.Where(f => f.ActivityTypeID == 3 && f.ActivityType == "RAP").Select(d => d.StartDate.Value).FirstOrDefault())
                    {
                        isFaLangReleased = false;
                    }
                    else
                    {
                        isFaLangReleased = isLanguageExcluded;
                    }
                }
                else
                {
                    isFaLangReleased = isLanguageExcluded;
                }
            }
            else
            {
                isFaLangReleased = isLanguageExcluded;
            }

            if (qlaFA.IsSick)
            {
                isFaLangReleased = true;
            }

            return isFaLangReleased;
        }

        private void NonSpeakerInterpretation(ROTDPhaseDetail nonSpeakerPhaseDetail,
                                              FlightAttendant fa,
                                              QLARequest req,
                                              QLAResponse res,
                                              List<QLARuleResult> tempQLARuleResult,
                                              List<PostQLAMappingDetails> subPhaseMappedPostDetails,
                                              List<BidCrewWaiver> crewmemberWaiver,
                                              Sequence seq,
                                              List<ContractSections> contractSections, int phase)
        {
            List<ROTDContractSections> contractSecList = new List<ROTDContractSections>();

            var crewWaiverList = crewmemberWaiver.Where(bidCW => bidCW.CrewMemberId.ToString() == req.EmployeeID && bidCW.BidCategoryID == (int)BidCategory.NonSpeaker).ToList();

            contractSections.ForEach(con =>
            {
                try
                {
                    contract = new ROTDContractSections();
                    contract.contractSection = con.ContractSection;
                    contract.contractSectionId = con.ContractSectionsID;

                    ruleDetailsList = new List<ROTDRuleDetails>();

                    foreach (QLARuleResult objRuleResult in tempQLARuleResult)
                    {
                        var ruleResult = subPhaseMappedPostDetails.Where(x => x.QLARule.Trim() == objRuleResult.Rule.Trim() && x.ContractSectionsID == con.ContractSectionsID)
                                                                  .Select(x => new
                                                                  {
                                                                      QLARule = x.QLARule,
                                                                      QLARuleID = x.QLARuleID,
                                                                      PostQLAStateID = x.PostQLAStateID,
                                                                      WaiverType = x.WaiverTypeID
                                                                  }).FirstOrDefault();

                        if (ruleResult != null && ruleResult.QLARule != null)
                        {
                            int waiverStatusID = 0;
                            string state = string.Empty;

                            ruleDetailsList.Add(new ROTDRuleDetails
                            {
                                ruleId = ruleResult.QLARuleID,
                                ruleName = objRuleResult.Result == ruleResult.QLARule ? ruleResult.QLARule : ruleResult.QLARule + "(" + objRuleResult.Result + ")",
                                value = state = CheckWaiverAgainstRule(ruleResult.QLARule, fa, req, ruleResult.PostQLAStateID, crewWaiverList, ruleResult.WaiverType, FDcount, ref waiverStatusID),
                                isLegal = (nonSpeakerPhaseDetail.FosRAP == null && nonSpeakerPhaseDetail.CalculateRAP == null) ? false : (waiverStatusID.Equals((int)ROTDPostQLAStates.NotLegal) ? false : true),
                                isOver35By7 = ((ruleResult.QLARule.ToUpper() == QLARuleConst.BLOC35X7.ToString() && waiverStatusID == (int)ROTDPostQLAStates.TrueAlways) ? true : false)
                            });
                        }
                        else if (ruleResult == null && _executeUnConfiguredRule == false)
                        {
                            ruleDetailsList.Add(new ROTDRuleDetails
                            {
                                ruleName = objRuleResult.Rule + "(" + objRuleResult.Result + ")",
                                isLegal = false
                            });
                        }
                    }

                    if (ruleDetailsList.Count() > 0)
                        contract.ruleDetails = ruleDetailsList;

                    if (contract.ruleDetails != null)
                        contract.isLegal = contract.ruleDetails.Where(s => s.isLegal == false).Count() > 0 ? false : true;
                    else
                        contract.isLegal = true;

                    contractSecList.Add(contract);
                    nonSpeakerPhaseDetail.ContractSections = contractSecList;
                }
                catch (Exception ex)
                {
                    LegalityPhase phaseValues;
                    phaseValues = ((LegalityPhase)iPhaseID);
                    LegalityErrorsException.Add(new LegalityErrors { employeeId = (res == null ? "" : res.EmployeeID.ToString()), activityId = con.ContractSectionsID, activityType = "Contract Section ID", appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                }
            });
        }

        #region SpeakerQual
        public ROTDPhaseDetail ContextualSpeakerQual(int subPhase, ROTDPhaseDetail phaseDetail, List<PostQLAMappingDetails> mappedPostDetails)
        {
            if (phaseDetail.LanguageID == null)
            {
                phaseDetail.ContractSections.ForEach(con =>
                {
                    //Filter Speaker Qual details from mapping details.
                    var ruleResultSpeaker = mappedPostDetails.Where(p => p.LegalityPhaseID == subPhase &&
                                                                         p.ContractSectionsID == con.contractSectionId &&
                                                                         p.QLARule.Trim().ToUpper() == QLARuleConst.SPEAKERQUAL)
                                                                         .Select(x => new
                                                                         {
                                                                             QLARule = x.QLARule,
                                                                             QLARuleID = x.QLARuleID,
                                                                             PostQLAStateID = x.PostQLAStateID,
                                                                             WaiverType = x.WaiverTypeID
                                                                         }).FirstOrDefault();

                    int waiverStatusID = 0;
                    string state = string.Empty;

                    //Adding Speaker Qual contextual Rule.
                    List<ROTDRuleDetails> speakerRuleDetails = null;
                    speakerRuleDetails = new List<ROTDRuleDetails>();
                    //speakerRuleDetails = con.ruleDetails;
                    if (con.ruleDetails != null && con.ruleDetails.Count > 0)
                    {
                        foreach (var r in con.ruleDetails)
                        {
                            speakerRuleDetails.Add(new ROTDRuleDetails
                            {
                                ruleId = r.ruleId,
                                ruleName = r.ruleName,
                                isLegal = r.isLegal,
                                isOver35By7 = r.isOver35By7,
                                value = r.value
                            });
                        }
                    }
                    speakerRuleDetails.Add(new ROTDRuleDetails
                    {
                        ruleId = ruleResultSpeaker.QLARuleID,
                        ruleName = ruleResultSpeaker.QLARule,
                        value = state = ROTDRULE.DictionaryWaiverCode[ruleResultSpeaker.PostQLAStateID],
                        isLegal = false,
                        isOver35By7 = false
                    });

                    con.isLegal = false;
                    con.ruleDetails = speakerRuleDetails;
                });
            }
            return phaseDetail;
        }
        #endregion

        public ROTDPhase CheckSequenceStandbyLegal(List<Sequence> sequence, List<Bid> bids, List<FlightAttendant> flightAttendant, List<Activity> faActivity, List<ContractMonth> contractMonthList, List<StandByCredit> standByCreditList, List<BidCrewWaiver> bidCrewWaiver, List<FALanguageDetails> faLanguage, bool IsTheProcessInvokedFromDBUI, BaseDate baseDate, List<RAPShifts> rapShift, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts, int runContextId, List<StandBy> standBy = null)
        {
            try
            {
                Logs.LogInformation("Begin CheckSequenceStandbyLegal-RuleOrchestrator"+ "RuleOrchestrator"+ "Legality ROTD Contextual and Interpretive Process for the Phase Id : " + iPhaseID + ", Run Id : " + RunId);
                //localized the methods used in rule orchestrator
                //objRuleOrchestrator = new RuleOrchestrator();
                phase = new ROTDPhase();
                rowCount = 0;
                contextDetails = new List<ROTDLegalityContextDetails>();
                List<FlightAttendant> crewMembers = null;
                BidOriginationDate = baseDate.ProcessingDate;
                long ruleIdentity = 0;

                List<QLARuleResult> faQLARuleResult = new List<QLARuleResult>();

                if ((ROTDLegalityPhase)iPhaseID == ROTDLegalityPhase.IsVolunteer)
                {
                    crewMembers = flightAttendant.Where(fa => fa.IsVolunteer == true).Select(fa => fa).ToList();
                }
                else
                {
                    crewMembers = flightAttendant.Where(fa => fa.IsVolunteer == false).Select(fa => fa).ToList();
                }

                if (faActivity == null)
                    faActivity = new List<Activity>();

                filteredActivities = faActivity.Where(X => X.ActivityType == ActivityTypes.RAP.ToString()).ToList();

                var groupQlaRuleResult = (from t in lstQLARuleResult
                                          where t.Rule.Trim().ToUpper().Equals(QLARuleConst.TOUCHFD) | t.Rule.Trim().ToUpper().Equals(QLARuleConst.TOUCHGD) | t.Rule.Trim().ToUpper().Equals(QLARuleConst.TOUCHLH)
                                          group t by new { t.RequestId } into grp
                                          select new
                                          {
                                              RequestId = grp.Key.RequestId,
                                              RulesResult = grp.ToList()
                                          }).ToList();


                var groupQlaRequest = (from t in lstQLARequest
                                       group t by new { t.EmployeeID } into grp
                                       select new
                                       {
                                           EmployeeID = grp.Key.EmployeeID,
                                           grpQLARequest = grp.ToList()
                                       }).ToList();

                var groupQlaResponse = (from t in lstQLAResponse
                                        group t by new { t.EmployeeID } into grp
                                        select new
                                        {
                                            EmployeeID = grp.Key.EmployeeID,
                                            grpQLAResponse = grp.ToList()
                                        }).ToList();

                var faQLACrewMembers = (from crew in crewMembers
                                        join grpReq in groupQlaRequest on crew.EmployeeNumber equals Convert.ToInt32(grpReq.EmployeeID)
                                        join grpRes in groupQlaResponse on crew.EmployeeNumber equals Convert.ToInt32(grpRes.EmployeeID)
                                        select new
                                        {
                                            CrewMember = crew,
                                            faQLARequest = grpReq.grpQLARequest.ToList(),
                                            faQLAResponse = grpRes.grpQLAResponse.ToList()
                                        }).ToList();

                //clear existing response and prepare again in contextual
                lstQLAResponse = new List<QLAResponse>();

                foreach (var item in faQLACrewMembers)
                {
                    try
                    {
                        //localize the methods used in rule orchestrator
                        //item.CrewMember.FARAPActivity = objRuleOrchestrator.IsValidFARAPProcessingDate(item.CrewMember, filteredActivities, baseDate, out _possibleIsLegal);
                        item.CrewMember.FARAPActivity = IsValidFARAPProcessingDate(item.CrewMember, filteredActivities, baseDate, out _possibleIsLegal);
                        if (item.CrewMember.FARAPActivity != null && !item.CrewMember.IsSick)
                            item.CrewMember.IsRAP = true;
                        //localize the methods used in rule orchestrator
                        //Rules = objRuleOrchestrator.MapRules(bids, item.CrewMember, runContextId);
                        Rules = MapRules(bids, item.CrewMember, runContextId);

                        string RAPCode = string.Empty;
                        int IsCurrentRAP = 1;

                        var faSequence = (from seq in sequence
                                          join faReq in item.faQLARequest on new { A = seq.SequenceNumber, B = seq.SequencePosition, C = ActivityTypes.SEQ.ToString(), D = seq.OriginationDate.Date } equals new { A = faReq.ActivityID, B = faReq.PositionCode, C = faReq.ActivityType.ToUpper(), D = faReq.ActivityOriginationDate.Date } into ps
                                          from p in ps.DefaultIfEmpty()
                                          join faRes in item.faQLAResponse on (p != null ? p.RequestId : "0") equals faRes.RequestId
                                          join grpRuleResult in groupQlaRuleResult on (p != null ? p.RequestId : "0") equals grpRuleResult.RequestId into rule
                                          from r in rule.DefaultIfEmpty()
                                          select new
                                          {
                                              sequenceRequestResponse = seq,
                                              sequenceRequest = p,
                                              sequenceResponse = faRes,
                                              SequenceQLARuleResult = (r == null ? null : r.RulesResult)
                                          }).ToList();

                        var faStandBy = (from stb in standBy
                                         join faReq in item.faQLARequest on new { A = stb.StandByID, B = ActivityTypes.STB.ToString() } equals new { A = faReq.ActivityID, B = faReq.ActivityType.ToUpper() } into ps
                                         from p in ps.DefaultIfEmpty()
                                         join faRes in item.faQLAResponse on (p != null ? p.RequestId : "0") equals faRes.RequestId
                                         join grpRuleResult in groupQlaRuleResult on (p != null ? p.RequestId : "0") equals grpRuleResult.RequestId into rule
                                         from r in rule.DefaultIfEmpty()
                                         select new
                                         {
                                             standByRequestResponse = stb,
                                             standByRequest = p,
                                             standByResponse = faRes,
                                             StandByQLARuleResult = (r == null ? null : r.RulesResult)
                                         }).ToList();


                        if (item.CrewMember.FACurrentRAPs != null && item.CrewMember.FACurrentRAPs.Count > 0 && !item.CrewMember.IsSick)
                        {
                            int cnt = 0;

                            foreach (Activity act in item.CrewMember.FACurrentRAPs)
                            {
                                try
                                {
                                    //FosRAP Prior D RAP identify.
                                    if (Convert.ToDateTime(baseDate.ProcessingDate).Date > Convert.ToDateTime(act.StartDate).Date)
                                    {
                                        IsCurrentRAP = 0;
                                    }
                                    else
                                    {
                                        IsCurrentRAP = 1;
                                    }
                                    RAPCode = act.ActivityCode;

                                    #region Sequence
                                    faSequence.ForEach(x =>
                                    {
                                        try
                                        {
                                            ruleIdentity++;
                                            ruleName = new List<string>();

                                            if (x.sequenceRequest != null)
                                            {
                                                Rules.ToList().ForEach(s =>
                                                {
                                                    var ruleResult = s.ExecuteRule(x.sequenceRequestResponse, bids, item.CrewMember, act, null, s.RuleName, lstBidCrewWaiver, rapShift, baseDate, ref _possibleIsLegal, ref RAPCode, ref IsCurrentRAP, x.SequenceQLARuleResult, IsBaseCoTerminal, previousMonthRAPShifts);

                                                    if (ruleResult.Count > 0)
                                                    {
                                                        QLARuleResult objQLARuleResult = new QLARuleResult()
                                                        {
                                                            RequestId = x.sequenceRequest.RequestId,
                                                            ActivityID = Convert.ToInt32(x.sequenceRequestResponse.SequenceNumber),
                                                            Rule = s.RuleName,
                                                            Messages = string.Join("\n", ruleResult),
                                                            Result = s.RuleName,
                                                            isContext = true,
                                                            ruleIdentity = ruleIdentity
                                                        };
                                                        faQLARuleResult.Add(objQLARuleResult);
                                                        ruleName.AddRange(ruleResult);
                                                    }

                                                });

                                                if (item.CrewMember.FACurrentRAPs.IndexOf(act) == 0)
                                                {
                                                    lstQLAResponse.Add(new QLAResponse
                                                    {
                                                        EmployeeID = Convert.ToString(item.CrewMember.EmployeeNumber),
                                                        RequestId = x.sequenceRequest.RequestId,
                                                        Valid = x.sequenceResponse.Valid,
                                                        IsLegal = x.sequenceResponse.IsLegal,
                                                        IsContractual = x.sequenceResponse.IsContractual,
                                                        IsQualified = x.sequenceResponse.IsQualified,
                                                        IsAssign = x.sequenceResponse.IsAssign,
                                                        IsAward = x.sequenceResponse.IsAward,
                                                        IscurrentFA = false,
                                                        CalRAPActivityCode = null,
                                                        FosRAPActivityCode = RAPCode,
                                                        IsCurrentRAP = IsCurrentRAP,
                                                        ruleIdentity = ruleIdentity
                                                    });

                                                    rowCount++;
                                                    //Fill entity to save DB and display in UI
                                                    contextDetails.Add(new ROTDLegalityContextDetails
                                                    {
                                                        ContextMessage = String.Empty,
                                                        ReservesCrewSequenceLegalityID = rowCount,
                                                        IsLegal = (_possibleIsLegal == false) ? false : true,
                                                        Message = string.Join("\n", ruleName),
                                                        crewSequenceLegality = new ROTDReservesCrewSequenceLegality
                                                        {
                                                            ROTDReservesCrewSequenceLegalityID = rowCount,
                                                            ReservesCrewMemberID = Convert.ToInt32(item.CrewMember.ReservesCrewMemberID),
                                                            SequencePositionDetailsID = x.sequenceRequestResponse.SequencePositionDetailsID,
                                                            RunID = RunId,
                                                            EmployeeID = item.CrewMember.EmployeeNumber,
                                                            legalityPhases = new LegalityPhases
                                                            {
                                                                LegalityPhase = LegalityPhasesStatus.Contextual.ToString()
                                                            }
                                                        }
                                                    });
                                                }
                                                else
                                                {
                                                    QLAResponse objQLAResponse = new QLAResponse()
                                                    {
                                                        EmployeeID = Convert.ToString(item.CrewMember.EmployeeNumber),
                                                        RequestId = x.sequenceRequest.RequestId,
                                                        Valid = x.sequenceResponse.Valid,
                                                        IsLegal = x.sequenceResponse.IsLegal,
                                                        IsContractual = x.sequenceResponse.IsContractual,
                                                        IsQualified = x.sequenceResponse.IsQualified,
                                                        IsAssign = x.sequenceResponse.IsAssign,
                                                        IsAward = x.sequenceResponse.IsAward,
                                                        IscurrentFA = true,
                                                        CalRAPActivityCode = null,
                                                        FosRAPActivityCode = RAPCode,
                                                        IsCurrentRAP = IsCurrentRAP,
                                                        ruleIdentity = ruleIdentity
                                                    };
                                                    lstQLAResponse.Add(objQLAResponse);
                                                }

                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            LegalityPhase phaseValues;
                                            phaseValues = ((LegalityPhase)iPhaseID);
                                            LegalityErrorsException.Add(new LegalityErrors { employeeId = (item == null ? "" : item.CrewMember.EmployeeNumber.ToString()), activityId = x.sequenceRequestResponse.NumberofLanding, activityType = ActivityTypes.Sequence.ToString(), appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                                        }
                                    });
                                    #endregion

                                    #region Standby

                                    faStandBy.ForEach(x =>
                                    {
                                        try
                                        {
                                            ruleIdentity++;
                                            ruleName = new List<string>();

                                            if (x.standByRequest != null)
                                            {
                                                Rules.ToList().ForEach(s =>
                                                {
                                                    var ruleResult = s.ExecuteRule(null, bids, item.CrewMember, act, x.standByRequestResponse, s.RuleName, lstBidCrewWaiver, rapShift, baseDate, ref _possibleIsLegal, ref RAPCode, ref IsCurrentRAP, x.StandByQLARuleResult, IsBaseCoTerminal, previousMonthRAPShifts);

                                                    if (ruleResult.Count > 0)
                                                    {
                                                        QLARuleResult objQLARuleResult = new QLARuleResult()
                                                        {
                                                            RequestId = x.standByRequest.RequestId,
                                                            ActivityID = Convert.ToInt32(x.standByRequestResponse.StandByID),
                                                            Rule = s.RuleName,
                                                            Messages = string.Join("\n", ruleName),
                                                            Result = s.RuleName,
                                                            isContext = true,
                                                            ruleIdentity = ruleIdentity
                                                        };
                                                        faQLARuleResult.Add(objQLARuleResult);
                                                        ruleName.AddRange(ruleResult);
                                                    }

                                                });

                                                if (item.CrewMember.FACurrentRAPs.IndexOf(act) == 0)
                                                {
                                                    lstQLAResponse.Add(new QLAResponse
                                                    {
                                                        EmployeeID = Convert.ToString(item.CrewMember.EmployeeNumber),
                                                        RequestId = x.standByRequest.RequestId,
                                                        Valid = x.standByResponse.Valid,
                                                        IsLegal = x.standByResponse.IsLegal,
                                                        IsContractual = x.standByResponse.IsContractual,
                                                        IsQualified = x.standByResponse.IsQualified,
                                                        IsAssign = x.standByResponse.IsAssign,
                                                        IsAward = x.standByResponse.IsAward,
                                                        IscurrentFA = false,
                                                        CalRAPActivityCode = null,
                                                        FosRAPActivityCode = RAPCode,
                                                        IsCurrentRAP = IsCurrentRAP,
                                                        ruleIdentity = ruleIdentity
                                                    });

                                                    rowCount++;
                                                    //Fill entity to save DB and display in UI
                                                    contextDetails.Add(new ROTDLegalityContextDetails
                                                    {
                                                        ContextMessage = String.Empty,
                                                        ReservesCrewSequenceLegalityID = rowCount,
                                                        IsLegal = (_possibleIsLegal == false) ? false : true,
                                                        Message = string.Join("\n", ruleName),
                                                        crewSequenceLegality = new ROTDReservesCrewSequenceLegality
                                                        {
                                                            ROTDReservesCrewSequenceLegalityID = rowCount,
                                                            ReservesCrewMemberID = Convert.ToInt32(item.CrewMember.ReservesCrewMemberID),
                                                            StandByID = x.standByRequestResponse.StandByID,
                                                            RunID = RunId,
                                                            EmployeeID = item.CrewMember.EmployeeNumber,
                                                            legalityPhases = new LegalityPhases
                                                            {
                                                                LegalityPhase = LegalityPhasesStatus.Contextual.ToString()
                                                            }
                                                        }
                                                    });
                                                }
                                                else
                                                {
                                                    QLAResponse objQLAResponse = new QLAResponse()
                                                    {
                                                        EmployeeID = Convert.ToString(item.CrewMember.EmployeeNumber),
                                                        RequestId = x.standByRequest.RequestId,
                                                        Valid = x.standByResponse.Valid,
                                                        IsLegal = x.standByResponse.IsLegal,
                                                        IsContractual = x.standByResponse.IsContractual,
                                                        IsQualified = x.standByResponse.IsQualified,
                                                        IsAssign = x.standByResponse.IsAssign,
                                                        IsAward = x.standByResponse.IsAward,
                                                        IscurrentFA = true,
                                                        CalRAPActivityCode = null,
                                                        FosRAPActivityCode = RAPCode,
                                                        IsCurrentRAP = IsCurrentRAP,
                                                        ruleIdentity = ruleIdentity
                                                    };
                                                    lstQLAResponse.Add(objQLAResponse);
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            LegalityPhase phaseValues;
                                            phaseValues = ((LegalityPhase)iPhaseID);
                                            LegalityErrorsException.Add(new LegalityErrors { employeeId = (item == null ? "" : item.CrewMember.EmployeeNumber.ToString()), activityId = x.standByRequestResponse.StandByID, activityType = ActivityTypes.Standby.ToString(), appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                                        }
                                    });
                                    #endregion

                                    cnt = cnt + 1;
                                }
                                catch (Exception ex)
                                {
                                    LegalityPhase phaseValues;
                                    phaseValues = ((LegalityPhase)iPhaseID);
                                    LegalityErrorsException.Add(new LegalityErrors { employeeId = (item == null ? "" : item.CrewMember.EmployeeNumber.ToString()), activityId = act.ActivityID, activityType = ActivityTypes.RAP.ToString(), appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                                }
                            }
                        }
                        else
                        {
                            #region Sequence
                            faSequence.ForEach(x =>
                            {
                                RAPCode = string.Empty;
                                try
                                {
                                    ruleIdentity++;
                                    ruleName = new List<string>();

                                    if (x.sequenceRequest != null)
                                    {
                                        Rules.ToList().ForEach(s =>
                                        {
                                            var ruleResult = s.ExecuteRule(x.sequenceRequestResponse, bids, item.CrewMember, item.CrewMember.FARAPActivity, null, s.RuleName, lstBidCrewWaiver, rapShift, baseDate, ref _possibleIsLegal, ref RAPCode, ref IsCurrentRAP, x.SequenceQLARuleResult, IsBaseCoTerminal, previousMonthRAPShifts);
                                            if (ruleResult.Count > 0)
                                            {
                                                QLARuleResult objQLARuleResult = new QLARuleResult()
                                                {
                                                    RequestId = x.sequenceRequest.RequestId,
                                                    ActivityID = Convert.ToInt32(x.sequenceRequestResponse.SequenceNumber),
                                                    Rule = s.RuleName,
                                                    Messages = string.Join("\n", ruleResult),
                                                    Result = s.RuleName,
                                                    isContext = true,
                                                    ruleIdentity = ruleIdentity
                                                };
                                                faQLARuleResult.Add(objQLARuleResult);
                                                ruleName.AddRange(ruleResult);
                                            }

                                        });

                                        lstQLAResponse.Add(new QLAResponse
                                        {
                                            EmployeeID = Convert.ToString(item.CrewMember.EmployeeNumber),
                                            RequestId = x.sequenceRequest.RequestId,
                                            Valid = x.sequenceResponse.Valid,
                                            IsLegal = x.sequenceResponse.IsLegal,
                                            IsContractual = x.sequenceResponse.IsContractual,
                                            IsQualified = x.sequenceResponse.IsQualified,
                                            IsAssign = x.sequenceResponse.IsAssign,
                                            IsAward = x.sequenceResponse.IsAward,
                                            IscurrentFA = false,
                                            CalRAPActivityCode = item.CrewMember.IsSick ? null : RAPCode,
                                            FosRAPActivityCode = item.CrewMember.IsSick ? RAPCode : null,
                                            IsCurrentRAP = IsCurrentRAP,
                                            ruleIdentity = ruleIdentity
                                        });

                                        rowCount++;
                                        //Fill entity to save DB and display in UI
                                        contextDetails.Add(new ROTDLegalityContextDetails
                                        {
                                            ContextMessage = String.Empty,
                                            ReservesCrewSequenceLegalityID = rowCount,
                                            IsLegal = (_possibleIsLegal == false) ? false : true,
                                            Message = string.Join("\n", ruleName),
                                            crewSequenceLegality = new ROTDReservesCrewSequenceLegality
                                            {
                                                ROTDReservesCrewSequenceLegalityID = rowCount,
                                                ReservesCrewMemberID = Convert.ToInt32(item.CrewMember.ReservesCrewMemberID),
                                                SequencePositionDetailsID = x.sequenceRequestResponse.SequencePositionDetailsID,
                                                RunID = RunId,
                                                EmployeeID = item.CrewMember.EmployeeNumber,
                                                legalityPhases = new LegalityPhases
                                                {
                                                    LegalityPhase = LegalityPhasesStatus.Contextual.ToString()
                                                }
                                            }
                                        });
                                    }
                                }
                                catch (Exception ex)
                                {
                                    LegalityPhase phaseValues;
                                    phaseValues = ((LegalityPhase)iPhaseID);
                                    LegalityErrorsException.Add(new LegalityErrors { employeeId = (item == null ? "" : item.CrewMember.EmployeeNumber.ToString()), activityId = x.sequenceRequestResponse.SequenceNumber, activityType = ActivityTypes.Sequence.ToString(), appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                                }
                            });
                            #endregion

                            #region Standby
                            faStandBy.ForEach(x =>
                            {
                                RAPCode = string.Empty;
                                try
                                {
                                    ruleIdentity++;
                                    ruleName = new List<string>();

                                    if (x.standByRequest != null)
                                    {
                                        Rules.ToList().ForEach(s =>
                                        {
                                            var ruleResult = s.ExecuteRule(null, bids, item.CrewMember, item.CrewMember.FARAPActivity, x.standByRequestResponse, s.RuleName, lstBidCrewWaiver, rapShift, baseDate, ref _possibleIsLegal, ref RAPCode, ref IsCurrentRAP, x.StandByQLARuleResult, IsBaseCoTerminal, previousMonthRAPShifts);

                                            if (ruleResult.Count > 0)
                                            {
                                                QLARuleResult objQLARuleResult = new QLARuleResult()
                                                {
                                                    RequestId = x.standByRequest.RequestId,
                                                    ActivityID = Convert.ToInt32(x.standByRequestResponse.StandByID),
                                                    Rule = s.RuleName,
                                                    Messages = string.Join("\n", ruleName),
                                                    Result = s.RuleName,
                                                    isContext = true,
                                                    ruleIdentity = ruleIdentity
                                                };
                                                faQLARuleResult.Add(objQLARuleResult);
                                                ruleName.AddRange(ruleResult);
                                            }

                                        });

                                        lstQLAResponse.Add(new QLAResponse
                                        {
                                            EmployeeID = Convert.ToString(item.CrewMember.EmployeeNumber),
                                            RequestId = x.standByRequest.RequestId,
                                            Valid = x.standByResponse.Valid,
                                            IsLegal = x.standByResponse.IsLegal,
                                            IsContractual = x.standByResponse.IsContractual,
                                            IsQualified = x.standByResponse.IsQualified,
                                            IsAssign = x.standByResponse.IsAssign,
                                            IsAward = x.standByResponse.IsAward,
                                            IscurrentFA = false,
                                            CalRAPActivityCode = item.CrewMember.IsSick ? null : RAPCode,
                                            FosRAPActivityCode = item.CrewMember.IsSick ? RAPCode : null,
                                            IsCurrentRAP = IsCurrentRAP,
                                            ruleIdentity = ruleIdentity
                                        });

                                        rowCount++;
                                        //Fill entity to save DB 
                                        contextDetails.Add(new ROTDLegalityContextDetails
                                        {
                                            ContextMessage = String.Empty,
                                            ReservesCrewSequenceLegalityID = rowCount,
                                            IsLegal = (_possibleIsLegal == false) ? false : true,
                                            Message = string.Join("\n", ruleName),
                                            crewSequenceLegality = new ROTDReservesCrewSequenceLegality
                                            {
                                                ROTDReservesCrewSequenceLegalityID = rowCount,
                                                ReservesCrewMemberID = Convert.ToInt32(item.CrewMember.ReservesCrewMemberID),
                                                StandByID = x.standByRequestResponse.StandByID,
                                                RunID = RunId,
                                                EmployeeID = item.CrewMember.EmployeeNumber,
                                                legalityPhases = new LegalityPhases
                                                {
                                                    LegalityPhase = LegalityPhasesStatus.Contextual.ToString()
                                                }
                                            }
                                        });
                                    }
                                }
                                catch (Exception ex)
                                {
                                    LegalityPhase phaseValues;
                                    phaseValues = ((LegalityPhase)iPhaseID);
                                    LegalityErrorsException.Add(new LegalityErrors { employeeId = (item == null ? "" : item.CrewMember.EmployeeNumber.ToString()), activityId = x.standByRequestResponse.StandByID, activityType = ActivityTypes.Standby.ToString(), appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                                }
                            });
                            #endregion
                        }
                    }
                    catch (Exception ex)
                    {
                        LegalityPhase phaseValues;
                        phaseValues = ((LegalityPhase)iPhaseID);
                        LegalityErrorsException.Add(new LegalityErrors { employeeId = (item == null ? "" : item.CrewMember.EmployeeNumber.ToString()), appType = phaseValues.ToString(), errorMessage = ex.Message ?? "" + ex.StackTrace ?? "" });
                    }
                }

                if (faQLARuleResult.Count > 0)
                    lstQLARuleResult.AddRange(faQLARuleResult);

                //Interpretive Rule                
                phase = ExecuteProcessor(iPhaseID, sequence, bids, flightAttendant, faActivity, lstQLAResponse, lstQLARequest, lstQLARuleResult, mappedDetails, bidCrewWaiver, baseDate, standBy, rapShift);

                try
                {
                    //To Update the contextual repository with isover35by7 based on the interpretation 
                    var contracts = phase.QLAResponse.SelectMany(c => c.ContractSections, (c, message) => new { c, message }).ToList();

                    var over35By7Data = contracts.Where(x => x.message.ruleDetails != null &&
                                                             x.message.ruleDetails.Any(r => r.ruleName.ToUpper().Replace("(IL)", "").Replace("(NC)", "").Replace("(NQ)", "") == QLARuleConst.BLOC35X7.ToString() && r.isOver35By7 == true))
                                                 .Select(x => new
                                                 {
                                                     EmployeeNo = x.c.EmployeeNo,
                                                     ActivityID = x.c.ActivityID,
                                                     ActivityType = x.c.ActivityType.ToUpper(),
                                                     SequencePosition = x.c.SequencePosition,
                                                     SequenceOriginationDate = x.c.SequenceOriginationDate
                                                 }).Distinct().ToList();

                    foreach (var over35By7 in over35By7Data)
                    {

                        var sequencePositionId = (from seq in sequence
                                                  where seq.SequencePosition == over35By7.SequencePosition &&
                                                        seq.SequenceNumber == over35By7.ActivityID &&
                                                        seq.OriginationDate.Date == over35By7.SequenceOriginationDate.Date &&
                                                        over35By7.ActivityType.ToUpper() == ActivityTypes.SEQ.ToString()
                                                  select seq.SequencePositionDetailsID).FirstOrDefault();

                        contextDetails.Select(x => x.crewSequenceLegality)
                                     .Where(y => y.EmployeeID == Convert.ToInt32(over35By7.EmployeeNo) &&
                                                 ((sequencePositionId > 0 && sequencePositionId == y.SequencePositionDetailsID) || (over35By7.ActivityType.ToUpper() == ActivityTypes.STB.ToString() && y.StandByID == over35By7.ActivityID))).ToList()
                                     .ForEach(f =>
                                     {
                                         f.IsOver35By7 = true;
                                     });

                    }
                    //To Update the contextual repository with isover35by7LH based on the interpretation 
                    var over35By7LHData = contracts.Where(x => x.message.ruleDetails != null &&
                                                             x.message.ruleDetails.Any(r => r.ruleName.ToUpper().Replace("(IL)", "").Replace("(NC)", "").Replace("(NQ)", "") == QLARuleConst.BLOC35X7LH.ToString() && r.isOver35By7LH == true))
                                                 .Select(x => new
                                                 {
                                                     EmployeeNo = x.c.EmployeeNo,
                                                     ActivityID = x.c.ActivityID,
                                                     ActivityType = x.c.ActivityType.ToUpper(),
                                                     SequencePosition = x.c.SequencePosition,
                                                     SequenceOriginationDate = x.c.SequenceOriginationDate
                                                 }).Distinct().ToList();

                    foreach (var over35By7LH in over35By7LHData)
                    {

                        var sequencePositionId = (from seq in sequence
                                                  where seq.SequencePosition == over35By7LH.SequencePosition &&
                                                        seq.SequenceNumber == over35By7LH.ActivityID &&
                                                        seq.OriginationDate.Date == over35By7LH.SequenceOriginationDate.Date &&
                                                        over35By7LH.ActivityType.ToUpper() == ActivityTypes.SEQ.ToString()
                                                  select seq.SequencePositionDetailsID).FirstOrDefault();

                        contextDetails.Select(x => x.crewSequenceLegality)
                                     .Where(y => y.EmployeeID == Convert.ToInt32(over35By7LH.EmployeeNo) &&
                                                 ((sequencePositionId > 0 && sequencePositionId == y.SequencePositionDetailsID) || (over35By7LH.ActivityType.ToUpper() == ActivityTypes.STB.ToString() && y.StandByID == over35By7LH.ActivityID))).ToList()
                                     .ForEach(f =>
                                     {
                                         f.IsOver35By7LH = true;
                                     });
                    }
                }
                catch (Exception ex)
                {
                    Logs.LogError("AA.Crew.Legalities.ROTD"+ "ROTDProcessor.CheckSequenceStandbyLegal"+ "Error occurred while Updating the contextual repository with isover35by7 based on the interpretation. " + ex.GetAllException());
                    throw new Exception("Error occurred while Updating the contextual repository with isover35by7 based on the interpretation. " + ex.GetAllException());
                }

                if (IsTheProcessInvokedFromDBUI)
                {
                    Logs.LogInformation("Begin ExecuteProcessor-RuleOrchestrator "  +"RuleOrchestrator "+ "Save the Interpretive details to database. Phase Id:"+iPhaseID);
                    
                    SaveROTDLegality(sequence, standBy);
                    List<RcsDetails> lstRcs = new List<RcsDetails>();
                    SaveQLADetails(sequence, standBy, lstRcs);
                    SaveROTDInterpretiveLegality(sequence, standBy, lstRcs);
                    lstRcs = null;
                    Logs.LogInformation("End ExecuteProcessor-RuleOrchestrator " +"RuleOrchestrator "+ "Save the Interpretive details to database. Phase Id:"+iPhaseID);
                }

                Logs.LogInformation("End CheckSequenceStandbyLegal-RuleOrchestrator" + "RuleOrchestrator"+ "Legality ROTD Contextual and Interpretive Process for the Phase Id : " +iPhaseID + " ,Run Id :" + RunId);
            }
            catch (Exception ex)
            {
                throw new Exception("Error occurred while start ROTD Process. " + ex.GetAllException());
            }
            return phase;
        }
        
        
        public void SaveROTDLegality(List<Sequence> sequence, List<StandBy> standBy)
        {
            //List<RcsDetails> lstRcs = new List<RcsDetails>();
            try
            {
                if ((ROTDLegalityPhase)iPhaseID == ROTDLegalityPhase.IsVolunteer || (ROTDLegalityPhase)iPhaseID == ROTDLegalityPhase.NonVolunteer || (ROTDLegalityPhase)iPhaseID == ROTDLegalityPhase.NonVolunteerStandBy)
                {
                    // Save Contextual details
                    _interpretiveDataProvider.SaveROTDLegality(contextDetails, RunId, iPhaseID);
                }
                else
                {
                    _interpretiveDataProvider.SetContextDetailsReservesCrewSequenceLegalityID(contextDetails, RunId);
                }
            }
            catch (Exception ex)
            {
                Logs.LogError("AA.Crew.Legalities.ROTD "+ "ROTDProcessor.SaveROTDLegality"+ "Error occurred Save ROTD Legality Details. " + ex.GetAllException());
                throw new Exception("Error occurred Save ROTD Legality Details. " + ex.GetAllException());
            }
        }

        public void SaveQLADetails(List<Sequence> sequence, List<StandBy> standBy, List<RcsDetails> lstRcs)
        {
            try
            {
                // Save QLA details
                lstRcs = _interpretiveDataProvider.SaveQLADetails(sequence, standBy, contextDetails, lstQLAResponse, lstQLARequest, lstQLARuleResult, lstRcs, RunId, iPhaseID, phase);
            }
            catch (Exception ex)
            {
                Logs.LogError("AA.Crew.Legalities.ROTD " + "ROTDProcessor.SaveROTDLegality " + "Error occured Save QLA Details. " + ex.GetAllException());
                throw new Exception("Error occurred Save QLA Details. " + ex.GetAllException());
            }
        }

        public void SaveROTDInterpretiveLegality(List<Sequence> sequence, List<StandBy> standBy, List<RcsDetails> lstRcs)
        {
            try
            {
                // Save ROTD interpretive 
                _interpretiveDataProvider.SaveROTDInterpretiveLegality(sequence, standBy, contextDetails, phase, lstRcs, RunId, iPhaseID, QLASupportingData, BidOriginationDate);
            }
            catch (Exception ex)
            {
                Logs.LogError("AA.Crew.Legalities.ROTD " + "ROTDProcessor.SaveROTDLegality " + "Error occurred Save ROTD Interpretive Legality Details. " + ex.GetAllException());
                throw new Exception("Error occurred Save ROTD Interpretive Legality Details. " + ex.GetAllException());
            }
        }

        public string CheckWaiverAgainstRule(string rule, FlightAttendant fa, QLARequest req, long ruleStateId, List<BidCrewWaiver> lstWaiver, string waiverType, int fdCount, ref int waiverStatusId)
        {
            string RuleState = ROTDRULE.DictionaryWaiverCode[ruleStateId];

            if ((waiverType != "0") && RuleState == ROTDRULE.DictionaryWaiverCode[2])
            {
                if (lstWaiver != null && lstWaiver.Count > 0)
                {
                    var waiver = lstWaiver.Where(s => s.WaiverTypeID.ToString().Equals(waiverType)).FirstOrDefault();
                    if (waiver != null)
                    {
                        waiver_value = waiver.IsActive ? PostQLAStatesConst.TrueAlways : PostQLAStatesConst.NotLegal;
                    }
                    else
                    {
                        waiver_value = PostQLAStatesConst.NotLegal;
                    }
                }
                else
                {
                    waiver_value = PostQLAStatesConst.NotLegal;
                }
            }
            else if ((rule.ToUpper().Equals(QLARuleConst.TOUCHFD) || (rule.ToUpper().Equals(QLARuleConst.TOUCHLH)) && fdCount > 0))
            {
                if (RuleState == ROTDRULE.DictionaryWaiverCode[4])
                    waiver_value = fdCount > 0 && fdCount < 2 ? PostQLAStatesConst.TrueAlways : PostQLAStatesConst.NotLegal;
                else if (RuleState == ROTDRULE.DictionaryWaiverCode[5])
                    waiver_value = fdCount >= 2 ? PostQLAStatesConst.TrueAlways : PostQLAStatesConst.NotLegal;
                else
                    waiver_value = RuleState;
            }
            else
            {
                waiver_value = RuleState;
            }

            if (waiver_value == PostQLAStatesConst.TrueAlways)
                waiverStatusId = (int)ROTDPostQLAStates.TrueAlways;
            else if (waiver_value == PostQLAStatesConst.NotLegal)
                waiverStatusId = (int)ROTDPostQLAStates.NotLegal;
            else
                waiverStatusId = (int)ruleStateId;

            return waiver_value;
        }
        public RAPShifts FilterCurrentRAPShifts(Sequence sequence, StandBy standBy, List<RAPShifts> rapShifts, DateTime processingDate)
        {
            List<RAPShifts> objRAPShift = new List<RAPShifts>();
            DateTime date = processingDate.AddDays(1);
            DateTime proDate = processingDate;
            DateTime coTerminalDate = processingDate;
            DateTime priorDate = processingDate.AddDays(-1);
            if (sequence != null)
            {
                foreach (RAPShifts item in rapShifts)
                {
                    if (((!sequence.CoTerminalStation && !sequence.SatelliteStation) || sequence.SatelliteStation))
                    {
                        DateTime priorEndDate = new DateTime();
                        if (item.StartDateTime > item.EndDateTime)
                        {
                            priorEndDate = Convert.ToDateTime(priorDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            if (sequence.SequenceStartDateTime >= Convert.ToDateTime(priorDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(2)).ToLongTimeString()) && sequence.SequenceReportDateTime <= priorEndDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = priorEndDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = priorEndDate, Shift = item.Shift });
                            }
                        }
                        proDate = processingDate;
                        while (proDate <= date)
                        {
                            DateTime endDate = new DateTime();
                            if (item.StartDateTime > item.EndDateTime)
                            {
                                endDate = Convert.ToDateTime(proDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            }
                            else
                            {
                                endDate = Convert.ToDateTime(proDate.ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            }
                            if (sequence.SequenceStartDateTime >= Convert.ToDateTime(proDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(2)).ToLongTimeString()) && sequence.SequenceReportDateTime <= endDate.Add(TimeSpan.FromHours(2)))
                            {
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = endDate, Shift = item.Shift });
                            }

                            proDate = proDate.AddDays(1);
                        }
                    }
                    else if (sequence.CoTerminalStation)
                    {
                        DateTime priorEndDate = new DateTime();
                        if (item.StartDateTime > item.EndDateTime)
                        {
                            priorEndDate = Convert.ToDateTime(priorDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            if (sequence.SequenceStartDateTime >= Convert.ToDateTime(priorDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(3)).ToLongTimeString()) && sequence.SequenceReportDateTime <= priorEndDate.Add(TimeSpan.FromHours(2)))
                            {
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = priorEndDate, Shift = item.Shift });
                            }
                        }
                        coTerminalDate = processingDate;
                        while (coTerminalDate <= date)
                        {
                            DateTime endDate = new DateTime();
                            if (item.StartDateTime > item.EndDateTime)
                            {
                                endDate = Convert.ToDateTime(coTerminalDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            }
                            else
                            {
                                endDate = Convert.ToDateTime(coTerminalDate.ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            }
                            if (sequence.SequenceStartDateTime >= Convert.ToDateTime(coTerminalDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(3)).ToLongTimeString()) && sequence.SequenceReportDateTime <= endDate.Add(TimeSpan.FromHours(2)))
                            {
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = endDate, Shift = item.Shift });
                            }

                            coTerminalDate = coTerminalDate.AddDays(1);
                        }
                    }
                }
            }
            else if (standBy != null)
            {
                foreach (RAPShifts item in rapShifts)
                {
                    if (!standBy.CoTerminalStation)
                    {
                        DateTime priorEndDate = new DateTime();
                        if (item.StartDateTime > item.EndDateTime)
                        {
                            priorEndDate = Convert.ToDateTime(priorDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            if (standBy.ReportTime >= Convert.ToDateTime(priorDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(2)).ToLongTimeString()) && standBy.ReportTime.Add(TimeSpan.FromHours(standBy.Duration)) <= priorEndDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = priorEndDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = priorEndDate, Shift = item.Shift });
                            }
                        }
                        proDate = processingDate;
                        while (proDate <= date)
                        {
                            DateTime endDate = new DateTime();
                            if (item.StartDateTime > item.EndDateTime)
                            {
                                endDate = Convert.ToDateTime(proDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            }
                            else
                            {
                                endDate = Convert.ToDateTime(proDate.ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            }
                            if (standBy.ReportTime >= Convert.ToDateTime(proDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(2)).ToLongTimeString()) && standBy.ReportTime.Add(TimeSpan.FromHours(standBy.Duration)) <= endDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = endDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = endDate, Shift = item.Shift });
                            }

                            proDate = proDate.AddDays(1);
                        }
                    }
                    else if (standBy.CoTerminalStation)
                    {
                        DateTime priorEndDate = new DateTime();
                        if (item.StartDateTime > item.EndDateTime)
                        {
                            priorEndDate = Convert.ToDateTime(priorDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            if (standBy.ReportTime >= Convert.ToDateTime(priorDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(3)).ToLongTimeString()) && standBy.ReportTime.Add(TimeSpan.FromHours(standBy.Duration)) <= priorEndDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = priorEndDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = priorEndDate, Shift = item.Shift });
                            }
                        }
                        coTerminalDate = processingDate;
                        while (coTerminalDate <= date)
                        {
                            DateTime endDate = new DateTime();
                            if (item.StartDateTime > item.EndDateTime)
                            {
                                endDate = Convert.ToDateTime(coTerminalDate.Add(TimeSpan.FromHours(24)).ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            }
                            else
                            {
                                endDate = Convert.ToDateTime(coTerminalDate.ToShortDateString() + " " + item.EndDateTime.ToLongTimeString());
                            }
                            if (standBy.ReportTime >= Convert.ToDateTime(coTerminalDate.ToShortDateString() + " " + item.StartDateTime.Add(TimeSpan.FromHours(3)).ToLongTimeString()) && standBy.ReportTime.Add(TimeSpan.FromHours(standBy.Duration)) <= endDate.Add(TimeSpan.FromHours(2)))
                            {
                                //item.EndDateTime = endDate;
                                objRAPShift.Add(new RAPShifts { StartDateTime = item.StartDateTime, EndDateTime = endDate, Shift = item.Shift });
                            }

                            coTerminalDate = coTerminalDate.AddDays(1);
                        }
                    }
                }
            }
            return objRAPShift.OrderBy(x => x.EndDateTime).FirstOrDefault();
        }

        public ROTDPhaseDetail CreatePhaseDetail(Nullable<int> langId)
        {
            ROTDPhaseDetail phaseDetail1 = new ROTDPhaseDetail
            {
                RequestID = phaseDetail.RequestID,
                ActivityID = phaseDetail.ActivityID,
                ActivityType = phaseDetail.ActivityType,
                CalculateRAP = phaseDetail.CalculateRAP,
                ContractSections = phaseDetail.ContractSections,
                EmployeeNo = phaseDetail.EmployeeNo,
                ROTDPhaseDetailId = phaseDetail.ROTDPhaseDetailId,
                SequencePosition = phaseDetail.SequencePosition,
                FosRAP = phaseDetail.FosRAP,
                IsCurrentRAP = phaseDetail.IsCurrentRAP,
                SequenceOriginationDate = phaseDetail.SequenceOriginationDate,
                LanguageID = langId
            };
            return phaseDetail1;
        }

        public Activity IsValidFARAPProcessingDate(FlightAttendant flightAttendant, List<Activity> faActivity, BaseDate basedate, out bool isValidFaRap)
        {
            Activity objRAPAct = null;
            try
            {
                flightAttendant.FACurrentRAPs = faActivity.Where(X => X.ActivityType == ActivityTypes.RAP.ToString() && X.Employeenumber == flightAttendant.ReservesCrewMemberID && (basedate.ProcessingDate.Date.ToShortDateString() == X.StartDate.Value.Date.ToShortDateString() || basedate.ProcessingDate.Date.ToShortDateString() == X.EndDate.Value.Date.ToShortDateString())).ToList();
                objRAPAct = flightAttendant.FACurrentRAPs.OrderBy(x => x.EndDate).FirstOrDefault();

                if (objRAPAct != null)
                {
                    isValidFaRap = true;
                }
                else
                {
                    isValidFaRap = false;
                }
                return objRAPAct;
            }
            catch (Exception ex)
            {
                Logs.LogError("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.IsValidFARAPProcessingDate" + ex.Message ?? "" + ex.StackTrace ?? "");
                throw;
            }
        }

        public List<IROTDRules> MapRules(List<Bid> bids, FlightAttendant flightAttendant, int runContextId)
        {
            List<IROTDRules> baseRule = new List<IROTDRules>();
            try
            {
                ROTDContextualRuleSet legalityRule = new ROTDContextualRuleSet();
                legalityRule.MappedRules = new List<ROTDContextualRule>();

                if (LegalityRules == null)
                {
                    LegalityRules = new ROTDContextualRuleSet();
                    LegalityRules.MappedRules = _interpretiveDataProvider.GetAllContextualMappedRules("ROTD").Result;
                }


                if (runContextId == (int)RunContextsEnum.MinCall)
                {
                    if (flightAttendant.IsRAP == true) //RAP Rules Logic
                    {
                        legalityRule.MappedRules = LegalityRules.MappedRules.Where(s => s.RuleTypeName.Equals(ROTDRuleCategory.MinCallRAP.ToString())).Select(s => s).ToList();
                    }
                    else if (flightAttendant.IsRAP == false) //NoRAP Rules Logic
                    {
                        legalityRule.MappedRules = LegalityRules.MappedRules.Where(s => s.RuleTypeName.Equals(ROTDRuleCategory.MinCallNoRAP.ToString())).Select(s => s).ToList();
                    }
                }
                else
                {
                    if (flightAttendant.IsRAP == true) //RAP Rules Logic
                    {
                        legalityRule.MappedRules = LegalityRules.MappedRules.Where(s => s.RuleTypeName.Equals(ROTDRuleCategory.RAP.ToString())).Select(s => s).ToList();
                    }
                    else if (flightAttendant.IsRAP == false) //NoRAP Rules Logic
                    {
                        legalityRule.MappedRules = LegalityRules.MappedRules.Where(s => s.RuleTypeName.Equals(ROTDRuleCategory.NoRAP.ToString())).Select(s => s).ToList();
                    }
                }


                foreach (var mappedRule in legalityRule.MappedRules)
                {
                    IROTDRules rule;
                    Type ruleObj = Type.GetType(mappedRule.RuleClass + ", AA.Crew.ROTD.Legality.WebAPI.Business");
                    rule = (IROTDRules)Activator.CreateInstance(ruleObj);
                    rule.RuleName = mappedRule.RuleName;
                    baseRule.Add(rule);
                }
            }
            catch (Exception ex)
            {
                Logs.LogError("AA.Crew.Legalities.ROTD.RuleOrchestrator "+ "MapRules "+ ex.Message ?? "" + ex.StackTrace ?? "");
                throw;
            }

            return baseRule;
        }

        #endregion
    }
}