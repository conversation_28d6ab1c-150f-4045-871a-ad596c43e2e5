﻿using AA.Crew.ROTD.Legality.WebAPI.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Operations
{
    public static class ExtensionMethods
    {
        public static T GetValue<T>(this IList<AppSettings> appSettings, string settingName)
        {
            T result = default(T);
            if (appSettings == null || !appSettings.Any() || string.IsNullOrWhiteSpace(settingName))
                return result;
            try
            {
                var appSetting = appSettings.Where(x => x.SettingName.Trim().ToUpper() == settingName.Trim().ToUpper()).FirstOrDefault();
                if (appSetting == null)
                    return result;
                result = (T)Convert.ChangeType(appSetting.SettingValues, typeof(T));
            }
            catch
            {
                //Could not convert.  Pass back default value...
                result = default(T);
            }
            return result;
        }
    }
}
