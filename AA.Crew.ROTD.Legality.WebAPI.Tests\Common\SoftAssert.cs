﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests.SoftAssert
{
    //
    // Summary:
    //     A collection of helper classes to test various conditions within unit tests.
    //     If the condition being tested is not met, an exception is thrown.
    public sealed class SoftAssert
    {
        //
        // Summary:
        //     Gets the singleton instance of the Assert functionality.
        //
        // Remarks:
        //     Users can use this to plug-in custom assertions through C# extension methods.
        //     For instance, the signature of a custom assertion provider could be "public static
        //     void IsOfType<T>(this Assert assert, object obj)" Users could then use a syntax
        //     similar to the default assertions which in this case is "Assert.That.IsOfType<Dog>(animal) {     try { Assert.AreEqual(PPPPPPPP); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); }     }"
        //     More documentation is at "https://github.com/Microsoft/testfx-docs".
        public static Assert That { get; }


        #region Private Variables

        /// <summary>
        /// Holds list of Assert failures of Single step. (Single step can have multiple Assert statements)
        /// </summary>
        private static List<Exception> StepErrors = null;

        /// <summary>
        /// Holds list of Assert failures of Complete Scenario.
        /// </summary>
        private static List<Exception> ScenarioErrors = null;

        /// <summary>
        ///  Holds Count of Total Step Errors for Entire scenario. 
        /// </summary>
        private static int TotalStepErrorCount = 0;

        private static int TotalStepErrorCountForParent = 0;


        private static void AddStepErrors(Exception ex)
        {
            StepErrors.Add(ex);
            ScenarioErrors.Add(ex);
            TotalStepErrorCount++;
            TotalStepErrorCountForParent++;
        }

        #endregion

        #region Public methods

        /// <summary>
        /// Recommended Usage: 
        ///     * [AfterStep] Hook: Call GetStepErrors() to write errors of current step into log.
        /// </summary>
        public static List<Exception> GetStepErrors()
        {
            return StepErrors;
        }

        /// <summary>
        /// Recommended Usage: 
        ///     * [BeforeStep] Hook: Create a new error list 
        ///     * [AfterStep] Hook: Reset the error list after writing into log
        /// </summary>
        public static void ResetStepErrors()
        {
            StepErrors = new List<Exception>();
        }

        /// <summary>
        /// Recommended Usage: 
        ///     * [BeforeScenario] Hook: Create a new error list 
        ///     * [AfterScenario] Hook: Reset the error list after writing into log
        /// </summary>
        public static void ResetScenarioErrors()
        {
            ScenarioErrors = new List<Exception>();
        }



        /// <summary>
        ///  Recommended Usage: [AfterScenario]
        /// </summary>
        /// <returns></returns>
        public static int GetTotalStepErrorCount()
        {
            return TotalStepErrorCount;
        }

        public static int GetTotalStepErrorCountForParent()
        {
            return TotalStepErrorCountForParent;
        }

        /// <summary>
        /// Recommended Usage: [BeforeScenario]
        /// </summary>
        public static void ResetTotalStepErrorCount()
        {
            TotalStepErrorCount = 0;
        }

        public static void ResetTotalStepErrorCountForParentCount()
        {
            TotalStepErrorCountForParent = 0;
        }

        /// <summary>
        /// Recommended Usage: 
        /// SoftAssertSteps [Then(@"Review all soft assertions to determine test has no failed steps\.")]
        /// </summary>
        public static void ReviewAll()
        {
            if (TotalStepErrorCountForParent > 0)
            {
                string errorList = "";
                foreach (var err in ScenarioErrors)
                {
                    errorList += "\n" + err.Message + "\n";
                }
                Assert.Fail($"\n {TotalStepErrorCountForParent} SoftAssert step(s) failed. \n" + errorList);
            }
        }


        #endregion

        //
        // Summary:
        //     Tests whether the specified values are equal and throws an exception if the two
        //     values are not equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   expected:
        //     The first value to compare. This is the value the tests expects.
        //
        //   actual:
        //     The second value to compare. This is the value produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is not equal to expected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Type parameters:
        //   T:
        //     The type of values to compare.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual<T>(T expected, T actual, string message, params object[] parameters)
        { try { Assert.AreEqual(expected, actual, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects are equal and throws an exception if the
        //     two objects are not equal. Different numeric types are treated as unequal even
        //     if the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   expected:
        //     The first object to compare. This is the object the tests expects.
        //
        //   actual:
        //     The second object to compare. This is the object produced by the code under test.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(object expected, object actual)
        { try { Assert.AreEqual(expected, actual); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects are equal and throws an exception if the
        //     two objects are not equal. Different numeric types are treated as unequal even
        //     if the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   expected:
        //     The first object to compare. This is the object the tests expects.
        //
        //   actual:
        //     The second object to compare. This is the object produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is not equal to expected.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(object expected, object actual, string message)
        { try { Assert.AreEqual(expected, actual, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects are equal and throws an exception if the
        //     two objects are not equal. Different numeric types are treated as unequal even
        //     if the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   expected:
        //     The first object to compare. This is the object the tests expects.
        //
        //   actual:
        //     The second object to compare. This is the object produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is not equal to expected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(object expected, object actual, string message, params object[] parameters)
        { try { Assert.AreEqual(expected, actual, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified floats are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first float to compare. This is the float the tests expects.
        //
        //   actual:
        //     The second float to compare. This is the float produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than expected by more than delta.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(float expected, float actual, float delta)
        { try { Assert.AreEqual(expected, actual, delta); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified floats are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first float to compare. This is the float the tests expects.
        //
        //   actual:
        //     The second float to compare. This is the float produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than expected by more than delta.
        //
        //   message:
        //     The message to include in the exception when actual is different than expected
        //     by more than delta. The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(float expected, float actual, float delta, string message)
        { try { Assert.AreEqual(expected, actual, delta, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified floats are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first float to compare. This is the float the tests expects.
        //
        //   actual:
        //     The second float to compare. This is the float produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than expected by more than delta.
        //
        //   message:
        //     The message to include in the exception when actual is different than expected
        //     by more than delta. The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(float expected, float actual, float delta, string message, params object[] parameters)
        { try { Assert.AreEqual(expected, actual, delta, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified doubles are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first double to compare. This is the double the tests expects.
        //
        //   actual:
        //     The second double to compare. This is the double produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than expected by more than delta.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(double expected, double actual, double delta)
        { try { Assert.AreEqual(expected, actual, delta); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified doubles are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first double to compare. This is the double the tests expects.
        //
        //   actual:
        //     The second double to compare. This is the double produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than expected by more than delta.
        //
        //   message:
        //     The message to include in the exception when actual is different than expected
        //     by more than delta. The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(double expected, double actual, double delta, string message)
        { try { Assert.AreEqual(expected, actual, delta, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are equal and throws an exception if they
        //     are not equal. The invariant culture is used for the comparison.
        //
        // Parameters:
        //   expected:
        //     The first string to compare. This is the string the tests expects.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(string expected, string actual, bool ignoreCase)
        { try { Assert.AreEqual(expected, actual, ignoreCase); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are equal and throws an exception if they
        //     are not equal. The invariant culture is used for the comparison.
        //
        // Parameters:
        //   expected:
        //     The first string to compare. This is the string the tests expects.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   message:
        //     The message to include in the exception when actual is not equal to expected.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(string expected, string actual, bool ignoreCase, string message)
        { try { Assert.AreEqual(expected, actual, ignoreCase, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are equal and throws an exception if they
        //     are not equal. The invariant culture is used for the comparison.
        //
        // Parameters:
        //   expected:
        //     The first string to compare. This is the string the tests expects.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   message:
        //     The message to include in the exception when actual is not equal to expected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(string expected, string actual, bool ignoreCase, string message, params object[] parameters)
        { try { Assert.AreEqual(expected, actual, ignoreCase, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first string to compare. This is the string the tests expects.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   culture:
        //     A CultureInfo object that supplies culture-specific comparison information.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(string expected, string actual, bool ignoreCase, CultureInfo culture)
        { try { Assert.AreEqual(expected, actual, ignoreCase, culture); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first string to compare. This is the string the tests expects.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   culture:
        //     A CultureInfo object that supplies culture-specific comparison information.
        //
        //   message:
        //     The message to include in the exception when actual is not equal to expected.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(string expected, string actual, bool ignoreCase, CultureInfo culture, string message)
        { try { Assert.AreEqual(expected, actual, ignoreCase, culture, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first string to compare. This is the string the tests expects.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   culture:
        //     A CultureInfo object that supplies culture-specific comparison information.
        //
        //   message:
        //     The message to include in the exception when actual is not equal to expected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(string expected, string actual, bool ignoreCase, CultureInfo culture, string message, params object[] parameters)
        { try { Assert.AreEqual(expected, actual, ignoreCase, culture, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified values are equal and throws an exception if the two
        //     values are not equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   expected:
        //     The first value to compare. This is the value the tests expects.
        //
        //   actual:
        //     The second value to compare. This is the value produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is not equal to expected.
        //     The message is shown in test results.
        //
        // Type parameters:
        //   T:
        //     The type of values to compare.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual<T>(T expected, T actual, string message)
        { try { Assert.AreEqual(expected, actual, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified values are equal and throws an exception if the two
        //     values are not equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   expected:
        //     The first value to compare. This is the value the tests expects.
        //
        //   actual:
        //     The second value to compare. This is the value produced by the code under test.
        //
        // Type parameters:
        //   T:
        //     The type of values to compare.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual<T>(T expected, T actual)
        { try { Assert.AreEqual(expected, actual); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified doubles are equal and throws an exception if they
        //     are not equal.
        //
        // Parameters:
        //   expected:
        //     The first double to compare. This is the double the tests expects.
        //
        //   actual:
        //     The second double to compare. This is the double produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than expected by more than delta.
        //
        //   message:
        //     The message to include in the exception when actual is different than expected
        //     by more than delta. The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected is not equal to actual.
        public static void AreEqual(double expected, double actual, double delta, string message, params object[] parameters)
        { try { Assert.AreEqual(expected, actual, delta, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are unequal and throws an exception if they
        //     are equal. The invariant culture is used for the comparison.
        //
        // Parameters:
        //   notExpected:
        //     The first string to compare. This is the string the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(string notExpected, string actual, bool ignoreCase)
        { try { Assert.AreNotEqual(notExpected, actual, ignoreCase); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified floats are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first float to compare. This is the float the test expects not to match actual.
        //
        //   actual:
        //     The second float to compare. This is the float produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than notExpected by at most delta.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected or
        //     different by less than delta. The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(float notExpected, float actual, float delta, string message, params object[] parameters)
        { try { Assert.AreNotEqual(notExpected, actual, delta, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified floats are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first float to compare. This is the float the test expects not to match actual.
        //
        //   actual:
        //     The second float to compare. This is the float produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than notExpected by at most delta.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected or
        //     different by less than delta. The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(float notExpected, float actual, float delta, string message)
        { try { Assert.AreNotEqual(notExpected, actual, delta, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified floats are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first float to compare. This is the float the test expects not to match actual.
        //
        //   actual:
        //     The second float to compare. This is the float produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than notExpected by at most delta.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(float notExpected, float actual, float delta)
        { try { Assert.AreNotEqual(notExpected, actual, delta); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified doubles are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first double to compare. This is the double the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second double to compare. This is the double produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than notExpected by at most delta.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected or
        //     different by less than delta. The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(double notExpected, double actual, double delta, string message, params object[] parameters)
        { try { Assert.AreNotEqual(notExpected, actual, delta, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are unequal and throws an exception if they
        //     are equal. The invariant culture is used for the comparison.
        //
        // Parameters:
        //   notExpected:
        //     The first string to compare. This is the string the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(string notExpected, string actual, bool ignoreCase, string message)
        { try { Assert.AreNotEqual(notExpected, actual, ignoreCase, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects are unequal and throws an exception if the
        //     two objects are equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   notExpected:
        //     The first object to compare. This is the value the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second object to compare. This is the object produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(object notExpected, object actual, string message, params object[] parameters)
        { try { Assert.AreNotEqual(notExpected, actual, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified doubles are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first double to compare. This is the double the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second double to compare. This is the double produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than notExpected by at most delta.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected or
        //     different by less than delta. The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(double notExpected, double actual, double delta, string message)
        { try { Assert.AreNotEqual(notExpected, actual, delta, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects are unequal and throws an exception if the
        //     two objects are equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   notExpected:
        //     The first object to compare. This is the value the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second object to compare. This is the object produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(object notExpected, object actual, string message)
        { try { Assert.AreNotEqual(notExpected, actual, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are unequal and throws an exception if they
        //     are equal. The invariant culture is used for the comparison.
        //
        // Parameters:
        //   notExpected:
        //     The first string to compare. This is the string the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(string notExpected, string actual, bool ignoreCase, string message, params object[] parameters)
        { try { Assert.AreNotEqual(notExpected, actual, ignoreCase, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first string to compare. This is the string the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   culture:
        //     A CultureInfo object that supplies culture-specific comparison information.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(string notExpected, string actual, bool ignoreCase, CultureInfo culture)
        { try { Assert.AreNotEqual(notExpected, actual, ignoreCase, culture); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first string to compare. This is the string the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   culture:
        //     A CultureInfo object that supplies culture-specific comparison information.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(string notExpected, string actual, bool ignoreCase, CultureInfo culture, string message)
        { try { Assert.AreNotEqual(notExpected, actual, ignoreCase, culture, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified values are unequal and throws an exception if the
        //     two values are equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   notExpected:
        //     The first value to compare. This is the value the test expects not to match actual.
        //
        //   actual:
        //     The second value to compare. This is the value produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Type parameters:
        //   T:
        //     The type of values to compare.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual<T>(T notExpected, T actual, string message, params object[] parameters)
        { try { Assert.AreNotEqual(notExpected, actual, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified values are unequal and throws an exception if the
        //     two values are equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   notExpected:
        //     The first value to compare. This is the value the test expects not to match actual.
        //
        //   actual:
        //     The second value to compare. This is the value produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected.
        //     The message is shown in test results.
        //
        // Type parameters:
        //   T:
        //     The type of values to compare.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual<T>(T notExpected, T actual, string message)
        { try { Assert.AreNotEqual(notExpected, actual, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified values are unequal and throws an exception if the
        //     two values are equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   notExpected:
        //     The first value to compare. This is the value the test expects not to match actual.
        //
        //   actual:
        //     The second value to compare. This is the value produced by the code under test.
        //
        // Type parameters:
        //   T:
        //     The type of values to compare.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual<T>(T notExpected, T actual)
        { try { Assert.AreNotEqual(notExpected, actual); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified strings are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first string to compare. This is the string the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second string to compare. This is the string produced by the code under test.
        //
        //   ignoreCase:
        //     A Boolean indicating a case-sensitive or insensitive comparison. (true indicates
        //     a case-insensitive comparison.)
        //
        //   culture:
        //     A CultureInfo object that supplies culture-specific comparison information.
        //
        //   message:
        //     The message to include in the exception when actual is equal to notExpected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(string notExpected, string actual, bool ignoreCase, CultureInfo culture, string message, params object[] parameters)
        { try { Assert.AreNotEqual(notExpected, actual, ignoreCase, culture, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects are unequal and throws an exception if the
        //     two objects are equal. Different numeric types are treated as unequal even if
        //     the logical values are equal. 42L is not equal to 42.
        //
        // Parameters:
        //   notExpected:
        //     The first object to compare. This is the value the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second object to compare. This is the object produced by the code under test.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(object notExpected, object actual)
        { try { Assert.AreNotEqual(notExpected, actual); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified doubles are unequal and throws an exception if they
        //     are equal.
        //
        // Parameters:
        //   notExpected:
        //     The first double to compare. This is the double the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second double to compare. This is the double produced by the code under test.
        //
        //   delta:
        //     The required accuracy. An exception will be thrown only if actual is different
        //     than notExpected by at most delta.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected is equal to actual.
        public static void AreNotEqual(double notExpected, double actual, double delta)
        { try { Assert.AreNotEqual(notExpected, actual, delta); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects refer to different objects and throws an
        //     exception if the two inputs refer to the same object.
        //
        // Parameters:
        //   notExpected:
        //     The first object to compare. This is the value the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second object to compare. This is the value produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is the same as notExpected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected refers to the same object as actual.
        public static void AreNotSame(object notExpected, object actual, string message, params object[] parameters)
        { try { Assert.AreNotSame(notExpected, actual, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects refer to different objects and throws an
        //     exception if the two inputs refer to the same object.
        //
        // Parameters:
        //   notExpected:
        //     The first object to compare. This is the value the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second object to compare. This is the value produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is the same as notExpected.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected refers to the same object as actual.
        public static void AreNotSame(object notExpected, object actual, string message)
        { try { Assert.AreNotSame(notExpected, actual, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects refer to different objects and throws an
        //     exception if the two inputs refer to the same object.
        //
        // Parameters:
        //   notExpected:
        //     The first object to compare. This is the value the test expects not to match
        //     actual.
        //
        //   actual:
        //     The second object to compare. This is the value produced by the code under test.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if notExpected refers to the same object as actual.
        public static void AreNotSame(object notExpected, object actual)
        { try { Assert.AreNotSame(notExpected, actual); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects both refer to the same object and throws
        //     an exception if the two inputs do not refer to the same object.
        //
        // Parameters:
        //   expected:
        //     The first object to compare. This is the value the test expects.
        //
        //   actual:
        //     The second object to compare. This is the value produced by the code under test.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected does not refer to the same object as actual.
        public static void AreSame(object expected, object actual)
        { try { Assert.AreSame(expected, actual); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects both refer to the same object and throws
        //     an exception if the two inputs do not refer to the same object.
        //
        // Parameters:
        //   expected:
        //     The first object to compare. This is the value the test expects.
        //
        //   actual:
        //     The second object to compare. This is the value produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is not the same as expected.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected does not refer to the same object as actual.
        public static void AreSame(object expected, object actual, string message, params object[] parameters)
        { try { Assert.AreSame(expected, actual, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified objects both refer to the same object and throws
        //     an exception if the two inputs do not refer to the same object.
        //
        // Parameters:
        //   expected:
        //     The first object to compare. This is the value the test expects.
        //
        //   actual:
        //     The second object to compare. This is the value produced by the code under test.
        //
        //   message:
        //     The message to include in the exception when actual is not the same as expected.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if expected does not refer to the same object as actual.
        public static void AreSame(object expected, object actual, string message)
        { try { Assert.AreSame(expected, actual, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Static equals overloads are used for comparing instances of two types for reference
        //     equality. This method should not be used for comparison of two instances for
        //     equality. This object will always throw with Assert.Fail. Please use Assert.AreEqual
        //     and associated overloads in your unit tests.
        //
        // Parameters:
        //   objA:
        //     Object A
        //
        //   objB:
        //     Object B
        //
        // Returns:
        //     False, always.
        public static bool Equals(object objA, object objB)
        { try { return Assert.Equals(objA, objB); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); return false; } }
        //
        // Summary:
        //     Throws an AssertFailedException.
        //
        // Parameters:
        //   message:
        //     The message to include in the exception. The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Always thrown.
        public static void Fail(string message, params object[] parameters)
        { try { Assert.Fail(message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Throws an AssertFailedException.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Always thrown.
        public static void Fail()
        { try { Assert.Fail(); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Throws an AssertFailedException.
        //
        // Parameters:
        //   message:
        //     The message to include in the exception. The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Always thrown.
        public static void Fail(string message)
        { try { Assert.Fail(message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }


        public static void Fail(Exception ex)
        {
            try
            {
                Assert.Fail(ex.Message);
                //LogHelpers.WriteException(ex);
            }
            catch (UnitTestAssertException e)
            {

                if (ex.Data != null)
                {
                    foreach (DictionaryEntry pair in ex.Data)
                    {
                        e.Data.Add(pair.Key, pair.Value);
                    }
                }
                StepErrors.Add(e); TotalStepErrorCount++;
            }
        }

        //
        // Summary:
        //     Throws an AssertInconclusiveException.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException:
        //     Always thrown.
        public static void Inconclusive()
        { try { Assert.Inconclusive(); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Throws an AssertInconclusiveException.
        //
        // Parameters:
        //   message:
        //     The message to include in the exception. The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException:
        //     Always thrown.
        public static void Inconclusive(string message, params object[] parameters)
        { try { Assert.Inconclusive(message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Throws an AssertInconclusiveException.
        //
        // Parameters:
        //   message:
        //     The message to include in the exception. The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException:
        //     Always thrown.
        public static void Inconclusive(string message)
        { try { Assert.Inconclusive(message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified condition is false and throws an exception if the
        //     condition is true.
        //
        // Parameters:
        //   condition:
        //     The condition the test expects to be false.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if condition is true.
        public static void IsFalse(bool condition)
        { try { Assert.IsFalse(condition); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified condition is false and throws an exception if the
        //     condition is true.
        //
        // Parameters:
        //   condition:
        //     The condition the test expects to be false.
        //
        //   message:
        //     The message to include in the exception when condition is true. The message is
        //     shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if condition is true.
        public static void IsFalse(bool condition, string message)
        { try { Assert.IsFalse(condition, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified condition is false and throws an exception if the
        //     condition is true.
        //
        // Parameters:
        //   condition:
        //     The condition the test expects to be false.
        //
        //   message:
        //     The message to include in the exception when condition is true. The message is
        //     shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if condition is true.
        public static void IsFalse(bool condition, string message, params object[] parameters)
        { try { Assert.IsFalse(condition, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is an instance of the expected type and throws
        //     an exception if the expected type is not in the inheritance hierarchy of the
        //     object.
        //
        // Parameters:
        //   value:
        //     The object the test expects to be of the specified type.
        //
        //   expectedType:
        //     The expected type of value.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is null or expectedType is not in the inheritance hierarchy of
        //     value.
        public static void IsInstanceOfType(object value, Type expectedType)
        { try { Assert.IsInstanceOfType(value, expectedType); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is an instance of the expected type and throws
        //     an exception if the expected type is not in the inheritance hierarchy of the
        //     object.
        //
        // Parameters:
        //   value:
        //     The object the test expects to be of the specified type.
        //
        //   expectedType:
        //     The expected type of value.
        //
        //   message:
        //     The message to include in the exception when value is not an instance of expectedType.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is null or expectedType is not in the inheritance hierarchy of
        //     value.
        public static void IsInstanceOfType(object value, Type expectedType, string message)
        { try { Assert.IsInstanceOfType(value, expectedType, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is an instance of the expected type and throws
        //     an exception if the expected type is not in the inheritance hierarchy of the
        //     object.
        //
        // Parameters:
        //   value:
        //     The object the test expects to be of the specified type.
        //
        //   expectedType:
        //     The expected type of value.
        //
        //   message:
        //     The message to include in the exception when value is not an instance of expectedType.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is null or expectedType is not in the inheritance hierarchy of
        //     value.
        public static void IsInstanceOfType(object value, Type expectedType, string message, params object[] parameters)
        { try { Assert.IsInstanceOfType(value, expectedType, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is not an instance of the wrong type and throws
        //     an exception if the specified type is in the inheritance hierarchy of the object.
        //
        // Parameters:
        //   value:
        //     The object the test expects not to be of the specified type.
        //
        //   wrongType:
        //     The type that value should not be.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is not null and wrongType is in the inheritance hierarchy of
        //     value.
        public static void IsNotInstanceOfType(object value, Type wrongType)
        { try { Assert.IsNotInstanceOfType(value, wrongType); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is not an instance of the wrong type and throws
        //     an exception if the specified type is in the inheritance hierarchy of the object.
        //
        // Parameters:
        //   value:
        //     The object the test expects not to be of the specified type.
        //
        //   wrongType:
        //     The type that value should not be.
        //
        //   message:
        //     The message to include in the exception when value is an instance of wrongType.
        //     The message is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is not null and wrongType is in the inheritance hierarchy of
        //     value.
        public static void IsNotInstanceOfType(object value, Type wrongType, string message, params object[] parameters)
        { try { Assert.IsNotInstanceOfType(value, wrongType, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is not an instance of the wrong type and throws
        //     an exception if the specified type is in the inheritance hierarchy of the object.
        //
        // Parameters:
        //   value:
        //     The object the test expects not to be of the specified type.
        //
        //   wrongType:
        //     The type that value should not be.
        //
        //   message:
        //     The message to include in the exception when value is an instance of wrongType.
        //     The message is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is not null and wrongType is in the inheritance hierarchy of
        //     value.
        public static void IsNotInstanceOfType(object value, Type wrongType, string message)
        { try { Assert.IsNotInstanceOfType(value, wrongType, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is non-null and throws an exception if it
        //     is null.
        //
        // Parameters:
        //   value:
        //     The object the test expects not to be null.
        //
        //   message:
        //     The message to include in the exception when value is null. The message is shown
        //     in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is null.
        public static void IsNotNull(object value, string message)
        { try { Assert.IsNotNull(value, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is non-null and throws an exception if it
        //     is null.
        //
        // Parameters:
        //   value:
        //     The object the test expects not to be null.
        //
        //   message:
        //     The message to include in the exception when value is null. The message is shown
        //     in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is null.
        public static void IsNotNull(object value, string message, params object[] parameters)
        { try { Assert.IsNotNull(value, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is non-null and throws an exception if it
        //     is null.
        //
        // Parameters:
        //   value:
        //     The object the test expects not to be null.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is null.
        public static void IsNotNull(object value)
        { try { Assert.IsNotNull(value); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is null and throws an exception if it is not.
        //
        // Parameters:
        //   value:
        //     The object the test expects to be null.
        //
        //   message:
        //     The message to include in the exception when value is not null. The message is
        //     shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is not null.
        public static void IsNull(object value, string message)
        { try { Assert.IsNull(value, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is null and throws an exception if it is not.
        //
        // Parameters:
        //   value:
        //     The object the test expects to be null.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is not null.
        public static void IsNull(object value)
        { try { Assert.IsNull(value); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified object is null and throws an exception if it is not.
        //
        // Parameters:
        //   value:
        //     The object the test expects to be null.
        //
        //   message:
        //     The message to include in the exception when value is not null. The message is
        //     shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if value is not null.
        public static void IsNull(object value, string message, params object[] parameters)
        { try { Assert.IsNull(value, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified condition is true and throws an exception if the
        //     condition is false.
        //
        // Parameters:
        //   condition:
        //     The condition the test expects to be true.
        //
        //   message:
        //     The message to include in the exception when condition is false. The message
        //     is shown in test results.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if condition is false.
        public static void IsTrue(bool condition, string message)
        { try { Assert.IsTrue(condition, message); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified condition is true and throws an exception if the
        //     condition is false.
        //
        // Parameters:
        //   condition:
        //     The condition the test expects to be true.
        //
        //   message:
        //     The message to include in the exception when condition is false. The message
        //     is shown in test results.
        //
        //   parameters:
        //     An array of parameters to use when formatting message.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if condition is false.
        public static void IsTrue(bool condition, string message, params object[] parameters)
        { try { Assert.IsTrue(condition, message, parameters); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Tests whether the specified condition is true and throws an exception if the
        //     condition is false.
        //
        // Parameters:
        //   condition:
        //     The condition the test expects to be true.
        //
        // Exceptions:
        //   T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException:
        //     Thrown if condition is false.
        public static void IsTrue(bool condition)
        { try { Assert.IsTrue(condition); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); } }
        //
        // Summary:
        //     Replaces null characters ('\0') with "\\0".
        //
        // Parameters:
        //   input:
        //     The string to search.
        //
        // Returns:
        //     The converted string with null characters replaced by "\\0".
        //
        // Remarks:
        //     This is only public and still present to preserve compatibility with the V1 framework.
        public static string ReplaceNullChars(string input)
        { try { return Assert.ReplaceNullChars(input); } catch (UnitTestAssertException utaEx) { AddStepErrors(utaEx); return input; } }





    }
}
