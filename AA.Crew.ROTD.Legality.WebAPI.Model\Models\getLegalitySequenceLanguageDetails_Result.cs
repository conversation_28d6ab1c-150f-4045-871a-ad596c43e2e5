using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalitySequenceLanguageDetails_Result
    {
        public long SequencePositionDetailsID { get; set; }
        public int LanguageID { get; set; }
        public Nullable<bool> IsExcluded { get; set; }
    }
}
