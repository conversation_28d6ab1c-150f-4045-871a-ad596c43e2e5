﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class ReservesCrewSequenceLegalityContractDetailsDTO
    {
        public long ReservesCrewSequenceLegalityContractDetailsID { get; set; }
        public Nullable<long> ContractSectionsID { get; set; }
        public Nullable<long> LegalityPhaseID { get; set; }
        public Nullable<long> ReservesCrewSequenceLegalityID { get; set; }
        public Nullable<int> FosRAP { get; set; }
        public Nullable<int> LanguageID { get; set; }
        public Nullable<bool> IsCurrentRAP { get; set; }
    }
}
