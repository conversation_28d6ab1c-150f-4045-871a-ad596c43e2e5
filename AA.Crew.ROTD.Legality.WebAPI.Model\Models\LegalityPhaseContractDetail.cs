using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityPhaseContractDetail
    {
        public long LegalityPhaseContractDetailsID { get; set; }
        public Nullable<long> LegalityPhaseID { get; set; }
        public Nullable<long> ContractSectionsID { get; set; }
        public Nullable<long> LegalitySubPhaseID { get; set; }
    
        public virtual ContractSection ContractSection { get; set; }
        public virtual LegalityPhas LegalityPhas { get; set; }
        public virtual LegalitySubPhas LegalitySubPhas { get; set; }
    }
}
