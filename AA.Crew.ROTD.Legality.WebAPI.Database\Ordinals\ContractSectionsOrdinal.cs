using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct ContractSectionsOrdinal
    {
        /* Oridinal variables */

        internal Int32 ContractSectionsID;
        internal Int32 ContractSection;
        internal Int32 DisplayName;
        internal Int32 ReportType;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.ContractSectionsID = sqlDataReader.GetOrdinal("ContractSectionsID");
            this.ContractSection = sqlDataReader.GetOrdinal("ContractSection");
            this.DisplayName = sqlDataReader.GetOrdinal("DisplayName");
            this.ReportType = sqlDataReader.GetOrdinal("ReportType");


            this.Initialized = true;
        }
    }
}
