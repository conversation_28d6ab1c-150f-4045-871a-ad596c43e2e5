<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="ExcelDataReader" Version="3.7.0-develop00371" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0-develop00371" />
    <PackageReference Include="ExtentReports" Version="4.1.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0-preview1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0-preview.6.23329.7" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.0-preview.23280.1" />
    <PackageReference Include="Microsoft.Rest.ClientRuntime" Version="3.0.3" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.0.4" />
    <PackageReference Include="MSTest.TestFramework" Version="3.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="3.0.2-dev-02044" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="7.0.0" />
    <PackageReference Include="SpecFlow" Version="4.0.31-beta" />
    <PackageReference Include="SpecFlow.MsTest" Version="4.0.31-beta" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0-preview.6.23329.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Business\AA.Crew.ROTD.Legality.WebAPI.Business.csproj" />
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Model\AA.Crew.ROTD.Legality.WebAPI.Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Config\html-config.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
