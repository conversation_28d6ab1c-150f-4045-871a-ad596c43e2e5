using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class StandByType
    {
        public StandByType()
        {
            this.Standbies = new HashSet<Standby>();
        }
    
        public int StandByTypeID { get; set; }
        public string StandByTypeName { get; set; }
        public int BaseID { get; set; }
        public bool IsActive { get; set; }
    
        public virtual Base Base { get; set; }
        public virtual ICollection<Standby> Standbies { get; set; }
    }
}
