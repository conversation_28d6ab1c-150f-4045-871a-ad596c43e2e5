using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;

namespace AA.Crew.ROTD.Legality.WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ResourceMonitorController : ControllerBase
    {
        private readonly IRuleOrchestrator _ruleOrchestrator;

        public ResourceMonitorController(IRuleOrchestrator ruleOrchestrator)
        {
            _ruleOrchestrator = ruleOrchestrator ?? throw new ArgumentNullException(nameof(ruleOrchestrator));
        }

        [HttpGet("memory-usage")]
        public IActionResult GetMemoryUsage()
        {
            var process = Process.GetCurrentProcess();
            var memoryInfo = new
            {
                WorkingSetMB = process.WorkingSet64 / (1024 * 1024),
                PrivateMemoryMB = process.PrivateMemorySize64 / (1024 * 1024),
                GCHeapMB = GC.GetTotalMemory(false) / (1024 * 1024)
            };
            return Ok(memoryInfo);
        }

        [HttpPost("force-gc")]
        public IActionResult ForceGarbageCollection()
        {
            var process = Process.GetCurrentProcess();
            long beforeWorkingSet = process.WorkingSet64;
            long beforePrivate = process.PrivateMemorySize64;
            long beforeGCHeap = GC.GetTotalMemory(false);

            _ruleOrchestrator.ForceGarbageCollection();

            process.Refresh();
            long afterWorkingSet = process.WorkingSet64;
            long afterPrivate = process.PrivateMemorySize64;
            long afterGCHeap = GC.GetTotalMemory(false);

            var result = new
            {
                Before = new
                {
                    WorkingSetMB = beforeWorkingSet / (1024 * 1024),
                    PrivateMemoryMB = beforePrivate / (1024 * 1024),
                    GCHeapMB = beforeGCHeap / (1024 * 1024)
                },
                After = new
                {
                    WorkingSetMB = afterWorkingSet / (1024 * 1024),
                    PrivateMemoryMB = afterPrivate / (1024 * 1024),
                    GCHeapMB = afterGCHeap / (1024 * 1024)
                },
                Message = "Garbage collection triggered."
            };

            return Ok(result);
        }
    }
}