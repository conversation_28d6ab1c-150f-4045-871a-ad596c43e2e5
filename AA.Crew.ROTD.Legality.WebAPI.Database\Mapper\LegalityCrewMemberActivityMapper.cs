using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityCrewMemberActivityMapper : MapperBase<List<LegalityCrewMemberActivityDTO>, List<Activity>>
    {
        public override List<Activity> Map(List<LegalityCrewMemberActivityDTO> legalityCrewMemberActivityDtoList)
        {
            try
            {
                return legalityCrewMemberActivityDtoList.Select(legalityCrewMemberActivityDto => new Activity
                {
                    ActivityID = legalityCrewMemberActivityDto.ReservesCrewMemberActivityID,
                    Employeenumber = legalityCrewMemberActivityDto.ReservesCrewMemberID,
                    ActivityTypeID = legalityCrewMemberActivityDto.ReservesCrewMemberActivityTypeID,
                    ActivityCode = legalityCrewMemberActivityDto.ActivityCode,
                    ActivityType = legalityCrewMemberActivityDto.ActivityType,
                    DurationInDays = legalityCrewMemberActivityDto.DurationInDays == null ? 0 : legalityCrewMemberActivityDto.DurationInDays.Value,
                    HomeBaseFAReducedRestEndTime = legalityCrewMemberActivityDto.HomeBaseFAReducedRestEndTime,
                    HomeBaseRestEndTime = legalityCrewMemberActivityDto.HomeBaseRestEndTime,
                    StartDate = legalityCrewMemberActivityDto.StartDateTime,
                    EndDate = legalityCrewMemberActivityDto.EndDateTime,
                    ReservesCrewMemberID = legalityCrewMemberActivityDto.ReservesCrewMemberID,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityCrewMemberActivityDTO> Map(List<Activity> element)
        {
            throw new NotImplementedException();
        }
    }
}
