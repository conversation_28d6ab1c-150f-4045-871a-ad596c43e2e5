using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct RapCodeOrdinal
    {
        /* Oridinal variables */

        internal Int32 RapCodeID;
        internal Int32 RapCode;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.RapCodeID = sqlDataReader.GetOrdinal("RapCodeID");
            this.RapCode = sqlDataReader.GetOrdinal("RapCode");


            this.Initialized = true;
        }
    }
}
