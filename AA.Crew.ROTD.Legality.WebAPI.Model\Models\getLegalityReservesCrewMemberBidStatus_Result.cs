using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityReservesCrewMemberBidStatus_Result
    {
        public long ReservesCrewMemberBidStatusID { get; set; }
        public Nullable<decimal> CalloutTime { get; set; }
        public Nullable<decimal> MaxCredit { get; set; }
        public Nullable<decimal> ActualPayProjection { get; set; }
        public Nullable<long> ReservesCrewMemberID { get; set; }
        public Nullable<long> CrewStatusID { get; set; }
        public Nullable<long> BidMonthIndicatorID { get; set; }
    }
}
