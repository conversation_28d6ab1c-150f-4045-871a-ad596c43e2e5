﻿using System;
using System.Text.Json.Serialization;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class BidStatus
    {
        /// <summary>
        /// Rolling contract month duration to determine each month data
        /// allowableValues = "MMMYYYY", example = "MAR2017"
        /// </summary>
        public string contractMonth { get; set; }

        /// <summary>
        /// start date and time of contract month
        /// </summary>
        [JsonConverter(typeof(JsonDateConverter))]
        public DateTime contractMonthStartDate { get; set; }

        /// <summary>
        /// end date and time of contract month
        /// </summary>
        [JsonConverter(typeof(JsonDateConverter))]
        public DateTime contractMonthEndDate { get; set; }

        /// <summary>
        /// Denotes Type of contract month
        /// example = "C,R,O"
        /// </summary>
        public string contractMonthType { get; set; }

        /// <summary>
        /// Denotes Current Division is either Domestic or International
        /// example = "D,I"
        /// </summary>
        public string currentDivision { get; set; }

        /// <summary>
        /// Denotes the current equipment group of an aircraft
        ///  example = "737,777"
        /// </summary>
        public string currentEquipment { get; set; }

        /// <summary>
        /// Denotes Current Position/Seat of CrewMember
        /// example = "FO"
        /// </summary>
        public string currentPosition { get; set; }

        /// <summary>
        /// Current Base returned as part of CrewMemberServices is always relative to which base crew belongs at the moment when CrewMemberServices are called by consumers
        /// example = "ORD"
        /// </summary>
        public string currentBase { get; set; }

        /// <summary>
        /// Denotes actual airport code
        ///  example = "ORD,DFW"
        /// </summary>
        public string actualAirportCode { get; set; }

        /// <summary>
        /// Denotes Next Division is either Domestic or International
        /// example = "D,I"
        /// </summary>
        public string nextDivision { get; set; }

        /// <summary>
        /// Denotes the Next equipment group of an aircraft
        /// example = "777,737"
        /// </summary>
        public string nextEquipment { get; set; }

        /// <summary>
        /// Denotes Next Position/Seat of CrewMember
        /// example = "CA,FO"
        /// 
        /// </summary>
        public string nextPosition { get; set; }

        /// <summary>
        /// Next Base returned as part of CrewMemberServices is always relative to the time at which CrewMemberService is called 
        /// and value will be the base to which crew gets transferred for a future contract month. 
        /// If there is no base transfer for future contract month, then next base will be empty.
        /// example = "DFW, ORD"
        /// </summary>
        public string nextBase { get; set; }

        /// <summary>
        /// Denotes category of CrewMember Position/Seat
        /// example = "FO,CA"
        /// </summary>
        public string seatCategory { get; set; }

        // @ApiModelProperty(notes = "", )
        /// <summary>
        /// FSM supervisor count 
        /// example = "0"
        /// </summary>
        public string fsmSupervisorNumber { get; set; }

        /// <summary>
        /// bool flag to Indicate whether Crew Member is checkAirman
        /// example = "true,false"
        /// </summary>
        public bool checkAirman { get; set; }

        /// <summary>
        /// checkAirmanStatus CurrentMonth information
        /// </summary>
        public CheckAirmanStatus checkAirmanCurrentMonthStatus { get; set; }

        /// <summary>
        /// checkAirmanStatus OtherMonth information
        /// </summary>
        public CheckAirmanStatus checkAirmanOtherMonthStatus { get; set; }

        // @ApiModelProperty(notes = "", )
        /// <summary>
        /// bool flag to Indicate whether Supervisor is inactive
        /// example = "true,false"
        /// </summary>
        public bool inactiveSupervisor { get; set; }

        /// <summary>
        /// bool flag to Indicate whether Crew Member is inactive 
        /// example = "true,false"
        /// </summary>
        public bool inactiveCrewMember { get; set; }

        /// <summary>
        /// Selection type of crew member
        ///  example = "RRSV,REGL"
        /// </summary>
        public string selectionType { get; set; }

        /// <summary>
        /// Actual pay projection 
        ///  example = "1453,2345"
        /// </summary>
        public int actualPayProjection { get; set; }

        /// <summary>
        /// Pay projection
        /// example = "1453,2345"
        /// </summary>
        public int payProjection { get; set; }

        /// <summary>
        /// Schedule pay projection
        /// example = "1453,2345"
        /// </summary>
        public int scheduledProjection { get; set; }

        /// <summary>
        /// Greater time to date
        /// </summary>
        public int greaterTimetoDate { get; set; }

        /// <summary>
        /// Base Guarantee
        /// </summary>
        public int faBaseGuarantee { get; set; }


        /// <summary>
        /// Max minutes which Reserve Crew wishes to work for this contract month
        /// </summary>
        public int callOutTime { get; set; }

        /// <summary>
        /// Monthly Max
        /// </summary>
        public int monthlyMax { get; set; }

        /// <summary>
        /// Net Value
        /// </summary>
        public int netValue { get; set; }

        // @ApiModelProperty(notes = "")
        /// <summary>
        /// TTS Monthly Max
        /// </summary>
        public int ttsMonthlyMax { get; set; }

        /// <summary>
        /// IP Max
        /// </summary>
        public int ipMax { get; set; }

        /// <summary>
        /// Seniority Number
        /// </summary>
        public int seniorityNumber { get; set; }

        /// <summary>
        /// bool flag to verify whether indicator is Waiver
        /// </summary>
        public bool waiverInd { get; set; }

        /// <summary>
        /// Voluntary count
        /// </summary>
        public int voluntaryCount { get; set; }

        /// <summary>
        /// In Voluntary count
        /// </summary>
        public int involuntaryCount { get; set; }

        /// <summary>
        /// Maximum number of hours
        /// </summary>
        public int? maxHours { get; set; }

        /// <summary>
        /// ETB net
        /// </summary>
        public int? etbNet { get; set; }

        /// <summary>
        /// Total ETB trip hours
        /// </summary>
        public int? totalETBTripHours { get; set; }

        /// <summary>
        /// Total ETB Pay and Credit
        /// </summary>
        public int? totalETBPNCAdj { get; set; }

        /// <summary>
        /// Total ETB
        /// </summary>
        public int? totalETB { get; set; }

        /// <summary>
        /// Actual Projection
        /// </summary>
        public int? projectionActuals { get; set; }

        /// <summary>
        /// MTD block pay
        /// </summary>
        public int? mtdBlockPay { get; set; }

        /// <summary>
        /// Scheduled block time
        /// </summary>
        public int? scheduledBlockTime { get; set; }

        /// <summary>
        /// MTD block time
        /// </summary>
        public int? mtdBlockTime { get; set; }

        /// <summary>
        /// Scheduled Pay
        /// </summary>
        public int? scheduledPay { get; set; }

        /// <summary>
        /// OT sum
        /// </summary>
        public int? otSum { get; set; }

        /// <summary>
        /// Total Pay and credit
        /// </summary>
        public int? totalPNC { get; set; }

        /// <summary>
        /// This property is applicable for Reserves.When a 3 day sequence is assigned to reserve, the asgSequenceClickCount is incremented by 3
        /// Reserve assigned Sequence Click Count
        /// </summary>
        public int asgSequenceClickCount { get; set; }

        /// <summary>
        /// This property is applicable for Reserves.When a standby shift is assigned to reserve, the asgStandByClickCount is incremented by 1
        /// Reserve assigned Stand By Click Count
        /// </summary>
        public int asgStandByClickCount { get; set; }

        /// <summary>
        /// Stand By Count 
        /// </summary>
        public int standbyCount { get; set; }

        /// <summary>
        /// Indicator corresponding to base expansion period for the given contract month.If true means base expansion period has ended otherwise not
        /// </summary>
        public bool baseInitialized { get; set; }

        /// <summary>
        /// SelectionCrewBase returned as part of CrewMemberServicess always represents the base to which crew belongs to a specific contract month
        /// </summary>
        public string selectionCrewBase { get; set; }

        /// <summary>
        /// ActualAirportSelectionCrewBase returned as part of CrewMemberServices always represents the base with respect to selectionCrewBase and it's respective contractMonth
        /// </summary>
        public string actualAirportSelectionCrewBase { get; set; }

        /// <summary>
        /// Indicator to show SickStatus S=Sick, I=Sick if Needed.
        /// example = "S"
        /// </summary>
        public string sickStatus { get; set; }

        /// <summary>
        /// CrewMembers Equipment for the contractual month
        /// example = "777"
        /// </summary>
        public string fleetCode { get; set; }

        /// <summary>
        /// CrewMembers Division for the contractual month
        /// example = "D"
        /// </summary>
        public string selectionDivision { get; set; }

        /// <summary>
        /// Pilot Monthly Reserve Type  (‘S’ = Pilot Short Call Reserve \n ‘L’ = Pilot Long Call Reserve)
        /// example = "S"
        /// </summary>
        public string reserveType { get; set; }

        /// <summary>
        /// bool indicator represents if this CrewMember is SAN Inclusive Reserve or not.
        /// </summary>
        public bool sanInclusiveReserve { get; set; }

        /// <summary>
        /// bool indicator represents if this CrewMember is SAN Reserve Volunteer or not.
        /// </summary>
        public bool sanReserveVolunteer { get; set; }

        public string dailyCreditActivity { get; set; }

    }
}
