﻿using AA.Crew.ROMS.ROTA.WebApi.Client;
using AA.Crew.ROMS.ROTA.WebApi.Model.Requests;
using AA.Crew.ROMS.ROTA.WebApi.Model.Responses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Service
{
    public class RotaWebApiProvider : IRotaWebApiProvider
    {
        private IServiceCallFactory _svcfactory = null;
        //private List<Model.Models.AppSettings> _appSettings;



        private const string ThreeDigitHourTime = "{0,2:D2}:{1,2:D2}";
        /// <summary>
        /// Data Servive Provider Constructor to initiate the logging object and data provider objects.
        /// </summary>
        /// <param name="_svcfactory">Unity _svcfactory as _svcfactory</param>
        public RotaWebApiProvider(IServiceCallFactory svcfactory)//, List<Model.Models.AppSettings> appSettings)
        {
            _svcfactory = svcfactory;
            //_appSettings = appSettings;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name = "" > </ param >
        public async Task<GetOperatingLanguageResponse> GetOperatingLanguages(GetOperatingLanguageRequest request)
        {
            try
            {
                //OperatingLanguageMapper responseMapper = new OperatingLanguageMapper();
                var client = this._svcfactory.RotaWebAPIService();
                var response = await client.GetOperatingLanguage(request);
                //var transResponse = responseMapper.Map(response);
                return response;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
