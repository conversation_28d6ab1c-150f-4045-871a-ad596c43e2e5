using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    //public class LegalityCrewSequenceMapper : MapperBase<List<LegalityCrewSequenceDTO>, List<LegalityCrewSequence>>
    //{
    //    public override List<LegalityCrewSequence> Map(List<LegalityCrewSequenceDTO> legalityCrewSequenceDtoList)
    //    {
    //        try
    //        {
    //            return legalityCrewSequenceDtoList.Select(legalityCrewSequenceDto => new LegalityCrewSequence
    //            {
    //                ReservesCrewSequenceLegalityID = legalityCrewSequenceDto.ReservesCrewSequenceLegalityID,
    //                ReservesCrewMemberID = legalityCrewSequenceDto.ReservesCrewMemberID,
    //                RunID = legalityCrewSequenceDto.RunID,
    //                SequencePositionDetailsID = legalityCrewSequenceDto.SequencePositionDetailsID,
    //                StandByID = legalityCrewSequenceDto.StandByID,
    //                IsOver35By7 = legalityCrewSequenceDto.IsOver35By7,
    //                SequenceID = legalityCrewSequenceDto.SequenceID,
    //                SequenceNumber = legalityCrewSequenceDto.SequenceNumber,
    //                SequencePosition = legalityCrewSequenceDto.SequencePosition,
    //                EmployeeID = legalityCrewSequenceDto.EmployeeID,
    //                ShiftStartTime = legalityCrewSequenceDto.ShiftStartTime,
    //                ShiftDurationHrs = legalityCrewSequenceDto.ShiftDurationHrs,


    //            }).ToList();
    //        }
    //        catch (Exception)
    //        {
    //            throw;
    //        }
    //    }

    //    public override List<LegalityCrewSequenceDTO> Map(List<LegalityCrewSequence> element)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
}
