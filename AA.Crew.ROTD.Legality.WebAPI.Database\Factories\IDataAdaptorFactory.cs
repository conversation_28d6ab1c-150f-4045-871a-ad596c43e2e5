﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Factories
{
    public interface IDataAdaptorFactory
    {
        string ConnectionString();
        IAppSettingsDataAdaptor GetAppSettingsDataAdaptor();
        IInterpretiveDataAdaptor GetInterpretiveDataAdaptor();
        ILegalityInterpretiveDataAdaptor GetLegalityInterpretiveDataAdaptor();
    }
}
