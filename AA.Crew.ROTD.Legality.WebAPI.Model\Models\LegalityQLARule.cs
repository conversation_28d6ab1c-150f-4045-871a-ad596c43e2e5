using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityQLARule
    {
        public LegalityQLARule()
        {
            this.LegalityQLASupportingDatas = new HashSet<LegalityQLASupportingData>();
        }
    
        public long LegalityQLARulesID { get; set; }
        public Nullable<long> LegalityQLADetailsID { get; set; }
        public Nullable<long> QLARuleID { get; set; }
        public string Result { get; set; }
        public string Message { get; set; }
        public string QLARuleName { get; set; }
        public Int32 SequenceID { get; set; }

        public virtual QLARule QLARule { get; set; }
        public virtual LegalityQLADetail LegalityQLADetail { get; set; }
        public virtual ICollection<LegalityQLASupportingData> LegalityQLASupportingDatas { get; set; }
    }
}
