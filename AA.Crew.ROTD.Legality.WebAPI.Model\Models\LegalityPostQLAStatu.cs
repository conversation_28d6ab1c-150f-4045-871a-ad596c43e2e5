using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityPostQLAStatu
    {
        public long LegalityPostQLAStatusID { get; set; }
        public Nullable<long> LegalityQLADetailsID { get; set; }
        public Nullable<long> PostQLAStateID { get; set; }
        public Nullable<long> ContractSectionsID { get; set; }
        public Nullable<long> LegalityPhaseID { get; set; }
        public Nullable<long> RuleID { get; set; }
        public string RuleName { get; set; }
    
        public virtual ContractSection ContractSection { get; set; }
        public virtual LegalityPhas LegalityPhas { get; set; }
        public virtual PostQLAState PostQLAState { get; set; }
        public virtual LegalityQLADetail LegalityQLADetail { get; set; }
    }
}
