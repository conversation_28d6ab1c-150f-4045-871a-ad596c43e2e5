using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityProcessAggressiveBidCrewWaiverMapper : MapperBase<List<LegalityProcessAggressiveBidCrewWaiverDTO>, List<AggressiveBidCrewWaiver>>
    {
        public override List<AggressiveBidCrewWaiver> Map(List<LegalityProcessAggressiveBidCrewWaiverDTO> legalityProcessAggressiveBidCrewWaiverDtoList)
        {
            try
            {
                return legalityProcessAggressiveBidCrewWaiverDtoList.Select(legalityProcessAggressiveBidCrewWaiverDto => new AggressiveBidCrewWaiver
                {
                    EmployeeID = legalityProcessAggressiveBidCrewWaiverDto.EmployeeID,
                    FAName = legalityProcessAggressiveBidCrewWaiverDto.FAName,
                    BidTypeId = legalityProcessAggressiveBidCrewWaiverDto.BidtypeID,
                    BidTypeName = legalityProcessAggressiveBidCrewWaiverDto.BidTypeName,
                    BidCategoryID = legalityProcessAggressiveBidCrewWaiverDto.BidCategoryID,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityProcessAggressiveBidCrewWaiverDTO> Map(List<AggressiveBidCrewWaiver> element)
        {
            throw new NotImplementedException();
        }
    }
}
