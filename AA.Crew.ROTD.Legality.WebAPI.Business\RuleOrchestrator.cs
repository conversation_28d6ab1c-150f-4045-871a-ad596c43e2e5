﻿using AA.Crew.Legalities.ROTD;
using AA.Crew.Legalities.ROTD.Data.Interfaces;
using AA.Crew.Reserves.QLA.Model.Request.Legalities;
using AA.Crew.Reserves.TimeConversion;
using AA.Crew.ROMS.LogHelper.Helper;
using AA.Crew.ROMS.ROTA.WebApi.Model.Requests;
using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Business.Operations;
using AA.Crew.ROTD.Legality.WebAPI.Database.Factories;
using AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.DBEntities;
using AA.Crew.ROTD.Legality.WebAPI.Model.Exceptions;
using AA.Crew.ROTD.Legality.WebAPI.Service;
using AA.Crew.ROTD.Legality.WebAPI.Service.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text;
using BaseDate = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.BaseDate;
using ContractMonth = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.ContractMonth;
using Dropdutylist = AA.Crew.Reserves.QLA.Model.Request.Legalities.Dropdutylist;
using LegalitiesQLARequest = AA.Crew.Reserves.QLA.Model.Request.LegalitiesQLARequest;
using LegalitiesQLAResponse = AA.Crew.Reserves.QLA.Model.Response.LegalitiesQLAResponse;
using LegalityQLASupportingData = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData;
using Pickupdutylist = AA.Crew.Reserves.QLA.Model.Request.Legalities.Pickupdutylist;
using QLARequest = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.QLARequest;
using Sequence = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence;


namespace AA.Crew.ROTD.Legality.WebAPI.Business
{
    public class RuleOrchestrator : IRuleOrchestrator
    {
        #region [Private Declaration(s)]
        private bool disposed = false;
        //private ROTDProcessor contextualProcess = null;
        private readonly List<Task> TaskList = new List<Task>();
        private readonly List<Task> messageTaskList = new List<Task>();
        //private IROTDProcessor rotdProcessor;
        private ILogger<RuleOrchestrator> _loggingProvider;
        private List<PostQLAMappingDetails> mappedDetails;
        private ROTDPhase phase;
        //private ROTDInterpretiveRepository _ROTDInterpretiveRepository = null;
        private ROTDContextualRuleSet legalityRule;
        //private List<IROTDRules> baseRule;
        //private ROTDLoader loader;
        private List<Model.BusinessObject.Sequence> lstSequence = null;
        private List<FlightAttendant> lstFlightAttendant = null;
        private List<StandBy> lstStandBy = null;
        private List<Activity> lstFaActivity = null;
        private List<BidCrewWaiver> lstBidCrewWaiver = null;
        private List<Bid> bids = null;
        private List<QLAResponse> lstQLAResponse = null;
        private List<Model.BusinessObject.QLARequest> lstQLARequest = null;
        private List<QLARuleResult> lstQLARuleResult = null;
        private List<RAPShifts> lstRAPShift = null;
        private List<RAPShifts> lstPreviousMonthRAPShift = null;
        //private QLA.IServiceCallFactory legalityServiceFactory;
        private int runId = 0;
        //private IROTDInterpretiveRepository rotdInterpretiveRepository;
        //private IROTDLoader rotdLoader;
        private LegalitiesQLAResponse QLAResponse = null;
        private AA.Crew.Reserves.QLA.Model.Request.Legalities.Employee empDetails = null;
        private List<Request> request = null;
        private List<Pickupdutylist> pickupdutylist = null;
        private List<Dropdutylist> dropdutylist = null;
        //  private ILegalitiesService objLegalitiesService = null;
        private List<LegalitiesQLARequest> failedLegalitiesRequests = null;
        private LegalitiesQLAResponse objLegalitiesQLAResponse = null;
        private LegalitiesQLARequest objLegalitiesQLARequest = null;
        private LegalitiesQLAResponse phaseQLAResponse = null;
        private LegalitiesQLARequest phaseQLARequest = null;
        private LegalitiesQLAResponse futurePhaseQLAResponse = null;
        private LegalitiesQLARequest futurePhaseQLARequest = null;
        private LegalitiesQLAResponse etbPhaseQLAResponse = null;
        private LegalitiesQLARequest etbPhaseQLARequest = null;
        private LegalitiesQLAResponse etbFuturePhaseQLAResponse = null;
        private LegalitiesQLARequest etbFuturePhaseQLARequest = null;
        private LegalitiesQLAResponse lineHolderPhaseQLAResponse = null;
        private LegalitiesQLARequest lineHolderPhaseQLARequest = null;
        private List<QLAResponse> nonVolunteerPhaseQLAResponse = null;
        private List<QLARuleResult> nonVolunteerQLARuleResult = null;
        private List<AggressiveBidCrewWaiver> lstAggressiveBidCrewWaiver = null;
        private LegalitiesQLARequest legalitiesQlaRequest = null;
        private List<LegalityQLASupportingData> futureQlaSupportingData = null;
        private List<LegalityQLASupportingData> etbQlaSupportingData = null;
        private List<LegalityQLASupportingData> etbFutureQlaSupportingData = null;
        private List<LegalityQLASupportingData> rapQlaSupportingData = null;
        private List<LegalityQLASupportingData> lineHolderQlaSupportingData = null;
        private List<LegalityQLASupportingData> lineHolderStandByQlaSupportingData = null;
        private List<LegalityQLASupportingData> lineHolderFutureQlaSupportingData = null;
        private List<QLAErrors> QLAErrors = null;
        private IMappedRules mappedROTDRules;
        private List<ContractMonth> contract = null;
        private string conMonthYear;
        private const int qlaDefaultValue = 1000;
        private const int qlaBatchDefaultOneFA = 1;
        private const int qlaBatchDefaultMaxFA = 20;
        private const int qlaAsyncBatchDefaultValue = 30;
        private readonly object lockObject = new object();
        private readonly object lockObjectLog = new object();
        private DateTime currentContractMonthEndDate;
        private DateTime currentContractMonthStartDate;
        private string nextContractMonth = null;
        private bool IsSickProcess = false;
        private Dictionary<string, string> qlaRequesResponseDictionary = null;
        private StringBuilder qlaRequestTimeLogString = new StringBuilder();
        private bool isBaseCoTerminal = false;
        private ITimeConversion _timeConversion;
        public LegalitiesQLARequest LegalitiesQLARequest
        {

            get
            {
                return legalitiesQlaRequest;
            }
            set
            {
                legalitiesQlaRequest = value;
            }
        }
        private List<QLAResponse> legalityQlaResponses = null;
        private List<QLARequest> qlaRequest = null;
        private List<QLARuleResult> legalityRuleResult = null;

        public List<QLARequest> QLARequest
        {

            get
            {
                return qlaRequest;
            }
            set
            {
                qlaRequest = value;
            }
        }
        public List<QLAResponse> LegalityQLAResponse
        {

            get
            {
                return legalityQlaResponses;
            }
            set
            {
                legalityQlaResponses = value;
            }
        }
        public List<QLARuleResult> LegalityRuleResult
        {

            get
            {
                return legalityRuleResult;
            }
            set
            {
                legalityRuleResult = value;
            }

        }

        private ROTDContextualRuleSet _legalityRules;

        public ROTDContextualRuleSet LegalityRules
        {
            get { return _legalityRules; }
            set { _legalityRules = value; }
        }

        int iPhaseID = 0;

        private List<LegalityErrors> legalityErrorsException;

        public List<LegalityErrors> LegalityErrorsException
        {
            get { return legalityErrorsException; }
            set { legalityErrorsException = value; }
        }

        public bool IsBaseCoTerminal
        {
            get { return isBaseCoTerminal; }
            set { isBaseCoTerminal = value; }
        }

        IDataProviderFactory _dataProviderFactory;
        IAppSettingsDataProvider _applicationSettingsProvider;
        ILegalityInterpretiveDataProvider _interpretiveDataProvider;
        ICrewDataServiceProvider _crewDataServiceProvider;
        ILegalityServiceProvider _legalityServiceProvider;
        IRotaWebApiProvider _rotaWebApiProvider;
        IROTDProcessor _rotdProcessor;
        List<AppSettings> _appSettings;
        
        #endregion

        #region [Constructor(s)]

        public RuleOrchestrator(ILogger<RuleOrchestrator> logger, List<AppSettings> appSettings, IDataProviderFactory dataProviderFactory, ICrewDataServiceProvider crewDataServiceProvider, ILegalityServiceProvider legalityServiceProvider, IROTDProcessor rotdProcessor, ITimeConversion timeConversion, IRotaWebApiProvider rotaWebApiProvider)
        {
            _loggingProvider = logger ?? throw new ArgumentNullException(nameof(logger));
            LegalityErrorsException = new List<LegalityErrors>();
            _dataProviderFactory = dataProviderFactory;
            _appSettings = appSettings;
            _applicationSettingsProvider = _dataProviderFactory.GetAppSettingsDataProvider();
            _interpretiveDataProvider = _dataProviderFactory.GetLegalityInterpretiveDataProvider();
            _crewDataServiceProvider = crewDataServiceProvider;
            _legalityServiceProvider = legalityServiceProvider;
            _rotaWebApiProvider = rotaWebApiProvider;
            _rotdProcessor = rotdProcessor;
            _timeConversion = timeConversion;
        }

        #endregion

        

        #region [Method(s)]        

        public async Task<AppSettings> GetApplicationSetting(string name)
        {
            return _applicationSettingsProvider.GetApplicationSetting(name).Result;
        }

        public bool ROTDLegalityLoadData(Int32 runId, int runContextId, long awardProgressStepID)
        {
            string logTitle = "RuleOrchestrator.ROTDLegalityLoadData()";
            string BaseCD = null;
            DateTime ProcessingDate = DateTime.MinValue;

            try
            {
                _loggingProvider.LogInformation(logTitle + "ROTD Process Starting. Run Id :{0} " , runId);

                int phaseId = (int)LegalityPhase.All;
                if (phaseId > 0)
                {
                    var result = _interpretiveDataProvider.GetBaseProcessingDate(runId).Result;
                    if (result != null)
                    {
                        BaseCD = result.BaseCD;
                        ProcessingDate = result.ProcessingDate;
                        this.runId = runId;
                        var currentTime = _timeConversion.ConvertUTCToStation(BaseCD, DateTime.UtcNow);
                        //_interpretiveDataProvider.UpdateAwardProgressAsInProgress(result.BaseCD, result.ProcessingDate, awardProgressStepID, currentTime.StationLocalTIme); 
                   
                        _loggingProvider.LogInformation($"{logTitle} ROTD Process LoadData() - Start. Run Id : {runId}, currentTime: {currentTime.ToString()} ");
                        LoadData(runId, phaseId, BaseCD, ProcessingDate, true, runContextId);
                        _loggingProvider.LogInformation($"{logTitle} ROTD Process LoadData() - End. Run Id : {runId}. LoadData() - Completed ");
                        
                        currentTime = _timeConversion.ConvertUTCToStation(BaseCD, DateTime.UtcNow);
                        _loggingProvider.LogInformation($"{logTitle} ROTD Process Starting. Updating Legality Step Completion - Run Id : {runId}, currentTime: {currentTime.ToString()}");
                        _interpretiveDataProvider.UpdateAwardProgressAsComplete(result.BaseCD, result.ProcessingDate, awardProgressStepID, currentTime.StationLocalTIme);
                        _loggingProvider.LogInformation($"{logTitle} ROTD Process Starting. Run Id : {runId}. Update Award Progress as complee - Completed ");
                        //LoadData(runId, phaseId, runContextId);
                        
                        // Add garbage collection after processing is complete
                        ForceGarbageCollection();
                    }
                    else
                    {
                        throw new NoValidDataException("No Valid Data for the Run ID : " + runId);
                    }
                }

                _loggingProvider.LogInformation(logTitle+ "ROTD Process Ended. Run Id : {0} " , runId);

                return true;
            }
            catch (NoValidDataException ex)
            {
                _loggingProvider.LogError(logTitle + "No Valid Data Exception ROTD. " + ex.GetAllException());
                var currentTime = _timeConversion.ConvertUTCToStation(BaseCD, DateTime.UtcNow);
                _interpretiveDataProvider.UpdateAwardProgressAsComplete(BaseCD, ProcessingDate, awardProgressStepID, currentTime.StationLocalTIme);
                
                //Add garbage collection after exception handling
                ForceGarbageCollection();
                return true;
            }
            catch (Exception ex)
            {
                _loggingProvider.LogError(logTitle, "Error occured while Load Data from DB for ROTD. " + ex.GetAllException());
                //Task.Run(async ()=> await _interpretiveDataProvider.UpdateAwardProgressAsError(BaseCD, ProcessingDate, awardProgressStepID)).Forget();
                _interpretiveDataProvider.UpdateAwardProgressAsError(BaseCD, ProcessingDate, awardProgressStepID);
                
                // Add garbage collection after exception handling
                ForceGarbageCollection();
                throw new Exception("Error occurred while Load Data from DB for ROTD. " + ex.GetAllException());
            }
        }

        private void LoadData(int runID, int subPhase, string baseCD, DateTime processingDate, bool isTheProcessInvokedFromDbUi, int runContextId)
        {
            string logTitle = "DataLoader.LoadData()";
            ROTDPhase phase = new ROTDPhase();
            try
            {
                var Result = _interpretiveDataProvider.GetBaseProcessingDate(runID).Result;
                if (Result == null)
                {
                    throw new NoValidDataException("No Valid Data for the Run ID : " + runID);
                }

                baseCD = Result.BaseCD;
                processingDate = Result.ProcessingDate;
                //this.runId = runId;
                long qlaDataPhaseId = 0;
                List<AA.Crew.ROMS.ROTA.WebApi.Model.Models.OperatingLanguage> operatingLanguages = new List<AA.Crew.ROMS.ROTA.WebApi.Model.Models.OperatingLanguage>();
                operatingLanguages = _rotaWebApiProvider.GetOperatingLanguages(new GetOperatingLanguageRequest { apiConsumerApp = new AA.Crew.ROMS.ROTA.WebApi.Model.Requests.ApiConsumerApp { appName = "Legality.WebAPI", runID = runID, baseCode = baseCD, processingDate = processingDate.ToString() }, RunId = runID, BaseName = baseCD, ProcessingDate = processingDate }).Result.opLanguages;

                lstFaActivity = _interpretiveDataProvider.GetLegalityCrewMemberActivity(runID).Result;
                lstFlightAttendant = _interpretiveDataProvider.GetReservesCrewMemberDetails(lstFaActivity, runID).Result;

                _loggingProvider.LogInformation($"{logTitle} Completed Fetching Run Info. Run Id: {runId}, Languages Count:{operatingLanguages.Count}, FAActivity Count:{lstFaActivity.Count}, " +
                    $" FlightAttendants Count:{lstFlightAttendant.Count} ");
                if (lstFlightAttendant != null && lstFlightAttendant.Count > 0)
                {
                    foreach (FlightAttendant fa in lstFlightAttendant)
                    {
                        if (operatingLanguages != null)
                        {
                            var languages = operatingLanguages.FindAll(x => x.RapDaysID == fa.AvailableDays).Select(l => l.LanguageID).ToList();
                            foreach (FALanguageDetails lang in fa.fALanguageDetails)
                            {
                                if (languages != null && languages.Count > 0 && languages.Contains(lang.FALang.LanguageID))
                                {
                                    fa.hasOperationalLanguage = true;
                                }
                            }
                        }
                    }
                }

                var isSick = GetApplicationSetting("SickProcess").Result.SettingValues;
                if (!string.IsNullOrEmpty(isSick))
                    IsSickProcess = Convert.ToBoolean(isSick);
                lstFlightAttendant = lstFlightAttendant.Where(a => a.IsSick == false || IsSickProcess).ToList();

                if (lstFlightAttendant == null || lstFlightAttendant.Count == 0)
                {
                    _loggingProvider.LogError($"{logTitle} - No flight attedants found");
                    throw new NoValidDataException("No Valid Flight Attendant");
                }

                lstSequence = _interpretiveDataProvider.GetSequenceDetails(runID, baseCD).Result;
                lstStandBy = _interpretiveDataProvider.GetLegalityStandBy(runID, processingDate).Result;

                if ((lstSequence == null || lstSequence.Count == 0) && (lstStandBy == null || lstStandBy.Count == 0))
                {
                    _loggingProvider.LogError($"{logTitle} - No Sequences and Standby found");
                    throw new NoValidDataException("No Valid Sequence and Standby");
                }

                _loggingProvider.LogInformation($"{logTitle} - Flight Attendant Count:{lstFlightAttendant?.Count}, Sequence Count:{lstSequence?.Count}, Standby Count:{lstStandBy?.Count} ");

                lstBidCrewWaiver = _interpretiveDataProvider.GetLegalityProcessBidCrewWaiver(runID).Result;
                // Retrive Bids ( Following naming is misleading, actually below SP call returns the Bids.
                lstAggressiveBidCrewWaiver = _interpretiveDataProvider.GetLegalityProcessAggressiveBidCrewWaiver(runID).Result;

                _loggingProvider.LogInformation($"{logTitle} - BidCrewWaiver Count:{lstBidCrewWaiver?.Count}, AggressiveBidCrewWaiver Count:{lstAggressiveBidCrewWaiver?.Count} ");

                //filter the BidType
                lstBidCrewWaiver = (from bidCW in lstBidCrewWaiver
                                    join bidACW in lstAggressiveBidCrewWaiver on bidCW.BidTypeId equals bidACW.BidTypeId
                                    where bidCW.CrewMemberId == bidACW.EmployeeID
                                    select new BidCrewWaiver
                                    {
                                        BidCrewWaiverId = bidCW.BidCrewWaiverId,
                                        BidTypeId = bidCW.BidTypeId,
                                        CrewMemberId = bidCW.CrewMemberId,
                                        IsActive = bidCW.IsActive,
                                        StartDate = bidCW.StartDate,
                                        WaiverTypeDescription = bidCW.WaiverTypeDescription,
                                        WaiverTypeID = bidCW.WaiverTypeID,
                                        BidCategoryID = bidACW.BidCategoryID,
                                        WaiverSupportingData = bidCW.WaiverSupportingData
                                    }).ToList();


                var contractMonth = _crewDataServiceProvider.GetContractMonthForGivenDate(processingDate).Result;


                _loggingProvider.LogInformation($"{logTitle} - GetContractMonthForGivenDate:{LogSanitizer.SerializeSafely(contractMonth)} ");

                //come bck and proceed after apigee setup done
                conMonthYear = contractMonth.Month;
                var isBaseCoTerminal = _interpretiveDataProvider.GetCoTerminalBases().Result.Any(x => x.BaseCD == baseCD);
                var IsBaseCoTerminal = isBaseCoTerminal;

                _loggingProvider.LogInformation($"{logTitle} - GetCoTerminalBases:{isBaseCoTerminal.ToString()} ");


                lstRAPShift = _crewDataServiceProvider.GetRAPShift(baseCD, conMonthYear).Result;

                _loggingProvider.LogInformation($"{logTitle} - GetRAPShift:{LogSanitizer.SerializeSafely(lstRAPShift)} ");

                currentContractMonthEndDate = contractMonth.EndDate;
                nextContractMonth = _crewDataServiceProvider.GetContractMonthForGivenDate(currentContractMonthEndDate.AddDays(1)).Result.Month;

                lstPreviousMonthRAPShift = new List<RAPShifts>();
                currentContractMonthStartDate = contractMonth.StartDate;
                if (currentContractMonthStartDate.Date == processingDate.Date)
                {
                    _loggingProvider.LogInformation($"{logTitle} - Contract Month Start date is same as Processing Date ");
                    string previousContractMonth = _crewDataServiceProvider.GetContractMonthForGivenDate(currentContractMonthStartDate.AddDays(-1)).Result.Month;
                    lstPreviousMonthRAPShift = _crewDataServiceProvider.GetRAPShift(baseCD, previousContractMonth).Result;
                }


                //if (runId == 0)
                //{
                //    runId = rotdLoader.getRunId(baseCD, processingDate);
                //}

                BaseDate objBaseDate = new BaseDate() { BaseName = baseCD, ProcessingDate = processingDate };

                var CurrentSysTime = new TimeConversion().ConvertUTCToStation(baseCD, DateTime.UtcNow);

                objBaseDate.CurrentSysTime = CurrentSysTime.StationLocalTIme;

                mappedDetails = _interpretiveDataProvider.GetLegalityPostQLAMapping(runId).Result;

                _loggingProvider.LogInformation($"{logTitle} - GetLegalityPostQLAMapping(). Count: {mappedDetails?.Count}");

                // ROTD Phase - Non-Volunteer legality validation
                if (lstFlightAttendant.Any(f => f.IsVolunteer == false))
                {
                    _loggingProvider.LogInformation($"{logTitle} - Non-Volunteer legality validation - Flight Attendant has volunteers");

                    var validNonVolunteerPhaseFa = lstFlightAttendant.Where(f => f.IsVolunteer == false).ToList();
                    // Initial CCS call for all FA and Seq
                    if (lstSequence.Count > 0)
                    {
                        QLAResponse = GetCrewSequenceAndExecuteCcs(validNonVolunteerPhaseFa, lstSequence, new List<StandBy>(), baseCD, processingDate, runId, (int)ROTDPhases.NonVolunteer);
                        phase = ExecuteContextualInterpretive(ROTDLegalityPhase.NonVolunteer, QLAResponse, validNonVolunteerPhaseFa, lstSequence, new List<StandBy>(), isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                    }
                    // Initial CCS call for all FA and Stb
                    if (lstStandBy.Count > 0)
                    {
                        QLAResponse = GetCrewSequenceAndExecuteCcs(validNonVolunteerPhaseFa, new List<Sequence>(), lstStandBy, baseCD, processingDate, runId, (int)ROTDLegalityPhase.NonVolunteerStandBy);
                        phase = ExecuteContextualInterpretive(ROTDLegalityPhase.NonVolunteerStandBy, QLAResponse, validNonVolunteerPhaseFa, new List<Sequence>(), lstStandBy, isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                    }
                    qlaDataPhaseId = (int)ROTDPhases.NonVolunteer;
                    nonVolunteerQLARuleResult = lstQLARuleResult;
                    nonVolunteerPhaseQLAResponse = lstQLAResponse;
                    //Force GC after non-volunteer phase
                    ForceGarbageCollection();
                }

                // ROTD Phase - IsVolunteer legality validation
                if (lstFlightAttendant.Any(f => f.IsVolunteer == true))
                {
                    _loggingProvider.LogInformation($"{logTitle} - IsVolunteer legality validation - Flight Attendant has volunteers");

                    var validVolunteerPhaseFa = lstFlightAttendant.Where(f => f.IsVolunteer == true).ToList();
                    // Initial CCS call for all FA and Seq/Stb
                    QLAResponse = GetCrewSequenceAndExecuteCcs(validVolunteerPhaseFa, lstSequence, lstStandBy, baseCD, processingDate, runId, (int)ROTDPhases.IsVolunteer);
                    phase = ExecuteContextualInterpretive(ROTDLegalityPhase.IsVolunteer, QLAResponse, validVolunteerPhaseFa, lstSequence, lstStandBy, isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                    if (qlaDataPhaseId == 0)
                    {
                        qlaDataPhaseId = phase.phaseId;
                    }
                    
                    // Add garbage collection after volunteer phase
                    ForceGarbageCollection();
                }

                //ROTD Legality next phase validations
                if (isTheProcessInvokedFromDbUi)
                {
                    _loggingProvider.LogInformation($"{logTitle} - isTheProcessInvokedFromDbUi: {isTheProcessInvokedFromDbUi.ToString()}");


                    SetROTAPhaseQlaSupportData(runId, Convert.ToInt32(qlaDataPhaseId), processingDate);

                    // ROTD Phase - Future legality validation
                    if (futureQlaSupportingData != null && futureQlaSupportingData.Count > 0)
                    {
                        _loggingProvider.LogInformation($"{logTitle} - futureQlaSupportingData Count: {futureQlaSupportingData.Count}");
                        var validFuturePhaseFa = lstFlightAttendant.Where(x => futureQlaSupportingData.Select(f => f.EmployeeId).Distinct().Contains(x.EmployeeNumber)).ToList();
                        validFuturePhaseFa = validFuturePhaseFa.Where(fa => fa.IsVolunteer == false).Select(fa => fa).ToList();
                        if (validFuturePhaseFa != null && validFuturePhaseFa.Count > 0)
                        {
                            var validFutureSequence = lstSequence.Where(x => futureQlaSupportingData.Select(f => f.PickupActivityId).Distinct().Contains(Convert.ToInt32(x.SequenceNumber))).ToList();

                            futurePhaseQLAResponse = GetROTDPhaseQLARequestResponse(runId, futureQlaSupportingData, ROTDLegalityPhase.Future, validFutureSequence, new List<StandBy>(), validFuturePhaseFa, processingDate);//changed as per to remove the error

                            phase = ExecuteContextualInterpretive(ROTDLegalityPhase.Future, futurePhaseQLAResponse, validFuturePhaseFa, validFutureSequence, new List<StandBy>(), isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                        }
                    }

                    // ROTD Phase - ETB/Future legality validation
                    if (etbFutureQlaSupportingData != null && etbFutureQlaSupportingData.Count > 0)
                    {
                        _loggingProvider.LogInformation($"{logTitle} - etbFutureQlaSupportingData Count: {futureQlaSupportingData.Count}");
                        var validEtbFuturePhaseFa = lstFlightAttendant.Where(x => etbFutureQlaSupportingData.Select(f => f.EmployeeId).Distinct().Contains(x.EmployeeNumber)).ToList();
                        validEtbFuturePhaseFa = validEtbFuturePhaseFa.Where(fa => fa.IsVolunteer == false).Select(fa => fa).ToList();
                        if (validEtbFuturePhaseFa != null && validEtbFuturePhaseFa.Count > 0)
                        {
                            var validEtbFutureSequence = lstSequence.Where(x => etbFutureQlaSupportingData.Select(f => f.PickupActivityId).Distinct().Contains(Convert.ToInt32(x.SequenceNumber))).ToList();

                            etbFuturePhaseQLAResponse = GetROTDPhaseQLARequestResponse(runId, etbFutureQlaSupportingData, ROTDLegalityPhase.ETB_Future, validEtbFutureSequence, new List<StandBy>(), validEtbFuturePhaseFa, processingDate);//changed as per to remove the error
                            phase = ExecuteContextualInterpretive(ROTDLegalityPhase.ETB_Future, etbFuturePhaseQLAResponse, validEtbFuturePhaseFa, validEtbFutureSequence, new List<StandBy>(), isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                        }
                    }

                    // ROTD Phase -  LineHolder Future legality validation
                    if (lineHolderFutureQlaSupportingData != null && lineHolderFutureQlaSupportingData.Count > 0)
                    {
                        _loggingProvider.LogInformation($"{logTitle} - lineHolderFutureQlaSupportingData Count: {futureQlaSupportingData.Count}");

                        var validLineHolderPhaseFa = lstFlightAttendant.Where(x => lineHolderFutureQlaSupportingData.Select(f => f.EmployeeId).Distinct().Contains(x.EmployeeNumber)).ToList();
                        validLineHolderPhaseFa = validLineHolderPhaseFa.Where(fa => fa.IsVolunteer == false).Select(fa => fa).ToList();
                        if (validLineHolderPhaseFa != null && validLineHolderPhaseFa.Count > 0)
                        {
                            var validLineHolderSequence = lstSequence.Where(x => lineHolderFutureQlaSupportingData.Select(f => f.PickupActivityId).Distinct().Contains(Convert.ToInt32(x.SequenceNumber))).ToList();

                            lineHolderPhaseQLAResponse = GetROTDPhaseQLARequestResponse(runId, lineHolderFutureQlaSupportingData, ROTDLegalityPhase.LineHolder_Future, validLineHolderSequence, new List<StandBy>(), validLineHolderPhaseFa, processingDate);
                            phase = ExecuteContextualInterpretive(ROTDLegalityPhase.LineHolder_Future, lineHolderPhaseQLAResponse, validLineHolderPhaseFa, validLineHolderSequence, new List<StandBy>(), isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                        }
                    }

                    // ROTD Phase - ETB legality validation
                    if (etbQlaSupportingData != null && etbQlaSupportingData.Count > 0)
                    {
                        _loggingProvider.LogInformation($"{logTitle} - etbQlaSupportingData Count: {futureQlaSupportingData.Count}");

                        var validEtbPhaseFa = lstFlightAttendant.Where(x => etbQlaSupportingData.Select(f => f.EmployeeId).Distinct().Contains(x.EmployeeNumber)).ToList();
                        validEtbPhaseFa = validEtbPhaseFa.Where(fa => fa.IsVolunteer == false).Select(fa => fa).ToList();
                        if (validEtbPhaseFa != null && validEtbPhaseFa.Count > 0)
                        {
                            var validEtbSequence = lstSequence.Where(x => etbQlaSupportingData.Select(f => f.PickupActivityId).Distinct().Contains(Convert.ToInt32(x.SequenceNumber))).ToList();

                            etbPhaseQLAResponse = GetROTDPhaseQLARequestResponse(runId, etbQlaSupportingData, ROTDLegalityPhase.ETB, validEtbSequence, new List<StandBy>(), validEtbPhaseFa, processingDate);//changed as per to remove the error
                            phase = ExecuteContextualInterpretive(ROTDLegalityPhase.ETB, etbPhaseQLAResponse, validEtbPhaseFa, validEtbSequence, new List<StandBy>(), isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                        }
                    }

                    // ROTD Phase - LineHolder legality validation
                    if (lineHolderQlaSupportingData != null && lineHolderQlaSupportingData.Count > 0)
                    {
                        _loggingProvider.LogInformation($"{logTitle} - lineHolderQlaSupportingData Count: {futureQlaSupportingData.Count}");

                        var validLineHolderPhaseFa = lstFlightAttendant.Where(x => lineHolderQlaSupportingData.Select(f => f.EmployeeId).Distinct().Contains(x.EmployeeNumber)).ToList();
                        validLineHolderPhaseFa = validLineHolderPhaseFa.Where(fa => fa.IsVolunteer == false).Select(fa => fa).ToList();
                        if (validLineHolderPhaseFa != null && validLineHolderPhaseFa.Count > 0)
                        {
                            var validLineHolderSequence = lstSequence.Where(x => lineHolderQlaSupportingData.Select(f => f.PickupActivityId).Distinct().Contains(Convert.ToInt32(x.SequenceNumber))).ToList();
                            lineHolderPhaseQLAResponse = GetROTDPhaseQLARequestResponse(runId, lineHolderQlaSupportingData, ROTDLegalityPhase.LineHolder, validLineHolderSequence, new List<StandBy>(), validLineHolderPhaseFa, processingDate);//changed as per to remove the error
                            phase = ExecuteContextualInterpretive(ROTDLegalityPhase.LineHolder, lineHolderPhaseQLAResponse, validLineHolderPhaseFa, validLineHolderSequence, new List<StandBy>(), isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                        }
                    }

                    // ROTD Phase - LineHolder standby legality validation
                    if (lineHolderStandByQlaSupportingData != null && lineHolderStandByQlaSupportingData.Count > 0)
                    {
                        _loggingProvider.LogInformation($"{logTitle} - lineHolderStandByQlaSupportingData Count: {futureQlaSupportingData.Count}");

                        var validLineHolderPhaseFa = lstFlightAttendant.Where(x => lineHolderStandByQlaSupportingData.Select(f => f.EmployeeId).Distinct().Contains(x.EmployeeNumber)).ToList();
                        validLineHolderPhaseFa = validLineHolderPhaseFa.Where(fa => fa.IsVolunteer == false).Select(fa => fa).ToList();
                        if (validLineHolderPhaseFa != null && validLineHolderPhaseFa.Count > 0)
                        {
                            var validLineHolderStandBy = lstStandBy.Where(x => lineHolderStandByQlaSupportingData.Select(f => f.PickupActivityId).Distinct().Contains(Convert.ToInt32(x.StandByID))).ToList();

                            lineHolderPhaseQLAResponse = GetROTDPhaseQLARequestResponse(runId, lineHolderStandByQlaSupportingData, ROTDLegalityPhase.LineHolderStandBy, new List<Sequence>(), validLineHolderStandBy, validLineHolderPhaseFa, processingDate);//changed as per to remove the error
                            phase = ExecuteContextualInterpretive(ROTDLegalityPhase.LineHolderStandBy, lineHolderPhaseQLAResponse, validLineHolderPhaseFa, new List<Sequence>(), validLineHolderStandBy, isTheProcessInvokedFromDbUi, objBaseDate, runContextId, IsBaseCoTerminal);
                        }
                    }
                    // Force GC after all phases
                    ForceGarbageCollection();
                }
                //come bck and proceed after apigee setup done
                if (LegalityErrorsException != null && LegalityErrorsException.Count > 0)
                {
                    _loggingProvider.LogError($"{logTitle} - Encountered Legality Error. LegalityErrorsException: {LogSanitizer.SerializeSafely(LegalityErrorsException)}");
                    throw new Exception("Error occurred for some records while executing both processor. Employee Ids : " + String.Join(",", LegalityErrorsException.ToArray().Select(x => x.employeeId).Distinct()));
                }

                _loggingProvider.LogInformation("End LoadData-RuleOrchestrator " + "RuleOrchestrator" + "Get ROTD details from Database based on Base and Processing Date. Base : " + baseCD + " ,Bid Orgination Date : " + processingDate.ToShortDateString() + ", Run Id :  " + runId);
            }
            catch (NoValidDataException ex)
            {
                _loggingProvider.LogError(ex, $"{logTitle} - No Valid Data. Message:{ex.Message}");
                throw;
            }
            catch (CCSException ex)
            {
                _loggingProvider.LogError(logTitle+ "Error occured while calling CCS from LoadData for ROTD. " + ex.GetAllException());
                
                // Add garbage collection after exception
                ForceGarbageCollection();
                throw ex;
            }
            catch (Exception ex)
            {
                _loggingProvider.LogError(logTitle+ "Error occured while Load Data from DB for ROTD. " + ex.GetAllException());
                
                // Add garbage collection after exception
                ForceGarbageCollection();
                throw new Exception("Error occured while Load Data from DB for ROTD. " + ex.GetAllException());
            }
        }

        public LegalitiesQLAResponse GetCrewSequenceAndExecuteCcs(List<FlightAttendant> flightAttendant, List<Sequence> sequence, List<StandBy> standBy, string inBase, DateTime bidOrginationDate, int runid, int phaseId)
        {
            empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();
            request = new List<Request>();
            pickupdutylist = new List<Pickupdutylist>();
            dropdutylist = new List<Dropdutylist>();
            objLegalitiesQLARequest = new LegalitiesQLARequest();
            //if (objLegalitiesService == null)
            //    objLegalitiesService = new LegalitiesService();
            objLegalitiesQLAResponse = new LegalitiesQLAResponse();
            qlaRequesResponseDictionary = new Dictionary<string, string>();
            failedLegalitiesRequests = new List<LegalitiesQLARequest>();
            int count = 0;

            double totalSequenceStandBy = (sequence == null ? 0 : sequence.Count) + (standBy == null ? 0 : standBy.Count);
            double ccsRequestCount = Convert.ToDouble(GetApplicationSetting("LegalitiesCCSBatchRequestCount").Result.SettingValues);
            int qlaBatchMaxFA = Convert.ToInt32(GetApplicationSetting("LegalitiesCCSEmployeeCount").Result.SettingValues);
            int asyncBatchCount = Convert.ToInt32(GetApplicationSetting("LegalitiesAsyncBatchCount").Result.SettingValues);
            int qlaRetryCount = Convert.ToInt32(GetApplicationSetting("QLARetryCount").Result.SettingValues);

            qlaBatchMaxFA = qlaBatchMaxFA > 0 ? qlaBatchMaxFA : qlaBatchDefaultMaxFA;
            ccsRequestCount = ccsRequestCount > 0 ? ccsRequestCount : qlaDefaultValue;
            asyncBatchCount = asyncBatchCount > 0 ? asyncBatchCount : qlaAsyncBatchDefaultValue;
            qlaRetryCount = qlaRetryCount > 0 ? qlaRetryCount : 1;
            string ccsQlaEndPoint = GetCCSQLAEndPoint(bidOrginationDate);


            if (String.IsNullOrEmpty(inBase.Trim()))
            {
                if (sequence == null)
                {
                    inBase = "";
                    bidOrginationDate = DateTime.MinValue;
                }
                else
                {
                    inBase = sequence.FirstOrDefault().StartBase;
                    bidOrginationDate = sequence.FirstOrDefault().SequenceStartDateTime;
                }

            }

            if (!String.IsNullOrEmpty(inBase.Trim()) && sequence != null)
            {
                _loggingProvider.LogInformation("Begin GetCrewSequenceAndExecuteCcs-RuleOrchestrator"+ "RuleOrchestrator"+ "Making QLA Request and Execute CCS. Run Id :" + runid + ", Phase Id : "+ phaseId);

                if (totalSequenceStandBy > ccsRequestCount)
                {
                    objLegalitiesQLAResponse = GetQLAResponseForMoreThanCcsMaxRequest(flightAttendant, sequence, standBy, bidOrginationDate, runId, phaseId, ccsRequestCount, qlaBatchMaxFA, asyncBatchCount, qlaRetryCount, ccsQlaEndPoint);
                }
                else
                {
                    foreach (FlightAttendant item in flightAttendant)
                    {
                        try
                        {
                            sequence.ForEach(crew =>
                            {
                                count++;

                                if (crew.SequencePositionDetailsID > 0)
                                {
                                    if (sequence != null)
                                    {
                                        if (item.IsSick && IsSickProcess)
                                        {
                                            dropdutylist.Add(new Dropdutylist
                                            {
                                                airlineCode = CCSDetails.AA.ToString(),
                                                contractMonth = conMonthYear,
                                                activityType = CCSDetails.SCK.ToString(),
                                                activityCode = "00",
                                                activityOriginationDate = bidOrginationDate.ToString("yyyy-MM-dd"),
                                                positionCode = "",
                                                startDateTime = bidOrginationDate.ToString("yyyy-MM-dd'T'HH:mm:ss")
                                            });
                                        }
                                        pickupdutylist.Add(new Pickupdutylist
                                        {
                                            activityId = Convert.ToInt32(crew.SequenceNumber),
                                            activityOriginationDate = crew.OriginationDate.ToString("yyyy-MM-dd"),
                                            activityType = CCSDetails.SEQ.ToString(),
                                            contractMonth = crew.OriginationDate.Date > currentContractMonthEndDate.Date ? nextContractMonth : conMonthYear,
                                            positionCode = Convert.ToString(crew.SequencePosition),
                                            airlineCode = CCSDetails.AA.ToString()
                                        });

                                        //Adding PickUp duty list into Request
                                        request.Add(new Request
                                        {
                                            requestId = Convert.ToInt32(count),
                                            pickupDutyList = pickupdutylist,
                                            dropDutyList = dropdutylist != null && dropdutylist.Count > 0 ? dropdutylist : new List<Dropdutylist>()
                                        });
                                        pickupdutylist = new List<Pickupdutylist>();
                                        dropdutylist = new List<Dropdutylist>();
                                    }
                                }
                            });
                            if (standBy != null)
                            {
                                standBy.ForEach(crew =>
                                {
                                    count++;


                                    var reportTime = standBy.Where(x => x.StandByID == crew.StandByID).Select(x => x.ReportTime).FirstOrDefault();
                                    var shiftStartTime = TimeSpan.Parse(crew.ReportTime.TimeOfDay.ToString());
                                    if (item.IsSick && IsSickProcess)
                                    {
                                        dropdutylist.Add(new Dropdutylist
                                        {
                                            airlineCode = CCSDetails.AA.ToString(),
                                            contractMonth = conMonthYear,
                                            activityType = CCSDetails.SCK.ToString(),
                                            activityCode = "00",
                                            activityOriginationDate = bidOrginationDate.ToString("yyyy-MM-dd"),
                                            positionCode = "",
                                            startDateTime = bidOrginationDate.ToString("yyyy-MM-dd'T'HH:mm:ss")
                                        });
                                    }
                                    pickupdutylist.Add(new Pickupdutylist
                                    {
                                        activityId = Convert.ToInt32(crew.StandByID),
                                        activityOriginationDate = reportTime.ToString("yyyy-MM-dd"),
                                        activityType = CCSDetails.STB.ToString(),
                                        contractMonth = conMonthYear,
                                        airlineCode = CCSDetails.AA.ToString(),
                                        startDateTime = reportTime.ToString("yyyy-MM-dd'T'HH:mm:ss"),
                                        endDateTime = AddTimeToDate(bidOrginationDate.Add(shiftStartTime), crew.ShiftDurationHrs).ToString("yyyy-MM-dd'T'HH:mm:ss"),
                                        activityCode = crew.MinAVLDays.ToString()
                                    });

                                    //Adding PickUp duty list into Request
                                    request.Add(new Request
                                    {
                                        requestId = Convert.ToInt32(count),
                                        pickupDutyList = pickupdutylist,
                                        dropDutyList = dropdutylist != null && dropdutylist.Count > 0 ? dropdutylist : new List<Dropdutylist>()
                                    });
                                    pickupdutylist = new List<Pickupdutylist>();
                                    dropdutylist = new List<Dropdutylist>();

                                });
                            }
                            if (request.Count > 0)
                            {
                                if (objLegalitiesQLARequest.employees == null)
                                    objLegalitiesQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                                //Filling Employee Details
                                empDetails.airlineCode = CCSDetails.AA.ToString();
                                empDetails.employeeID = Convert.ToInt32(item.EmployeeNumber);

                                //Adding Request list into Employee details
                                empDetails.requests = request;

                                //Adding Employee details into QLARequest for last record
                                objLegalitiesQLARequest.employees.Add(empDetails);

                                empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();

                                request = new List<Request>();
                            }
                        }
                        catch (Exception ex)
                        {
                            _loggingProvider.LogError("AA.Crew.Legalities.ROTD"+ " RuleOrchestrator.GetCrewSequenceAndExecuteCCS "+ "Error occured while generating CCS Request.{0} " , ex.GetAllException());
                            throw new Exception("Error occured while generating CCS Request. " + ex.GetAllException());
                        }
                    }

                    if (objLegalitiesQLARequest.employees == null)
                        objLegalitiesQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                    if (objLegalitiesQLARequest != null)
                    {
                        LegalitiesQLARequest = objLegalitiesQLARequest;
                    }

                    try
                    {
                        if (objLegalitiesQLARequest.employees.Count > 0)
                        {
                            int qlaRequestBatchCount = Convert.ToInt32(Math.Floor(ccsRequestCount / totalSequenceStandBy));

                            qlaRequestBatchCount = qlaRequestBatchCount == 0 ? qlaBatchDefaultOneFA : qlaRequestBatchCount > qlaBatchMaxFA ? qlaBatchMaxFA : qlaRequestBatchCount;

                            objLegalitiesQLAResponse.employeeResponses = new List<Reserves.QLA.Model.Response.Legalities.Employeerespons>();
                            objLegalitiesQLAResponse.errorMessages = new List<string>();

                            if (objLegalitiesQLARequest.employees.Count > qlaRequestBatchCount)
                            {
                                var LegalitiesQLABatchRequest = objLegalitiesQLARequest.employees.Select((x, i) => new { Index = i, Value = x })
                                                                                            .GroupBy(x => x.Index / qlaRequestBatchCount)
                                                                                            .Select(x => x.Select(v => v.Value).ToList())
                                                                                            .ToList();

                                if (LegalitiesQLABatchRequest.Count > asyncBatchCount)
                                {
                                    var asyncLegalitiesQLABatchRequest = LegalitiesQLABatchRequest.Select((x, i) => new { Index = i, Value = x })
                                                                                      .GroupBy(x => x.Index / asyncBatchCount)
                                                                                      .Select(x => x.Select(v => v.Value).ToList())
                                                                                      .ToList();

                                    bool tradeIndValue = objLegalitiesQLARequest.tradeInd;
                                    foreach (var asyncRequest in asyncLegalitiesQLABatchRequest)
                                    {
                                        foreach (var requests in asyncRequest)
                                        {
                                            //Task asyncTask = Task.Run(() => AsyncQLARequest(requests, tradeIndValue, runid, phaseId));
                                            //TaskList.Add(asyncTask);
                                            //
                                            //// Use local function instead of lambda
                                            var localRequests = requests;
                                            var localTradeValue = tradeIndValue;
                                            var localRunId1 = runid;
                                            var localPhaseId1 = phaseId;
                                            var localQlaRetryCount = qlaRetryCount;
                                            var localCcsQlaEndPoint = ccsQlaEndPoint;
                                            Task ProcessRequestTask()
                                            {
                                                return AsyncQLARequest(localRequests, localTradeValue, localRunId1, localPhaseId1, localQlaRetryCount ,localCcsQlaEndPoint);
                                            }

                                            TaskList.Add(Task.Run(ProcessRequestTask)); 
                                            //TaskList.Add(Task.Run(() => AsyncQLARequest(requests, tradeIndValue, runid, phaseId, qlaRetryCount, ccsQlaEndPoint)));
                                        }
                                        Task.WaitAll(TaskList.ToArray());
                                        TaskList.Clear();
                                    }
                                }
                                else
                                {
                                    bool tradeIndValue = objLegalitiesQLARequest.tradeInd;
                                    foreach (var requests in LegalitiesQLABatchRequest)
                                    {
                                        //Task asyncTask = Task.Run(() => AsyncQLARequest(requests, tradeIndValue, runid, phaseId));
                                        //TaskList.Add(asyncTask);
                                        //TaskList.Add(Task.Run(() => AsyncQLARequest(requests, tradeIndValue, runid, phaseId, qlaRetryCount, ccsQlaEndPoint)));

                                        var localRequests = requests;
                                        var localTradeValue = tradeIndValue;
                                        var localRunId2 = runid;
                                        var localPhaseId2 = phaseId;
                                        var localQlaRetryCount = qlaRetryCount;
                                        var localCcsQlaEndPoint = ccsQlaEndPoint;
                                        Task ProcessRequestTask()
                                        {
                                            return AsyncQLARequest(localRequests, localTradeValue, localRunId2, localPhaseId2, localQlaRetryCount, localCcsQlaEndPoint);
                                        }

                                        TaskList.Add(Task.Run(ProcessRequestTask));
                                    }
                                    Task.WaitAll(TaskList.ToArray());
                                    TaskList.Clear();
                                }

                                // Retry attempt - QLA call for failed requests
                                while (qlaRetryCount > 0)
                                {
                                    TaskList.Clear();
                                    List<LegalitiesQLARequest> failedRequestForRetry = failedLegalitiesRequests;
                                    failedLegalitiesRequests = new List<LegalitiesQLARequest>();
                                    qlaRetryCount = qlaRetryCount - 1;
                                    foreach (var requests in failedRequestForRetry)
                                    {
                                        //TaskList.Add(Task.Run(() => AsyncQLARequest(requests.employees, false, runid, phaseId, qlaRetryCount, ccsQlaEndPoint)));
                                        var localRequests = requests.employees;
                                        var localTradeValue = false;
                                        var localRunId3 = runid;
                                        var localPhaseId3 = phaseId;
                                        var localQlaRetryCount = qlaRetryCount;
                                        var localCcsQlaEndPoint = ccsQlaEndPoint;
                                        Task ProcessRequestTask()
                                        {
                                            return AsyncQLARequest(localRequests, localTradeValue, localRunId3, localPhaseId3, localQlaRetryCount, localCcsQlaEndPoint);
                                        }

                                        TaskList.Add(Task.Run(ProcessRequestTask));
                                    }
                                    Task.WaitAll(TaskList.ToArray());
                                    TaskList.Clear();
                                    if (failedLegalitiesRequests.Count == 0)
                                    {
                                        qlaRetryCount = 0;
                                    }
                                }

                                _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetCrewSequenceAndExecuteCCS "+ "QLA request log datetime :  {0}" , qlaRequestTimeLogString.ToString());
                                // Save bulk QLA request and response async
                                //Task asyncMessageTask = Task.Run(() => _interpretiveDataProvider.SaveQLAMessage(runid, phaseId, qlaRequesResponseDictionary));
                                var localRunId = runid;
                                var localPhaseId = phaseId;
                                var localQlaRequestResponseDictionary = qlaRequesResponseDictionary;
                                Task asyncMessageTask = Task.Run(() => _interpretiveDataProvider.SaveQLAMessage(localRunId, localPhaseId, localQlaRequestResponseDictionary));
                                messageTaskList.Add(asyncMessageTask);
                                qlaRequesResponseDictionary.Clear();
                            }
                            else
                            {
                                //var legalitysvc = legalityServiceFactory.GetQLAService(ccsQlaEndPoint);
                                if (objLegalitiesQLARequest != null)
                                {

                                    //var res = legalitysvc.ValidateLegalitySync(objLegalitiesQLARequest);
                                    var res = _legalityServiceProvider.ValidateLegality(objLegalitiesQLARequest, ccsQlaEndPoint).Result;
                                    objLegalitiesQLAResponse = res;
                                    objLegalitiesQLAResponse.RawRequest = res.RawRequest;
                                    objLegalitiesQLAResponse.RawResponse = res.RawResponse;
                                    res.RawResponse = null;
                                    res.RawRequest = null;

                                }
                                //objLegalitiesQLAResponse = objLegalitiesService.ValidateLegalitySync(objLegalitiesQLARequest);
                                //_interpretiveDataProvider.SaveQLAMessage(runid, phaseId, objLegalitiesQLAResponse.RawRequest, objLegalitiesQLAResponse.RawResponse);
                                objLegalitiesQLAResponse.RawRequest = null;
                                objLegalitiesQLAResponse.RawResponse = null;
                            }
                        }
                    }
                    catch (AggregateException aeg)
                    {
                        _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetCrewSequenceAndExecuteCCS "+ "QLA request log datetime :  {0} " , qlaRequestTimeLogString.ToString());
                        foreach (var excp in aeg.InnerExceptions)
                        {
                            //come back after data layer migration
                            //throw new Exception("Error occured while executing CCS for ROTD Contextual : " + excp.LoggableString());
                            throw new Exception("Error occured while executing CCS for ROTD Contextual : " + excp.ToString());
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider.LogError("AA.Crew.Legalities.ROTD"+ "RuleOrchestrator.GetCrewSequenceAndExecuteCCS "+ " CCS Error. {0} " , ex.GetAllException());
                        throw new Exception("Error occured while executing CCS for ROTD Contextual. " + ex.GetAllException());
                    }

                }

                HandleQLAErrors(objLegalitiesQLAResponse, phaseId);

                _loggingProvider.LogInformation("End GetCrewSequenceAndExecuteCcs-RuleOrchestrator "+ "RuleOrchestrator"+ "Making QLA Request and Execute CCS. Run Id :  " + runid + ", Phase Id : " + phaseId);
                _loggingProvider.LogInformation("GetCrewSequenceAndExecuteCcs-RuleOrchestrator "+ "RuleOrchestrator"+ "CCS Response : " + objLegalitiesQLAResponse.RawResponse ?? "" + objLegalitiesQLAResponse.errorResponses ?? "");
            }

            return objLegalitiesQLAResponse;
        }

        private string GetCCSQLAEndPoint(DateTime ProcessingDate)
        {
            var ccsQLADateTime = DateTime.MinValue;
            var temp = GetApplicationSetting("CCSQLACLOUDCUTOFFDATE").Result.SettingValues;
            if (!string.IsNullOrEmpty(temp))
            {
                ccsQLADateTime = Convert.ToDateTime(temp);
            }
            return ProcessingDate < ccsQLADateTime ? "QLAOnPrem" : "QLACloud";
        }
        private void ClearTasks()
        {
            lock (lockObject)
            {
                TaskList.Clear();
                messageTaskList.Clear();
            }
        }
        public Reserves.QLA.Model.Response.LegalitiesQLAResponse GetQLAResponseForMoreThanCcsMaxRequest(
            List<FlightAttendant> flightAttendant, 
            List<Sequence> sequence, 
            List<StandBy> standBy, 
            DateTime bidOrginationDate, 
            int runid, 
            int phaseId, 
            double ccsRequestCount, 
            int qlaBatchMaxFA, 
            int asyncBatchCount, 
            int qlaRetryCount, 
            string ccsQlaEndPoint)
        {
            try
            {
                // Clear tasks before starting
                ClearTasks();
                
                empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();
                request = new List<Request>();
                pickupdutylist = new List<Reserves.QLA.Model.Request.Legalities.Pickupdutylist>();
                objLegalitiesQLARequest = new Reserves.QLA.Model.Request.LegalitiesQLARequest();
                //if (objLegalitiesService == null)
                //    objLegalitiesService = new LegalitiesService();
                objLegalitiesQLAResponse = new Reserves.QLA.Model.Response.LegalitiesQLAResponse();
                failedLegalitiesRequests = new List<Reserves.QLA.Model.Request.LegalitiesQLARequest>();
                int count = 0;

                List<Reserves.QLA.Model.Request.LegalitiesQLARequest> lstLegalitiesQLARequest = new List<Reserves.QLA.Model.Request.LegalitiesQLARequest>();
                int requestCounter = 0, faCounter = 0;

                foreach (FlightAttendant item in flightAttendant)
                {
                    try
                    {
                        sequence.ForEach(crew =>
                        {
                            count++;
                            if (crew.SequencePositionDetailsID > 0)
                            {
                                if (sequence != null)
                                {
                                    if (item.IsSick && IsSickProcess)
                                    {
                                        dropdutylist.Add(new Dropdutylist
                                        {
                                            airlineCode = CCSDetails.AA.ToString(),
                                            contractMonth = conMonthYear,
                                            activityType = CCSDetails.SCK.ToString(),
                                            activityCode = "00",
                                            activityOriginationDate = bidOrginationDate.ToString("yyyy-MM-dd"),
                                            positionCode = "",
                                            startDateTime = bidOrginationDate.ToString("yyyy-MM-dd'T'HH:mm:ss")
                                        });
                                    }
                                    pickupdutylist.Add(new Pickupdutylist
                                    {
                                        activityId = Convert.ToInt32(crew.SequenceNumber),
                                        activityOriginationDate = crew.OriginationDate.ToString("yyyy-MM-dd"),
                                        activityType = CCSDetails.SEQ.ToString(),
                                        contractMonth = crew.OriginationDate.Date > currentContractMonthEndDate.Date ? nextContractMonth : conMonthYear,
                                        positionCode = Convert.ToString(crew.SequencePosition),
                                        airlineCode = CCSDetails.AA.ToString()
                                    });

                                    //Adding PickUp duty list into Request
                                    request.Add(new Request
                                    {
                                        requestId = Convert.ToInt32(count),
                                        pickupDutyList = pickupdutylist,
                                        dropDutyList = dropdutylist != null && dropdutylist.Count > 0 ? dropdutylist : new List<Dropdutylist>()
                                    });
                                    pickupdutylist = new List<Pickupdutylist>();
                                    dropdutylist = new List<Dropdutylist>();

                                    requestCounter++;

                                    if (requestCounter == ccsRequestCount || ((sequence.Count - 1) == sequence.IndexOf(crew) && (flightAttendant.Count - 1) == flightAttendant.IndexOf(item)))
                                    {
                                        if (request.Count > 0)
                                        {
                                            if (objLegalitiesQLARequest.employees == null)
                                                objLegalitiesQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                                            //Filling Employee Details
                                            empDetails.airlineCode = CCSDetails.AA.ToString();
                                            empDetails.employeeID = Convert.ToInt32(item.EmployeeNumber);

                                            //Adding Request list into Employee details
                                            empDetails.requests = request;

                                            //Adding Employee details into QLARequest for last record
                                            objLegalitiesQLARequest.employees.Add(empDetails);

                                            lstLegalitiesQLARequest.Add(objLegalitiesQLARequest);
                                            objLegalitiesQLARequest = new LegalitiesQLARequest();
                                            empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();

                                            request = new List<Request>();
                                            requestCounter = 0;
                                            if (faCounter <= qlaBatchMaxFA)
                                            {
                                                faCounter = 0;
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        if (standBy != null)
                        {
                            standBy.ForEach(crew =>
                            {
                                count++;

                                var reportTime = standBy.Where(x => x.StandByID == crew.StandByID).Select(x => x.ReportTime).FirstOrDefault();
                                var shiftStartTime = TimeSpan.Parse(crew.ReportTime.TimeOfDay.ToString());

                                if (item.IsSick && IsSickProcess)
                                {
                                    dropdutylist.Add(new Dropdutylist
                                    {
                                        airlineCode = CCSDetails.AA.ToString(),
                                        contractMonth = conMonthYear,
                                        activityType = CCSDetails.SCK.ToString(),
                                        activityCode = "00",
                                        activityOriginationDate = bidOrginationDate.ToString("yyyy-MM-dd"),
                                        positionCode = "",
                                        startDateTime = bidOrginationDate.ToString("yyyy-MM-dd'T'HH:mm:ss")
                                    });
                                }
                                pickupdutylist.Add(new Pickupdutylist
                                {
                                    activityId = Convert.ToInt32(crew.StandByID),
                                    activityOriginationDate = reportTime.ToString("yyyy-MM-dd"),
                                    activityType = CCSDetails.STB.ToString(),
                                    contractMonth = conMonthYear,
                                    airlineCode = CCSDetails.AA.ToString(),
                                    startDateTime = reportTime.ToString("yyyy-MM-dd'T'HH:mm:ss"),
                                    endDateTime = AddTimeToDate(bidOrginationDate.Add(shiftStartTime), crew.ShiftDurationHrs).ToString("yyyy-MM-dd'T'HH:mm:ss"),
                                    activityCode = crew.MinAVLDays.ToString()
                                });

                                //Adding PickUp duty list into Request
                                request.Add(new Request
                                {
                                    requestId = Convert.ToInt32(count),
                                    pickupDutyList = pickupdutylist,
                                    dropDutyList = dropdutylist != null && dropdutylist.Count > 0 ? dropdutylist : new List<Dropdutylist>()
                                });
                                pickupdutylist = new List<Pickupdutylist>();
                                dropdutylist = new List<Dropdutylist>();

                                requestCounter++;

                                if (requestCounter == ccsRequestCount || ((standBy.Count - 1) == standBy.IndexOf(crew) && (flightAttendant.Count - 1) == flightAttendant.IndexOf(item)))
                                {
                                    if (request.Count > 0)
                                    {
                                        if (objLegalitiesQLARequest.employees == null)
                                            objLegalitiesQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                                        //Filling Employee Details
                                        empDetails.airlineCode = CCSDetails.AA.ToString();
                                        empDetails.employeeID = Convert.ToInt32(item.EmployeeNumber);

                                        //Adding Request list into Employee details
                                        empDetails.requests = request;

                                        //Adding Employee details into QLARequest for last record
                                        objLegalitiesQLARequest.employees.Add(empDetails);

                                        lstLegalitiesQLARequest.Add(objLegalitiesQLARequest);
                                        objLegalitiesQLARequest = new LegalitiesQLARequest();
                                        empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();

                                        request = new List<Request>();
                                        requestCounter = 0;
                                        if (faCounter <= qlaBatchMaxFA)
                                        {
                                            faCounter = 0;
                                        }
                                    }
                                }

                            });
                        }
                        if (request.Count > 0)
                        {
                            if (objLegalitiesQLARequest.employees == null)
                                objLegalitiesQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                            //Filling Employee Details
                            empDetails.airlineCode = CCSDetails.AA.ToString();
                            empDetails.employeeID = Convert.ToInt32(item.EmployeeNumber);

                            //Adding Request list into Employee details
                            empDetails.requests = request;

                            //Adding Employee details into QLARequest for last record
                            objLegalitiesQLARequest.employees.Add(empDetails);

                            empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();

                            request = new List<Request>();
                        }
                        faCounter++;
                        if (faCounter == qlaBatchMaxFA && objLegalitiesQLARequest != null)
                        {
                            lstLegalitiesQLARequest.Add(objLegalitiesQLARequest);
                            objLegalitiesQLARequest = new LegalitiesQLARequest();
                            empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();

                            request = new List<Request>();
                            faCounter = 0;
                            requestCounter = 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider.LogError("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetCrewSequenceAndExecuteCCS "+ "Error occured while generating CCS Request.{0} " , ex.GetAllException());
                        throw new Exception("Error occured while generating CCS Request. " + ex.GetAllException());
                    }
                }

                if (objLegalitiesQLARequest.employees == null)
                    objLegalitiesQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                try
                {
                    if (lstLegalitiesQLARequest.Count > 0)
                    {
                        objLegalitiesQLAResponse.employeeResponses = new List<Reserves.QLA.Model.Response.Legalities.Employeerespons>();
                        objLegalitiesQLAResponse.errorMessages = new List<string>();

                        if (lstLegalitiesQLARequest.Count > asyncBatchCount)
                        {
                            var asyncLegalitiesQLABatchRequest = lstLegalitiesQLARequest.Select((x, i) => new { Index = i, Value = x })
                                                                              .GroupBy(x => x.Index / asyncBatchCount)
                                                                              .Select(x => x.Select(v => v.Value).ToList())
                                                                              .ToList();

                            bool tradeIndValue = objLegalitiesQLARequest.tradeInd;
                            foreach (var asyncRequest in asyncLegalitiesQLABatchRequest)
                            {
                                foreach (var requests in asyncRequest)
                                {
                                    // Create local copies of all captured variables
                                    var localRequests = requests.employees;
                                    var localTradeValue = tradeIndValue;
                                    var localRunId = runid;
                                    var localPhaseId = phaseId;
                                    var localRetryCount = qlaRetryCount;
                                    var localEndPoint = ccsQlaEndPoint;
                                    
                                    // Use local function instead of lambda
                                    Task ProcessRequestTask()
                                    {
                                        return AsyncQLARequest(localRequests, localTradeValue, localRunId, localPhaseId, localRetryCount, localEndPoint);
                                    }
                                    
                                    TaskList.Add(Task.Run(ProcessRequestTask));
                                }
                                Task.WaitAll(TaskList.ToArray());
                                TaskList.Clear(); // Clear after waiting
                            }
                        }
                        else
                        {
                            bool tradeIndValue = objLegalitiesQLARequest.tradeInd;
                            foreach (var requests in lstLegalitiesQLARequest)
                            {
                                //Task asyncTask = Task.Run(() => AsyncQLARequest(requests.employees, tradeIndValue, runid, phaseId));
                                //TaskList.Add(asyncTask);
                                //TaskList.Add(Task.Run(() => AsyncQLARequest(requests.employees, tradeIndValue, runid, phaseId, qlaRetryCount, ccsQlaEndPoint)));
                                var localRequests = requests.employees;
                                var localTradeValue = tradeIndValue;
                                var localRunId = runid;
                                var localPhaseId = phaseId;
                                var localRetryCount = qlaRetryCount;
                                var localEndPoint = ccsQlaEndPoint;

                                // Use local function instead of lambda
                                Task ProcessRequestTask()
                                {
                                    return AsyncQLARequest(localRequests, localTradeValue, localRunId, localPhaseId, localRetryCount, localEndPoint);
                                }

                                TaskList.Add(Task.Run(ProcessRequestTask));
                            }
                            Task.WaitAll(TaskList.ToArray());
                            TaskList.Clear();
                        }

                        // Retry attempt - QLA call for failed requests
                        while (qlaRetryCount > 0)
                        {
                            List<LegalitiesQLARequest> failedRequestForRetry = new List<LegalitiesQLARequest>(failedLegalitiesRequests);
                            failedLegalitiesRequests.Clear(); // Clear immediately after copying
                            
                            int localRetryCount = qlaRetryCount - 1;
                            qlaRetryCount = localRetryCount;
                            
                            foreach (var requests in failedRequestForRetry)
                            {
                                var localRequests = requests.employees;
                                var localRunId = runid;
                                var localPhaseId = phaseId;
                                
                                Task ProcessRetryTask()
                                {
                                    return AsyncQLARequest(localRequests, false, localRunId, localPhaseId, localRetryCount, ccsQlaEndPoint);
                                }
                                
                                TaskList.Add(Task.Run(ProcessRetryTask));
                            }
                            Task.WaitAll(TaskList.ToArray());
                            TaskList.Clear(); // Clear after waiting
                            
                            if (failedLegalitiesRequests.Count == 0)
                            {
                                qlaRetryCount = 0;
                            }
                        }

                        _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetCrewSequenceAndExecuteCCS "+ "QLA request log datetime : {0} " , qlaRequestTimeLogString.ToString());
                        // Save bulk QLA request and response async
                        //Task asyncMessageTask = Task.Run(() => _interpretiveDataProvider.SaveQLAMessage(runid, phaseId, qlaRequesResponseDictionary));
                        var localRunId4 = runid;
                        var localPhaseId4 = phaseId;
                        var localQlaRequestResponseDictionary = qlaRequesResponseDictionary;
                        Task asyncMessageTask = Task.Run(() => _interpretiveDataProvider.SaveQLAMessage(localRunId4, localPhaseId4, localQlaRequestResponseDictionary));
                        messageTaskList.Add(asyncMessageTask);
                        qlaRequesResponseDictionary.Clear(); // Clear after saving messages
                        // Reconstruct request
                        var groupedRequestList = lstLegalitiesQLARequest.SelectMany(x => x.employees).ToList().GroupBy(u => u.employeeID).Select(grp => grp.ToList()).Distinct().ToList();

                        objLegalitiesQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                        foreach (var employeeRequest in groupedRequestList)
                        {
                            empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();
                            empDetails.requests = new List<Request>();
                            foreach (var emp in employeeRequest)
                            {
                                empDetails.requests.AddRange(emp.requests);
                            }
                            empDetails.airlineCode = CCSDetails.AA.ToString();
                            empDetails.employeeID = employeeRequest.First().employeeID;
                            objLegalitiesQLARequest.employees.Add(empDetails);
                        }

                        LegalitiesQLARequest = objLegalitiesQLARequest;

                        // Reconstruct response
                        var groupedResponseList = objLegalitiesQLAResponse.employeeResponses.GroupBy(u => u.employeeID).Select(grp => grp.ToList()).Distinct().ToList();

                        Reserves.QLA.Model.Response.LegalitiesQLAResponse tempLegalitiesQLAResponse = new Reserves.QLA.Model.Response.LegalitiesQLAResponse();
                        tempLegalitiesQLAResponse.employeeResponses = new List<Reserves.QLA.Model.Response.Legalities.Employeerespons>();
                        List<Reserves.QLA.Model.Response.Legalities.Qlarespons> tempQLAResponse = new List<Reserves.QLA.Model.Response.Legalities.Qlarespons>();

                        foreach (var employeeResponses in groupedResponseList)
                        {
                            tempQLAResponse = new List<Reserves.QLA.Model.Response.Legalities.Qlarespons>();
                            foreach (var emp in employeeResponses)
                            {
                                tempQLAResponse.AddRange(emp.qlaResponses);
                            }
                            tempLegalitiesQLAResponse.employeeResponses.Add(new Reserves.QLA.Model.Response.Legalities.Employeerespons
                            {
                                airlineCode = CCSDetails.AA.ToString(),
                                employeeID = employeeResponses.First().employeeID,
                                qlaResponses = tempQLAResponse
                            });
                        }

                        objLegalitiesQLAResponse.employeeResponses = tempLegalitiesQLAResponse.employeeResponses;

                    }
                }
                catch (AggregateException aeg)
                {
                    foreach (var excp in aeg.InnerExceptions)
                    {
                        throw new Exception("Error occured while executing CCS for ROTD Contextual : " + excp.LoggableString());
                    }
                }
                catch (Exception ex)
                {
                    _loggingProvider.LogError("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetQLAResponseForMoreThanCcsMaxRequest "+ "CCS Error. " + ex.GetAllException());
                    throw new Exception("Error occured while executing CCS for ROTD Contextual. " + ex.GetAllException());
                }

                // Clear tasks after use
                ClearTasks();
                
                // Force garbage collection
                ForceGarbageCollection();
                
                return objLegalitiesQLAResponse;
            }
            catch (Exception ex)
            {
                _loggingProvider.LogError("Error in GetQLAResponseForMoreThanCcsMaxRequest: {0}", ex.Message);
                
                // Add garbage collection after exception
                ForceGarbageCollection();
                throw;
            }
        }

        private DateTime AddTimeToDate(DateTime orgDate, string duration)
        {
            if (duration.ToLower().Contains("hrs"))
            {
                string[] hrs = duration.Split(' ');
                int parseNo;
                if (hrs.Length >= 1)
                {
                    if (Int32.TryParse(hrs[0], out parseNo))
                    {
                        orgDate = orgDate.AddHours(Convert.ToInt32(hrs[0]));
                    }
                }
            }

            return orgDate;
        }

        public async Task AsyncQLARequest(List<Reserves.QLA.Model.Request.Legalities.Employee> requests, bool tradeIndValue, int runid, int phaseId, int attempt, string ccsQlaEndPoint)
        {
            Reserves.QLA.Model.Request.LegalitiesQLARequest splitQLARequest = null;
            splitQLARequest = new Reserves.QLA.Model.Request.LegalitiesQLARequest
            {
                employees = requests,
                tradeInd = tradeIndValue
            };
            Reserves.QLA.Model.Response.LegalitiesQLAResponse splitQLAResponse = null;
            try
            {
                lock (lockObject)
                {
                    qlaRequestTimeLogString.AppendLine("Start request id - " + requests.First().requests.First().requestId.ToString() + " datetime - " + DateTime.Now.ToUniversalTime());
                }
                //  splitQLAResponse = await objLegalitiesService.ValidateLegality(splitQLARequest);
                //var legalitysvc = legalityServiceFactory.GetQLAService(ccsQlaEndPoint);
                if (splitQLARequest != null)
                {

                    //var res = legalitysvc.ValidateLegality(splitQLARequest).GetAwaiter().GetResult();
                    var res = _legalityServiceProvider.ValidateLegality(splitQLARequest, ccsQlaEndPoint).Result;
                    splitQLAResponse = res;
                    splitQLAResponse.RawRequest = res.RawRequest;
                    splitQLAResponse.RawResponse = res.RawResponse;


                }
                lock (lockObject)
                {
                    qlaRequestTimeLogString.AppendLine("End request id - " + requests.First().requests.First().requestId.ToString() + " datetime - " + DateTime.Now.ToUniversalTime());
                    objLegalitiesQLAResponse.employeeResponses.AddRange(splitQLAResponse.employeeResponses);
                    if (splitQLAResponse.errorMessages != null)
                    {
                        objLegalitiesQLAResponse.errorMessages.AddRange(splitQLAResponse.errorMessages);
                    }
                    qlaRequesResponseDictionary.Add(splitQLAResponse.RawRequest, splitQLAResponse.RawResponse);
                }
            }
            catch (Exception exp)
            {
                _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetCrewSequenceAndExecuteCCS"+ "Error occured while calling CCS. Error : Attempt (" + attempt.ToString() + ")" + exp.GetAllException());
                _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD"+ "RuleOrchestrator.GetCrewSequenceAndExecuteCCS"+ "Request json: {0} " , LogSanitizer.SerializeSafely(requests));
                if (attempt > 0)
                {
                    failedLegalitiesRequests.Add(splitQLARequest);
                }
                else
                {
                    _loggingProvider.LogError("AA.Crew.Legalities.ROTD.RuleOrchestrator "+ "RuleOrchestrator.AsyncQLARequest "+ "Error calling QLA service ValidateLegality. " + exp.GetAllException());
                    //Causing duplicate data in the logs so commenting this. The issue will be captured while interpreting the data.
                    //LegalityErrorsException.Add(new LegalityErrors { employeeId = (splitQLARequest == null ? "" : string.Join(",", splitQLARequest.employees.Select(x => x.employeeID))), activityId = 0, activityType = "QLAResponse", appType = phaseId.ToString(), errorMessage = exp.Message });
                }
            }

        }

        private void HandleQLAErrors(LegalitiesQLAResponse legalitiesQLAResponse, int phaseId)
        {
            QLAErrors = new List<QLAErrors>();

            if (legalitiesQLAResponse.errorResponses != null)
            {
                if (legalitiesQLAResponse.errorResponses.Length > 0)
                {
                    throw new Exception("No QLA Response. Error Content Found. Raw Response : " + legalitiesQLAResponse.RawResponse);
                }
            }

            if (legalitiesQLAResponse.employeeResponses != null)
            {
                if (legalitiesQLAResponse.employeeResponses.Where(x => x.qlaResponses != null).SelectMany(x => x.qlaResponses).Where(r => r.errorMessages != null).Any())
                {
                    var QLAErr = legalitiesQLAResponse.employeeResponses.Where(x => x.qlaResponses != null).SelectMany(x => x.qlaResponses, (x, qlaRes) => new { x, qlaRes }).Where(r => r.qlaRes.errorMessages != null).Select(res => new QLAErrors
                    {
                        employeeID = res.x.employeeID,
                        errorMessages = res.qlaRes.errorMessages
                    }).ToList();

                    QLAErrors.AddRange(QLAErr);
                }
                if (legalitiesQLAResponse.employeeResponses.Where(x => x.errors != null).Any())
                {
                    var QLAEmpErr = legalitiesQLAResponse.employeeResponses.Where(x => x.errors != null).Select(res => new QLAErrors
                    {
                        employeeID = res.employeeID,
                        errorMessages = res.errors
                    }).ToList();

                    QLAErrors.AddRange(QLAEmpErr);
                }

                if (legalitiesQLAResponse.employeeResponses.Where(x => x.qlaResponses != null).SelectMany(x => x.qlaResponses).Where(r => r.ruleResults != null && r.ruleResults.Any(x => x.rule == "ERROR")).Any())
                {
                    var errRule = legalitiesQLAResponse.employeeResponses.Where(x => x.qlaResponses != null).SelectMany(x => x.qlaResponses, (x, qlaRes) => new { x, qlaRes }).Where(e => e.qlaRes.ruleResults != null && e.qlaRes.ruleResults.Any(r => r.rule == "ERROR")).Select(res => new QLAErrors
                    {
                        employeeID = res.x.employeeID,
                        errorMessages = res.qlaRes.ruleResults.SelectMany(x => x.messages).ToList()
                    }).ToList();
                    QLAErrors.AddRange(errRule);
                }

                if (QLAErrors != null && QLAErrors.Count > 0)
                {
                    QLAErrors.ForEach(x =>
                    {
                        LegalityErrorsException.Add(new LegalityErrors { employeeId = x.employeeID.ToString(), activityType = "QLAResponse", appType = phaseId.ToString(), errorMessage = String.Join(",", x.errorMessages) });
                    });
                    var errors = LogSanitizer.SerializeSafely(QLAErrors);
                }
            }
        }

        public void ForceGarbageCollection()
        {
            GC.Collect(2, GCCollectionMode.Forced, true);
            GC.WaitForPendingFinalizers();
            GC.Collect(2, GCCollectionMode.Forced, true);
        }
            
        

        private ROTDPhase ExecuteContextualInterpretive(ROTDLegalityPhase legalityPhase, LegalitiesQLAResponse legalityQlaResponse, List<FlightAttendant> flightAttendants, List<Sequence> sequence, List<StandBy> standby, bool isInvokedFromDBUI, BaseDate baseDate, int runContextId, bool IsBaseCoTerminal)
        {
            try
            {
                _loggingProvider.LogInformation("Begin ExecuteContextualInterpretive-RuleOrchestrator "+ "RuleOrchestrator"+ "Legality ROTD Process Started for the Phase :  {0}" , legalityPhase.ToString());

                MappingContextualEntites(legalityQlaResponse);
                //rotdProcessor = _container.Resolve<IROTDProcessor>(new ParameterOverrides
                //{
                //    { "phaseId", (Int32)legalityPhase },
                //    { "lstQlaResponse", LegalityQLAResponse },
                //    { "lstQlaRequest", QLARequest },
                //    { "lstQlaRuleResult", LegalityRuleResult },
                //    { "waiver", lstBidCrewWaiver },
                //    { "mappedDetails", mappedDetails },
                //    { "runID", runId }
                //});
                _rotdProcessor.RunId = runId;
                _rotdProcessor.InitializeParameters((Int32)legalityPhase, LegalityQLAResponse, QLARequest, LegalityRuleResult, lstBidCrewWaiver, mappedDetails, runId);

                switch (legalityPhase)
                {
                    case ROTDLegalityPhase.Future:
                        _rotdProcessor.QLASupportingData = futureQlaSupportingData;
                        break;
                    case ROTDLegalityPhase.ETB:
                        _rotdProcessor.QLASupportingData = etbQlaSupportingData;
                        break;
                    case ROTDLegalityPhase.ETB_Future:
                        _rotdProcessor.QLASupportingData = etbFutureQlaSupportingData;
                        break;
                    case ROTDLegalityPhase.LineHolder:
                        _rotdProcessor.QLASupportingData = lineHolderQlaSupportingData;
                        break;
                    case ROTDLegalityPhase.LineHolderStandBy:
                        _rotdProcessor.QLASupportingData = lineHolderStandByQlaSupportingData;
                        break;
                    case ROTDLegalityPhase.LineHolder_Future:
                        _rotdProcessor.QLASupportingData = lineHolderFutureQlaSupportingData;
                        break;
                    default:
                        _rotdProcessor.QLASupportingData = null;
                        break;
                }

                if (_rotdProcessor.QLASupportingData != null && _rotdProcessor.QLASupportingData.Count > 0 &&
                   rapQlaSupportingData != null && rapQlaSupportingData.Count > 0)
                {
                    _rotdProcessor.QLASupportingData.AddRange(rapQlaSupportingData);
                }

                //These are not required as we are getting business objects directly from the service layer
                //List<RAPShifts> lstRapShiftListMapped = new List<RAPShifts>();
                //foreach (var item in lstRAPShift)
                //{
                //    RAPShifts rap = new RAPShifts()
                //    {
                //        ContractMonth = item.contractMonth,
                //        EndDateTime = DateTime.Parse(item.endDateTime),
                //        StartDateTime = DateTime.Parse(item.startDateTime),
                //        Shift = item.shift
                //    };
                //    lstRapShiftListMapped.Add(rap);
                //}


                //List<RAPShifts> lstPreviousMonthRAPShiftMapped = new List<RAPShifts>();
                //foreach (var item in lstPreviousMonthRAPShift)
                //{
                //    RAPShifts rap = new RAPShifts()
                //    {
                //        ContractMonth = item.ContractMonth,
                //        EndDateTime = DateTime.Parse(item.endDateTime),
                //        StartDateTime = DateTime.Parse(item.startDateTime),
                //        Shift = item.shift
                //    };
                //    lstPreviousMonthRAPShiftMapped.Add(rap);
                //}

                var phase = _rotdProcessor.CheckSequenceStandbyLegal(sequence, bids, flightAttendants, lstFaActivity, null, null, lstBidCrewWaiver, null, isInvokedFromDBUI, baseDate, lstRAPShift, IsBaseCoTerminal, lstPreviousMonthRAPShift, runContextId, standby);

                if (_rotdProcessor.LegalityErrorsException != null && _rotdProcessor.LegalityErrorsException.Count > 0)
                {
                    LegalityErrorsException.AddRange(_rotdProcessor.LegalityErrorsException);
                }

                //Check if qla messages has been saved
                Task t = Task.WhenAll(messageTaskList);
                if (t.IsCompleted == false)
                {
                    Task.WaitAll(messageTaskList.ToArray());
                }
                messageTaskList.Clear();

                _loggingProvider.LogInformation("End ExecuteContextualInterpretive-RuleOrchestrator "+ "RuleOrchestrator"+ "Legality ROTD Process Started for the Phase :  {0} " , legalityPhase.ToString());

                return phase;
            }
            catch (Exception ex)
            {
                _loggingProvider.LogError("Error in ExecuteContextualInterpretive: {0}", ex.Message);
                throw;
            }
        }

        public void MappingContextualEntites(LegalitiesQLAResponse CcsResponse)
        {
            QLARequest = new List<QLARequest>();

            if (LegalitiesQLARequest != null)
            {
                LegalitiesQLARequest.employees.ForEach(x =>
                {
                    QLARequest.AddRange(x.requests.Select(req => new QLARequest()
                    {
                        RequestId = req.requestId.ToString(),
                        ActivityID = req.pickupDutyList == null ? 0 : req.pickupDutyList[0].activityId,
                        ActivityType = req.pickupDutyList == null ? "" : req.pickupDutyList[0].activityType,
                        ActivityOriginationDate = req.pickupDutyList == null ? DateTime.MinValue : Convert.ToDateTime(req.pickupDutyList[0].activityOriginationDate),
                        PositionCode = req.pickupDutyList == null ? "" : req.pickupDutyList[0].positionCode,
                        EmployeeID = Convert.ToString(x.employeeID),
                        ActivityCode = req.pickupDutyList == null ? "" : req.pickupDutyList[0].activityCode
                    }).ToList());
                    lstQLARequest = QLARequest;
                });
            }

            if (CcsResponse.employeeResponses == null)
                CcsResponse.employeeResponses = new List<Reserves.QLA.Model.Response.Legalities.Employeerespons>();

            LegalityQLAResponse = CcsResponse.employeeResponses.Where(r => r.qlaResponses != null).SelectMany(s => s.qlaResponses, (s, message) => new { s, message }).Select(resp => new QLAResponse()
            {
                RequestId = resp.message.requestId.ToString(),
                IsContractual = resp.message.isContractual,
                IsLegal = resp.message.isLegal,
                IsQualified = resp.message.isQualified,
                Valid = resp.message.valid,
                EmployeeID = resp.s.employeeID.ToString()
            }).OrderBy(x => Convert.ToInt64(x.RequestId)).ToList();

            lstQLAResponse = LegalityQLAResponse;

            LegalityErrorsException.AddRange(CcsResponse.employeeResponses.Where(s => s.qlaResponses == null).Select(x => new LegalityErrors
            {
                appType = "ROTD : CCS Response not found",
                employeeId = x.employeeID.ToString(),
                errorMessage = x.errors != null ? String.Join(",", x.errors) : ""
            }).ToList());

            LegalityRuleResult = CcsResponse.employeeResponses.Where(r => r.qlaResponses != null).SelectMany(s => s.qlaResponses).Where(r => r.ruleResults != null).SelectMany(x => x.ruleResults, (x, message) => new { x, message }).Select(resp => new QLARuleResult()
            {
                Rule = resp.message.rule,
                Result = resp.message.result,
                Messages = resp.message.messages[0],
                RequestId = resp.x.requestId.ToString(),
                messageObjects = new MessageObjects()
                {
                    pickups = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).pickups != null ? resp.message.messageObjects.pickups : new List<Reserves.QLA.Model.Response.Legalities.Pickup>()).Where(x => x != null).Select(x => new Pickup()
                    {
                        activityCode = x.activityCode,
                        activityOriginationDate = x.activityOriginationDate,
                        activityId = x.activityId,
                        activityType = x.activityType,
                        contractMonth = x.contractMonth,
                        positionCode = x.positionCode,
                        startDateTime = x.startDateTime,
                        endDateTime = x.endDateTime
                    }).ToList(),

                    affectedBy = new AffectedBy
                    {
                        activityCode = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedBy != null ? resp.message.messageObjects.affectedBy : new Reserves.QLA.Model.Response.Legalities.AffectedBy()).activityCode,
                        activityOriginationDate = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedBy != null ? resp.message.messageObjects.affectedBy : new Reserves.QLA.Model.Response.Legalities.AffectedBy()).activityOriginationDate,
                        activityId = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedBy != null ? resp.message.messageObjects.affectedBy : new Reserves.QLA.Model.Response.Legalities.AffectedBy()).activityId,
                        activityType = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedBy != null ? resp.message.messageObjects.affectedBy : new Reserves.QLA.Model.Response.Legalities.AffectedBy()).activityType,
                        contractMonth = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedBy != null ? resp.message.messageObjects.affectedBy : new Reserves.QLA.Model.Response.Legalities.AffectedBy()).contractMonth,
                        positionCode = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedBy != null ? resp.message.messageObjects.affectedBy : new Reserves.QLA.Model.Response.Legalities.AffectedBy()).positionCode,
                        startDateTime = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedBy != null ? resp.message.messageObjects.affectedBy : new Reserves.QLA.Model.Response.Legalities.AffectedBy()).startDateTime,
                        endDateTime = ((resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedBy != null ? resp.message.messageObjects.affectedBy : new Reserves.QLA.Model.Response.Legalities.AffectedBy()).endDateTime
                    },

                    affectedType = (resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedType,

                    affectedStartDateTime = (resp.message.messageObjects != null ? resp.message.messageObjects : new Reserves.QLA.Model.Response.Legalities.MessageObjects()).affectedStartDateTime
                }

            }).OrderBy(x => Convert.ToInt64(x.RequestId)).ToList();

            lstQLARuleResult = LegalityRuleResult;
        }

        public LegalitiesQLAResponse GetROTDPhaseQLARequestResponse(int runId, List<LegalityQLASupportingData> qlaSupportingData, ROTDLegalityPhase legalityPhase, List<Sequence> sequence, List<StandBy> standBy, List<FlightAttendant> flightAttendants, DateTime processingDate)
        {
            empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();
            request = new List<Request>();
            pickupdutylist = new List<Pickupdutylist>();
            phaseQLARequest = new LegalitiesQLARequest();
            phaseQLAResponse = new LegalitiesQLAResponse();
            //if (objLegalitiesService == null)
            //    objLegalitiesService = new LegalitiesService();
            dropdutylist = new List<Dropdutylist>();
            futurePhaseQLAResponse = new LegalitiesQLAResponse();
            phaseQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();
            futurePhaseQLARequest = new LegalitiesQLARequest();
            etbPhaseQLARequest = new LegalitiesQLARequest();
            etbFuturePhaseQLARequest = new LegalitiesQLARequest();
            lineHolderPhaseQLARequest = new LegalitiesQLARequest();
            int count = 0;
            int reqId = 1;

            if (qlaSupportingData != null && qlaSupportingData.Count > 0)
            {
                double totalSequenceStandBy = (sequence == null ? 0 : sequence.Count) + (standBy == null ? 0 : standBy.Count);
                double ccsRequestCount = Convert.ToDouble(GetApplicationSetting("LegalitiesCCSBatchRequestCount").Result.SettingValues);
                int qlaBatchMaxFA = Convert.ToInt32(GetApplicationSetting("LegalitiesCCSEmployeeCount").Result.SettingValues);
                string ccsQlaEndPoint = GetCCSQLAEndPoint(processingDate);
                qlaBatchMaxFA = qlaBatchMaxFA > 0 ? qlaBatchMaxFA : qlaBatchDefaultMaxFA;
                ccsRequestCount = ccsRequestCount > 0 ? ccsRequestCount : qlaDefaultValue;

                if (totalSequenceStandBy > ccsRequestCount)
                {
                    phaseQLAResponse = GetROTDPhaseQLAResponseForMoreThanCcsMaxRequest(runId, qlaSupportingData, legalityPhase, ccsRequestCount, qlaBatchMaxFA, flightAttendants, processingDate, ccsQlaEndPoint);
                }
                else
                {
                    try
                    {
                        var qlaRequestData = (from qlaSupp in qlaSupportingData
                                              orderby qlaSupp.EmployeeId, qlaSupp.PickupActivityType, qlaSupp.PickupActivityId, qlaSupp.PickupPositionCode, qlaSupp.PickupActivityOriginationDate
                                              select new
                                              {
                                                  RunId = qlaSupp.RunId,
                                                  EmployeeId = qlaSupp.EmployeeId,
                                                  AffectedType = qlaSupp.AffectedType,
                                                  ContractMonth = qlaSupp.ContractMonth,
                                                  ActivityId = qlaSupp.ActivityId,
                                                  ActivityCode = qlaSupp.ActivityCode,
                                                  ActivityType = qlaSupp.ActivityType,
                                                  ActivityOriginationDate = qlaSupp.ActivityOriginationDate,
                                                  PositionCode = qlaSupp.PositionCode,
                                                  PickupContractMonth = qlaSupp.PickupContractMonth,
                                                  PickupActivityId = qlaSupp.PickupActivityId,
                                                  PickupActivityCode = qlaSupp.PickupActivityCode,
                                                  PickupActivityType = qlaSupp.PickupActivityType,
                                                  PickupActivityOriginationDate = qlaSupp.PickupActivityOriginationDate,
                                                  PickupPositionCode = qlaSupp.PickupPositionCode,
                                                  PickupStartDateTime = qlaSupp.PickupActivityType.ToUpper() == QLARequestActivityType.Stb.ToUpper() ? qlaSupp.PickupStartDateTime.ToString("yyyy-MM-dd'T'hh:mm:ss") : null,
                                                  PickupEndDateTime = qlaSupp.PickupActivityType.ToUpper() == QLARequestActivityType.Stb.ToUpper() ? qlaSupp.PickupEndDateTime : null,
                                                  ActivityReportDateTime = qlaSupp.ActivityReportDateTime
                                              }).Distinct().ToList();

                        foreach (var item in qlaRequestData)
                        {
                            count++;
                            bool isFASickFlag = flightAttendants.Where(x => x.EmployeeNumber == item.EmployeeId).Select(y => y.IsSick).FirstOrDefault();
                            string standByDropStartDateTime = null;
                            if (item.ActivityType != null && item.ActivityType == QLARequestActivityType.Stb.ToUpper())
                            {
                                standByDropStartDateTime = item.ActivityReportDateTime.HasValue ? item.ActivityReportDateTime.Value.ToString("yyyy-MM-dd'T'HH:mm:ss") : null;
                            }

                            if (item.PickupActivityType.ToUpper() == QLARequestActivityType.Seq.ToUpper())
                            {
                                dropdutylist.Add(new Dropdutylist
                                {
                                    activityId = item.ActivityId,
                                    activityOriginationDate = item.ActivityOriginationDate,
                                    activityType = string.IsNullOrEmpty(item.ActivityType) ? null : item.ActivityType.ToUpper(),
                                    contractMonth = item.ContractMonth,
                                    positionCode = item.PositionCode,
                                    airlineCode = CCSDetails.AA.ToString(),
                                    startDateTime = standByDropStartDateTime
                                });


                                if (pickupdutylist.Count == 0)
                                {
                                    if (IsSickProcess && isFASickFlag)
                                    {
                                        dropdutylist.Add(new Dropdutylist
                                        {
                                            airlineCode = CCSDetails.AA.ToString(),
                                            contractMonth = item.ContractMonth,
                                            activityType = CCSDetails.SCK.ToString(),
                                            activityCode = "00",
                                            activityOriginationDate = processingDate.ToString("yyyy-MM-dd"),
                                            positionCode = "",
                                            startDateTime = processingDate.ToString("yyyy-MM-dd'T'HH:mm:ss")
                                        });
                                    }
                                    pickupdutylist.Add(new Pickupdutylist
                                    {
                                        activityId = item.PickupActivityId,
                                        activityOriginationDate = item.PickupActivityOriginationDate,
                                        activityType = item.PickupActivityType.ToUpper(),
                                        contractMonth = item.PickupContractMonth,
                                        positionCode = item.PickupPositionCode,
                                        airlineCode = CCSDetails.AA.ToString()
                                    });
                                }
                            }

                            if (item.PickupActivityType.ToUpper() == QLARequestActivityType.Stb.ToUpper())
                            {
                                dropdutylist.Add(new Dropdutylist
                                {
                                    activityId = item.ActivityId,
                                    activityOriginationDate = item.ActivityOriginationDate,
                                    activityType = string.IsNullOrEmpty(item.ActivityType) ? null : item.ActivityType.ToUpper(),
                                    contractMonth = item.ContractMonth,
                                    positionCode = item.PositionCode,
                                    airlineCode = CCSDetails.AA.ToString(),
                                    startDateTime = standByDropStartDateTime
                                });


                                if (pickupdutylist.Count == 0)
                                {
                                    if (IsSickProcess && isFASickFlag)
                                    {
                                        dropdutylist.Add(new Dropdutylist
                                        {
                                            airlineCode = CCSDetails.AA.ToString(),
                                            contractMonth = item.ContractMonth,
                                            activityType = CCSDetails.SCK.ToString(),
                                            activityCode = "00",
                                            activityOriginationDate = processingDate.ToString("yyyy-MM-dd"),
                                            positionCode = "",
                                            startDateTime = processingDate.ToString("yyyy-MM-dd'T'HH:mm:ss")
                                        });
                                    }
                                    pickupdutylist.Add(new Pickupdutylist
                                    {
                                        activityId = item.PickupActivityId,
                                        activityOriginationDate = item.PickupActivityOriginationDate,
                                        activityType = item.PickupActivityType.ToUpper(),
                                        contractMonth = item.PickupContractMonth,
                                        airlineCode = CCSDetails.AA.ToString(),
                                        startDateTime = item.PickupStartDateTime,
                                        endDateTime = item.PickupEndDateTime,
                                        activityCode = item.PickupActivityCode
                                    });
                                }
                            }

                            if (qlaRequestData.Count == count ||
                               (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId != item.EmployeeId) ||
                               (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId == item.EmployeeId && qlaRequestData[count].PickupActivityId != item.PickupActivityId) ||
                               (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId == item.EmployeeId && qlaRequestData[count].PickupActivityId == item.PickupActivityId &&
                                qlaRequestData[count].PickupActivityType == item.PickupActivityType && qlaRequestData[count].PickupPositionCode != item.PickupPositionCode && item.PickupActivityType.ToUpper() == CCSDetails.SEQ.ToString().ToUpper()) ||
                               (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId == item.EmployeeId && qlaRequestData[count].PickupActivityId == item.PickupActivityId &&
                                qlaRequestData[count].PickupActivityType == item.PickupActivityType && qlaRequestData[count].PickupPositionCode == item.PickupPositionCode && qlaRequestData[count].PickupActivityOriginationDate != item.PickupActivityOriginationDate && item.PickupActivityType.ToUpper() == CCSDetails.SEQ.ToString().ToUpper()))
                            {
                                //Adding PickUp duty list into Request
                                request.Add(new Request
                                {
                                    requestId = reqId++,
                                    pickupDutyList = pickupdutylist,
                                    dropDutyList = dropdutylist
                                });
                                pickupdutylist = new List<Pickupdutylist>();
                                dropdutylist = new List<Dropdutylist>();
                            }

                            if (qlaRequestData.Count == count || (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId != item.EmployeeId))
                            {

                                if (request.Count > 0)
                                {
                                    //Filling Employee Details
                                    empDetails.airlineCode = CCSDetails.AA.ToString();
                                    empDetails.employeeID = Convert.ToInt32(item.EmployeeId);

                                    //Adding Request list into Employee details
                                    empDetails.requests = request;

                                    //Adding Employee details into QLARequest for last record
                                    phaseQLARequest.employees.Add(empDetails);

                                    empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();

                                    request = new List<Request>();
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider.LogError(" AA.Crew.Legalities.ROTD " + "RuleOrchestrator.GetROTDPhaseQLARequestResponse " + "Error occured while constructing QLA request. " + ex.GetAllException());
                        throw ex;
                    }

                    try
                    {
                        if (phaseQLARequest.employees == null)
                            phaseQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                        if (phaseQLARequest != null)
                        {
                            LegalitiesQLARequest = phaseQLARequest;
                            if (legalityPhase == ROTDLegalityPhase.Future)
                            {
                                futurePhaseQLARequest = phaseQLARequest;
                            }
                            else if (legalityPhase == ROTDLegalityPhase.ETB)
                            {
                                etbPhaseQLARequest = phaseQLARequest;
                            }
                            else if (legalityPhase == ROTDLegalityPhase.ETB_Future)
                            {
                                etbFuturePhaseQLARequest = phaseQLARequest;
                            }
                            else if (legalityPhase == ROTDLegalityPhase.LineHolder)
                            {
                                lineHolderPhaseQLARequest = phaseQLARequest;
                            }
                        }

                        if (phaseQLARequest.employees.Count > 0)
                        {
                            int qlaRequestBatchCount = Convert.ToInt32(Math.Floor(ccsRequestCount / totalSequenceStandBy));
                            qlaRequestBatchCount = qlaRequestBatchCount == 0 ? qlaBatchDefaultOneFA : qlaRequestBatchCount > qlaBatchMaxFA ? qlaBatchMaxFA : qlaRequestBatchCount;

                            phaseQLAResponse.employeeResponses = new List<Reserves.QLA.Model.Response.Legalities.Employeerespons>();
                            phaseQLAResponse.errorMessages = new List<string>();

                            if (phaseQLARequest.employees.Count > qlaRequestBatchCount)
                            {
                                var LegalitiesQLABatchRequest = phaseQLARequest.employees.Select((x, i) => new { Index = i, Value = x })
                                                                                            .GroupBy(x => x.Index / qlaRequestBatchCount)
                                                                                            .Select(x => x.Select(v => v.Value).ToList())
                                                                                            .ToList();

                                foreach (var requests in LegalitiesQLABatchRequest)
                                {
                                    LegalitiesQLARequest splitQLARequest = null;
                                    splitQLARequest = new LegalitiesQLARequest
                                    {
                                        employees = requests
                                    };
                                    LegalitiesQLAResponse splitQLAResponse = null;
                                    try
                                    {

                                        //var legalitysvc = legalityServiceFactory.GetQLAService(ccsQlaEndPoint);
                                        if (splitQLARequest != null)
                                        {

                                            //var res = legalitysvc.ValidateLegalitySync(splitQLARequest);
                                            var res = _legalityServiceProvider.ValidateLegality(splitQLARequest, ccsQlaEndPoint).Result;
                                            splitQLAResponse = res;
                                            splitQLAResponse.RawRequest = res.RawRequest;
                                            splitQLAResponse.RawResponse = res.RawResponse;
                                            res.RawRequest = null;
                                            res.RawResponse = null;
                                        }
                                        // splitQLAResponse = objLegalitiesService.ValidateLegalitySync(splitQLARequest);
                                        phaseQLAResponse.employeeResponses.AddRange(splitQLAResponse.employeeResponses);
                                        if (splitQLAResponse.errorMessages != null)
                                        {
                                            phaseQLAResponse.errorMessages.AddRange(splitQLAResponse.errorMessages);
                                        }
                                        //_interpretiveDataProvider.SaveQLAMessage(runId, (long)legalityPhase, splitQLAResponse.RawRequest, splitQLAResponse.RawResponse);
                                        splitQLAResponse = null;
                                    }
                                    catch (Exception exp)
                                    {
                                        _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetROTDPhaseQLARequestResponse"+ "Error occured while calling CCS. Error :  {0}" , exp.Message);
                                        _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetROTDPhaseQLARequestResponse"+ "Request json:  {0}" , LogSanitizer.SerializeSafely(requests));
                                        //string rawRequest = JsonConvert.SerializeObject(requests);
                                        //rotdInterpretiveRepository.SaveQLAMessage(runId, (long)legalityPhase, rawRequest, exp.Message);
                                        throw exp;
                                    }
                                }
                            }
                            else
                            {
                                //phaseQLAResponse = objLegalitiesService.ValidateLegalitySync(phaseQLARequest);
                                //var legalitysvc = legalityServiceFactory.GetQLAService(ccsQlaEndPoint);
                                if (phaseQLARequest != null)
                                {

                                    //var res = legalitysvc.ValidateLegalitySync(phaseQLARequest);
                                    var res = _legalityServiceProvider.ValidateLegality(phaseQLARequest, ccsQlaEndPoint).Result;
                                    phaseQLAResponse = res;
                                    phaseQLAResponse.RawRequest = res.RawRequest;
                                    phaseQLAResponse.RawResponse = res.RawResponse;
                                    res.RawRequest = null;
                                    res.RawResponse = null;
                                }
                                //_interpretiveDataProvider.SaveQLAMessage(runId, (long)legalityPhase, phaseQLAResponse.RawRequest, phaseQLAResponse.RawResponse);
                                phaseQLAResponse.RawRequest = null;
                                phaseQLAResponse.RawResponse = null;
                            }
                        }

                        HandleQLAErrors(phaseQLAResponse, (int)legalityPhase);
                    }
                    catch (Exception ex)
                    {
                        _loggingProvider.LogError("AA.Crew.Legalities.ROTD "+ " RuleOrchestrator.GetROTDPhaseQLARequestResponse"+ " Error occured while calling QLA service.{0} " , ex.GetAllException());
                        throw ex;
                    }
                }
            }

            return phaseQLAResponse;
        }

        private LegalitiesQLAResponse GetROTDPhaseQLAResponseForMoreThanCcsMaxRequest(int runId, List<LegalityQLASupportingData> qlaSupportingData, ROTDLegalityPhase legalityPhase, double ccsRequestCount, int qlaBatchMaxFA, List<FlightAttendant> flightAttendants, DateTime processingDate, string ccsQlaEndPoint)
        {
            empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();
            request = new List<Request>();
            pickupdutylist = new List<Pickupdutylist>();
            phaseQLARequest = new LegalitiesQLARequest();
            phaseQLAResponse = new LegalitiesQLAResponse();
            //if (objLegalitiesService == null)
            //    objLegalitiesService = new LegalitiesService();
            dropdutylist = new List<Dropdutylist>();
            futurePhaseQLAResponse = new LegalitiesQLAResponse();
            phaseQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();
            futurePhaseQLARequest = new LegalitiesQLARequest();
            etbPhaseQLARequest = new LegalitiesQLARequest();
            etbFuturePhaseQLARequest = new LegalitiesQLARequest();
            int count = 0;
            int reqId = 1;

            List<LegalitiesQLARequest> lstLegalitiesQLARequest = new List<LegalitiesQLARequest>();
            int requestCounter = 0, faCounter = 0;

            if (qlaSupportingData != null && qlaSupportingData.Count > 0)
            {
                try
                {
                    var qlaRequestData = (from qlaSupp in qlaSupportingData
                                          orderby qlaSupp.EmployeeId, qlaSupp.PickupActivityType, qlaSupp.PickupActivityId, qlaSupp.PickupPositionCode, qlaSupp.PickupActivityOriginationDate
                                          select new
                                          {
                                              RunId = qlaSupp.RunId,
                                              EmployeeId = qlaSupp.EmployeeId,
                                              AffectedType = qlaSupp.AffectedType,
                                              ContractMonth = qlaSupp.ContractMonth,
                                              ActivityId = qlaSupp.ActivityId,
                                              ActivityCode = qlaSupp.ActivityCode,
                                              ActivityType = qlaSupp.ActivityType,
                                              ActivityOriginationDate = qlaSupp.ActivityOriginationDate,
                                              PositionCode = qlaSupp.PositionCode,
                                              PickupContractMonth = qlaSupp.PickupContractMonth,
                                              PickupActivityId = qlaSupp.PickupActivityId,
                                              PickupActivityCode = qlaSupp.PickupActivityCode,
                                              PickupActivityType = qlaSupp.PickupActivityType,
                                              PickupActivityOriginationDate = qlaSupp.PickupActivityOriginationDate,
                                              PickupPositionCode = qlaSupp.PickupPositionCode,
                                              PickupStartDateTime = qlaSupp.PickupActivityType.ToUpper() == QLARequestActivityType.Stb.ToUpper() ? qlaSupp.PickupStartDateTime.ToString("yyyy-MM-dd'T'hh:mm:ss") : null,
                                              PickupEndDateTime = qlaSupp.PickupActivityType.ToUpper() == QLARequestActivityType.Stb.ToUpper() ? qlaSupp.PickupEndDateTime : null,
                                              ActivityReportDateTime = qlaSupp.ActivityReportDateTime
                                          }).Distinct().ToList();

                    foreach (var item in qlaRequestData)
                    {
                        count++;
                        bool isFASickFlag = flightAttendants.Where(x => x.EmployeeNumber == item.EmployeeId).Select(y => y.IsSick).FirstOrDefault();
                        string standByDropStartDateTime = null;
                        if (item.ActivityType != null && item.ActivityType == QLARequestActivityType.Stb.ToUpper())
                        {
                            standByDropStartDateTime = item.ActivityReportDateTime.HasValue ? item.ActivityReportDateTime.Value.ToString("yyyy-MM-dd'T'HH:mm:ss") : null;
                        }

                        if (item.PickupActivityType.ToUpper() == QLARequestActivityType.Seq.ToUpper())
                        {
                            dropdutylist.Add(new Dropdutylist
                            {
                                activityId = item.ActivityId,
                                activityOriginationDate = item.ActivityOriginationDate,
                                activityType = string.IsNullOrEmpty(item.ActivityType) ? null : item.ActivityType.ToUpper(),
                                contractMonth = item.ContractMonth,
                                positionCode = item.PositionCode,
                                airlineCode = CCSDetails.AA.ToString(),
                                startDateTime = standByDropStartDateTime
                            });


                            if (pickupdutylist.Count == 0)
                            {
                                if (IsSickProcess && isFASickFlag)
                                {
                                    dropdutylist.Add(new Dropdutylist
                                    {
                                        airlineCode = CCSDetails.AA.ToString(),
                                        contractMonth = item.ContractMonth,
                                        activityType = CCSDetails.SCK.ToString(),
                                        activityCode = "00",
                                        activityOriginationDate = processingDate.ToString("yyyy-MM-dd"),
                                        positionCode = "",
                                        startDateTime = processingDate.ToString("yyyy-MM-dd'T'HH:mm:ss")
                                    });
                                }
                                pickupdutylist.Add(new Pickupdutylist
                                {
                                    activityId = item.PickupActivityId,
                                    activityOriginationDate = item.PickupActivityOriginationDate,
                                    activityType = item.PickupActivityType.ToUpper(),
                                    contractMonth = item.PickupContractMonth,
                                    positionCode = item.PickupPositionCode,
                                    airlineCode = CCSDetails.AA.ToString()
                                });
                            }
                        }

                        if (item.PickupActivityType.ToUpper() == QLARequestActivityType.Stb.ToUpper())
                        {
                            dropdutylist.Add(new Dropdutylist
                            {
                                activityId = item.ActivityId,
                                activityOriginationDate = item.ActivityOriginationDate,
                                activityType = string.IsNullOrEmpty(item.ActivityType) ? null : item.ActivityType.ToUpper(),
                                contractMonth = item.ContractMonth,
                                positionCode = item.PositionCode,
                                airlineCode = CCSDetails.AA.ToString(),
                                startDateTime = standByDropStartDateTime
                            });


                            if (pickupdutylist.Count == 0)
                            {
                                if (IsSickProcess && isFASickFlag)
                                {
                                    dropdutylist.Add(new Dropdutylist
                                    {
                                        airlineCode = CCSDetails.AA.ToString(),
                                        contractMonth = item.ContractMonth,
                                        activityType = CCSDetails.SCK.ToString(),
                                        activityCode = "00",
                                        activityOriginationDate = processingDate.ToString("yyyy-MM-dd"),
                                        positionCode = "",
                                        startDateTime = processingDate.ToString("yyyy-MM-dd'T'HH:mm:ss")
                                    });
                                }
                                pickupdutylist.Add(new Pickupdutylist
                                {
                                    activityId = item.PickupActivityId,
                                    activityOriginationDate = item.PickupActivityOriginationDate,
                                    activityType = item.PickupActivityType.ToUpper(),
                                    contractMonth = item.PickupContractMonth,
                                    airlineCode = CCSDetails.AA.ToString(),
                                    startDateTime = item.PickupStartDateTime,
                                    endDateTime = item.PickupEndDateTime,
                                    activityCode = item.PickupActivityCode
                                });
                            }
                        }

                        if (qlaRequestData.Count == count ||
                           (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId != item.EmployeeId) ||
                           (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId == item.EmployeeId && qlaRequestData[count].PickupActivityId != item.PickupActivityId) ||
                           (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId == item.EmployeeId && qlaRequestData[count].PickupActivityId == item.PickupActivityId &&
                            qlaRequestData[count].PickupActivityType == item.PickupActivityType && qlaRequestData[count].PickupPositionCode != item.PickupPositionCode && item.PickupActivityType.ToUpper() == CCSDetails.SEQ.ToString().ToUpper()) ||
                           (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId == item.EmployeeId && qlaRequestData[count].PickupActivityId == item.PickupActivityId &&
                            qlaRequestData[count].PickupActivityType == item.PickupActivityType && qlaRequestData[count].PickupPositionCode == item.PickupPositionCode && qlaRequestData[count].PickupActivityOriginationDate != item.PickupActivityOriginationDate && item.PickupActivityType.ToUpper() == CCSDetails.SEQ.ToString().ToUpper()))
                        {
                            //Adding PickUp duty list into Request
                            request.Add(new Request
                            {
                                requestId = reqId++,
                                pickupDutyList = pickupdutylist,
                                dropDutyList = dropdutylist
                            });
                            pickupdutylist = new List<Pickupdutylist>();
                            dropdutylist = new List<Dropdutylist>();

                            requestCounter++;

                            if (requestCounter == ccsRequestCount || qlaRequestData.Count == count)
                            {
                                if (request.Count > 0)
                                {
                                    if (phaseQLARequest.employees == null)
                                        phaseQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                                    //Filling Employee Details
                                    empDetails.airlineCode = CCSDetails.AA.ToString();
                                    empDetails.employeeID = Convert.ToInt32(item.EmployeeId);

                                    //Adding Request list into Employee details
                                    empDetails.requests = request;

                                    //Adding Employee details into QLARequest for last record
                                    phaseQLARequest.employees.Add(empDetails);
                                    lstLegalitiesQLARequest.Add(phaseQLARequest);
                                    phaseQLARequest = new LegalitiesQLARequest();

                                    empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();

                                    request = new List<Request>();
                                    requestCounter = 0;
                                    if (faCounter <= qlaBatchMaxFA)
                                    {
                                        faCounter = 0;
                                    }
                                }
                            }
                        }

                        if (qlaRequestData.Count == count || (qlaRequestData.Count > count && qlaRequestData[count].EmployeeId != item.EmployeeId))
                        {
                            if (request.Count > 0)
                            {
                                if (phaseQLARequest.employees == null)
                                    phaseQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                                //Filling Employee Details
                                empDetails.airlineCode = CCSDetails.AA.ToString();
                                empDetails.employeeID = Convert.ToInt32(item.EmployeeId);

                                //Adding Request list into Employee details
                                empDetails.requests = request;

                                //Adding Employee details into QLARequest for last record
                                phaseQLARequest.employees.Add(empDetails);

                                empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();

                                request = new List<Request>();
                            }
                            faCounter++;
                            if (faCounter == qlaBatchMaxFA && phaseQLARequest != null)
                            {
                                lstLegalitiesQLARequest.Add(phaseQLARequest);
                                phaseQLARequest = new LegalitiesQLARequest();
                                empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();
                                request = new List<Request>();
                                faCounter = 0;
                                requestCounter = 0;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _loggingProvider.LogError("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetROTDPhaseQLAResponseForMoreThanCcsMaxRequest"+ "Error occured while constructing QLA request.{0} " , ex.GetAllException());
                    throw ex;
                }

                try
                {
                    if (phaseQLARequest.employees == null)
                        phaseQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                    if (lstLegalitiesQLARequest.Count > 0)
                    {
                        phaseQLAResponse.employeeResponses = new List<Reserves.QLA.Model.Response.Legalities.Employeerespons>();
                        phaseQLAResponse.errorMessages = new List<string>();

                        foreach (var requests in lstLegalitiesQLARequest)
                        {
                            LegalitiesQLARequest splitQLARequest = null;
                            splitQLARequest = new LegalitiesQLARequest
                            {
                                employees = requests.employees
                            };
                            LegalitiesQLAResponse splitQLAResponse = null;
                            try
                            {
                                //var legalitysvc = legalityServiceFactory.GetQLAService(ccsQlaEndPoint);
                                if (splitQLARequest != null)
                                {
                                    //var res = legalitysvc.ValidateLegalitySync(splitQLARequest);
                                    var res = _legalityServiceProvider.ValidateLegality(splitQLARequest, ccsQlaEndPoint).Result;
                                    splitQLAResponse = res;
                                    splitQLAResponse.RawRequest = res.RawRequest;
                                    splitQLAResponse.RawResponse = res.RawResponse;
                                    res.RawRequest = null;
                                    res.RawResponse = null;
                                }
                                //splitQLAResponse = objLegalitiesService.ValidateLegalitySync(splitQLARequest);
                                phaseQLAResponse.employeeResponses.AddRange(splitQLAResponse.employeeResponses);
                                if (splitQLAResponse.errorMessages != null)
                                {
                                    phaseQLAResponse.errorMessages.AddRange(splitQLAResponse.errorMessages);
                                }
                                //_interpretiveDataProvider.SaveQLAMessage(runId, (long)legalityPhase, splitQLAResponse.RawRequest, splitQLAResponse.RawResponse);
                                splitQLAResponse.RawRequest = null;
                                splitQLAResponse.RawResponse = null;
                            }
                            catch (Exception exp)
                            {
                                _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetROTDPhaseQLAResponseForMoreThanCcsMaxRequest"+ "Error occured while calling CCS. Error :  {0}", exp.Message);
                                _loggingProvider.LogInformation("AA.Crew.Legalities.ROTD "+ "RuleOrchestrator.GetROTDPhaseQLAResponseForMoreThanCcsMaxRequest"+ "Request json:  {0}" , LogSanitizer.SerializeSafely(requests.employees));
                                //string rawRequest = JsonConvert.SerializeObject(requests.employees);
                                //rotdInterpretiveRepository.SaveQLAMessage(runId, (long)legalityPhase, rawRequest, exp.Message);
                                throw exp;
                            }
                        }

                        // Reconstruct request
                        var groupedRequestList = lstLegalitiesQLARequest.SelectMany(x => x.employees).ToList().GroupBy(u => u.employeeID).Select(grp => grp.ToList()).Distinct().ToList();

                        phaseQLARequest.employees = new List<Reserves.QLA.Model.Request.Legalities.Employee>();

                        foreach (var employeeRequest in groupedRequestList)
                        {
                            empDetails = new Reserves.QLA.Model.Request.Legalities.Employee();
                            empDetails.requests = new List<Request>();
                            foreach (var emp in employeeRequest)
                            {
                                empDetails.requests.AddRange(emp.requests);
                            }
                            empDetails.airlineCode = CCSDetails.AA.ToString();
                            empDetails.employeeID = employeeRequest.First().employeeID;
                            phaseQLARequest.employees.Add(empDetails);
                        }

                        LegalitiesQLARequest = phaseQLARequest;

                        if (legalityPhase == ROTDLegalityPhase.Future)
                        {
                            futurePhaseQLARequest = phaseQLARequest;
                        }
                        else if (legalityPhase == ROTDLegalityPhase.ETB)
                        {
                            etbPhaseQLARequest = phaseQLARequest;
                        }
                        else if (legalityPhase == ROTDLegalityPhase.ETB_Future)
                        {
                            etbFuturePhaseQLARequest = phaseQLARequest;
                        }

                        // Reconstruct response
                        var groupedResponseList = phaseQLAResponse.employeeResponses.GroupBy(u => u.employeeID).Select(grp => grp.ToList()).Distinct().ToList();

                        LegalitiesQLAResponse tempLegalitiesQLAResponse = new LegalitiesQLAResponse();
                        tempLegalitiesQLAResponse.employeeResponses = new List<Reserves.QLA.Model.Response.Legalities.Employeerespons>();
                        List<Reserves.QLA.Model.Response.Legalities.Qlarespons> tempQLAResponse = new List<Reserves.QLA.Model.Response.Legalities.Qlarespons>();

                        foreach (var employeeResponses in groupedResponseList)
                        {
                            tempQLAResponse = new List<Reserves.QLA.Model.Response.Legalities.Qlarespons>();
                            foreach (var emp in employeeResponses)
                            {
                                tempQLAResponse.AddRange(emp.qlaResponses);
                            }
                            tempLegalitiesQLAResponse.employeeResponses.Add(new Reserves.QLA.Model.Response.Legalities.Employeerespons
                            {
                                airlineCode = CCSDetails.AA.ToString(),
                                employeeID = employeeResponses.First().employeeID,
                                qlaResponses = tempQLAResponse
                            });
                        }

                        phaseQLAResponse.employeeResponses = tempLegalitiesQLAResponse.employeeResponses;

                        HandleQLAErrors(phaseQLAResponse, (int)legalityPhase);

                    }
                }
                catch (Exception ex)
                {
                    _loggingProvider.LogError("AA.Crew.Legalities.ROTD", "RuleOrchestrator.GetROTDPhaseQLAResponseForMoreThanCcsMaxRequest", "Error occured while calling QLA service. " + ex.GetAllException());
                    throw ex;
                }
            }

            return phaseQLAResponse;
        }

        private void SetROTAPhaseQlaSupportData(int runId, int phaseId, DateTime processingDate)
        {
            futureQlaSupportingData = new List<LegalityQLASupportingData>();
            etbQlaSupportingData = new List<LegalityQLASupportingData>();
            etbFutureQlaSupportingData = new List<LegalityQLASupportingData>();
            lineHolderQlaSupportingData = new List<LegalityQLASupportingData>();
            lineHolderFutureQlaSupportingData = new List<LegalityQLASupportingData>(); 
            lineHolderStandByQlaSupportingData = new List<LegalityQLASupportingData>();
            var qlaSupportingData = _interpretiveDataProvider.GetLegalityQLASupportingData(runId).Result;
            //List<LegalityQLASupportingData> qlaSupportingData = new List<LegalityQLASupportingData>();
            //Filter supporting data for the required phase
            if (phaseId == (int)ROTDLegalityPhase.NonVolunteer)
                qlaSupportingData = qlaSupportingData.Where(x => x.LeaglityPhaseID == (int)ROTDLegalityPhase.NonVolunteer || x.LeaglityPhaseID == (int)ROTDLegalityPhase.NonVolunteerStandBy).ToList();
            else
                qlaSupportingData = qlaSupportingData.Where(x => x.LegalityQLARulesID == (int)ROTDLegalityPhase.IsVolunteer).ToList();

            rapQlaSupportingData = qlaSupportingData.Where(x => x.ActivityType == ActivityTypes.RAP.ToString() && x.AffectedType != null && x.AffectedType.ToUpper() != AffectedTypeConst.RESTRICTED).ToList<LegalityQLASupportingData>();

            var sDate = Convert.ToDateTime(processingDate.ToShortDateString() + " " + "2:00:00 AM");
            var eDate = Convert.ToDateTime(processingDate.AddDays(1).ToShortDateString() + " " + "1:59:00 AM");

            qlaSupportingData = qlaSupportingData.Where(x => (Convert.ToDateTime(x.ActivityReportDateTime) < sDate || Convert.ToDateTime(x.ActivityReportDateTime) > eDate)).ToList<LegalityQLASupportingData>();
            qlaSupportingData = qlaSupportingData.Where(x => (x.ActivityType == null || x.ActivityType != ActivityTypes.RAP.ToString())).ToList<LegalityQLASupportingData>();

            var messageObject = (from qlaData in qlaSupportingData
                                 group qlaData by new { qlaData.EmployeeId, qlaData.PickupActivityId } into grp
                                 orderby grp.Key.EmployeeId
                                 select new { EmployeeID = grp.Key.EmployeeId, PickupActivityId = grp.Key.PickupActivityId }).Distinct().ToList();

            foreach (var item in messageObject)
            {
                var qlaData = qlaSupportingData.Where(x => x.EmployeeId == item.EmployeeID && x.PickupActivityId == item.PickupActivityId && x.AffectedType != null && (x.AffectedType.ToUpper() == AffectedTypeConst.LINEHOLDER || x.AffectedType.ToUpper() == AffectedTypeConst.FUTURE || x.AffectedType.ToUpper() == AffectedTypeConst.ETB)).ToList();

                var futureQlaData = qlaData.Where(x => x.AffectedType.ToUpper() == AffectedTypeConst.FUTURE && x.PickupActivityType.ToUpper() == ActivityTypes.SEQ.ToString()).ToList();
                var etbQlaData = qlaData.Where(x => x.AffectedType.ToUpper() == AffectedTypeConst.ETB && x.PickupActivityType.ToUpper() == ActivityTypes.SEQ.ToString()).ToList();
                var lineHolderQlaData = qlaData.Where(x => x.AffectedType.ToUpper() == AffectedTypeConst.LINEHOLDER).ToList();

                // Prepare Lineholder Standby phase QLA supporting data
                var data = lineHolderQlaData.Where(x => x.PickupActivityType.ToUpper() == ActivityTypes.STB.ToString()).ToList();
                // Filter standby if afftected by both Lineholder  and Future/ETB        
                data = data.Where(x => !qlaData.Any(y => y.PickupActivityId == x.PickupActivityId && (y.AffectedType.ToUpper() == AffectedTypeConst.FUTURE || y.AffectedType.ToUpper() == AffectedTypeConst.ETB))).ToList();
                if (data != null && data.Count > 0)
                    lineHolderStandByQlaSupportingData.AddRange(data);

                // filter only sequence 
                lineHolderQlaData = lineHolderQlaData.Where(x => x.PickupActivityType.ToUpper() == ActivityTypes.SEQ.ToString()).ToList();

                // Prepare ETB/Future phase QLA supporting data
                if (etbQlaData.Count > 0 && futureQlaData.Count > 0)
                {
                    etbFutureQlaSupportingData.AddRange(futureQlaData);
                    var FutEtb = etbQlaData.Where(x => etbFutureQlaSupportingData.Any(y => y.PickupActivityId == x.PickupActivityId)).ToList();
                    etbFutureQlaSupportingData.AddRange(FutEtb);
                }

                // Prepare Lineholder/Future phase QLA supporting data
                data = qlaData.Where(x => lineHolderQlaData.Any(y => y.PickupActivityId == x.PickupActivityId && x.AffectedType.ToUpper() == AffectedTypeConst.FUTURE)).ToList();
                if (data != null && data.Count > 0)
                {
                    lineHolderFutureQlaSupportingData.AddRange(data);
                    var FutLh = lineHolderQlaData.Where(x => lineHolderFutureQlaSupportingData.Any(y => y.PickupActivityId == x.PickupActivityId)).ToList();
                    lineHolderFutureQlaSupportingData.AddRange(FutLh);
                }


                // Prepare Future phase QLA supporting data
                if (futureQlaData != null && futureQlaData.Count > 0)
                    futureQlaSupportingData.AddRange(futureQlaData);

                // Prepare ETB phase QLA supporting data
                etbQlaData = etbQlaData.Where(x => !qlaData.Any(y => y.PickupActivityId == x.PickupActivityId && (y.AffectedType.ToUpper() == AffectedTypeConst.FUTURE || y.AffectedType.ToUpper() == AffectedTypeConst.LINEHOLDER))).ToList();
                if (etbQlaData != null && etbQlaData.Count > 0)
                    etbQlaSupportingData.AddRange(etbQlaData);

                // Prepare Lineholder phase QLA supporting data
                lineHolderQlaData = lineHolderQlaData.Where(x => !qlaData.Any(y => y.PickupActivityId == x.PickupActivityId && (y.AffectedType.ToUpper() == AffectedTypeConst.ETB || y.AffectedType.ToUpper() == AffectedTypeConst.FUTURE))).ToList();
                if (lineHolderQlaData != null && lineHolderQlaData.Count > 0)
                    lineHolderQlaSupportingData.AddRange(lineHolderQlaData);
            }

        }

        #endregion
    }
}
