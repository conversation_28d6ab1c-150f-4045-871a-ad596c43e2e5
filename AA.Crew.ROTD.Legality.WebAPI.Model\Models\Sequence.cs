using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class Sequence
    {
        public Sequence()
        {
            this.SequencePositionDetails = new HashSet<SequencePositionDetail>();
        }
    
        public long SequenceID { get; set; }
        public long RunID { get; set; }
        public Nullable<int> SequenceNumber { get; set; }
    
        public virtual Run Run { get; set; }
        public virtual ICollection<SequencePositionDetail> SequencePositionDetails { get; set; }
    }
}
