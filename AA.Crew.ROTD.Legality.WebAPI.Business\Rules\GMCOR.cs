﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    public class GMCOR : IROTDRules
    {
        public string RuleName { get; set; }

        private List<string> retRuleName;
        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faActivity, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate baseDate, ref bool possibleIsLegal, ref string rapCode, ref int IsCurrentRAP, List<QLARuleResult> qlaRuleResult, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleName = new List<string>();
            bool flag = true;

            if (sequence != null)
            {
                if ((sequence.SatelliteStation || !IsBaseCoTerminal))
                {
                    if (baseDate.CurrentSysTime <= sequence.SequenceStartDateTime.Add(TimeSpan.FromHours(-2)))
                    {
                        flag = false;
                    }
                }
                else if (IsBaseCoTerminal)
                {
                    if (baseDate.CurrentSysTime <= sequence.SequenceStartDateTime.Add(TimeSpan.FromHours(-3)))
                    {
                        flag = false;
                    }
                }
            }
            else if (standby != null)
            {
                if (IsBaseCoTerminal)
                {
                    if (baseDate.CurrentSysTime <= standby.ReportTime.Add(TimeSpan.FromHours(-3)))
                    {
                        flag = false;
                    }
                }
                else
                {
                    if (baseDate.CurrentSysTime <= standby.ReportTime.Add(TimeSpan.FromHours(-2)))
                    {
                        flag = false;
                    }
                }
            }
            if (!flag)
            {
                possibleIsLegal = false;
                retRuleName.Add(ruleName);
            }
            return retRuleName;
        }

    }
}
