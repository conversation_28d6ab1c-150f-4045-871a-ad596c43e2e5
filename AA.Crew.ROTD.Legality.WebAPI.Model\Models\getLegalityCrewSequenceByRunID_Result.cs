using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityCrewSequenceByRunID_Result
    {
        public long ReservesCrewSequenceLegalityID { get; set; }
        public long ReservesCrewMemberID { get; set; }
        public long RunID { get; set; }
        public Nullable<long> SequencePositionDetailsID { get; set; }
        public Nullable<int> StandByID { get; set; }
        public Nullable<bool> IsOver35By7 { get; set; }
    }
}
