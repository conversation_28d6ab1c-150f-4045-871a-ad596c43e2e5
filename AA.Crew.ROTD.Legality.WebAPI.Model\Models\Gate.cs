using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class Gate
    {
        public Gate()
        {
            this.Standbies = new HashSet<Standby>();
        }
    
        public int GateID { get; set; }
        public string GateName { get; set; }
        public int BaseID { get; set; }
        public bool IsActive { get; set; }
        public string BaseTerminalName { get; set; }
    
        public virtual Base Base { get; set; }
        public virtual ICollection<Standby> Standbies { get; set; }
    }
}
