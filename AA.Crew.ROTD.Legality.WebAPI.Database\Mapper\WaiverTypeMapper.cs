using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    //public class WaiverTypeMapper : MapperBase<List<WaiverTypeDTO>, List<WaiverType>>
    //{
    //    public override List<WaiverType> Map(List<WaiverTypeDTO> waiverTypeDtoList)
    //    {
    //        try
    //        {
    //            return waiverTypeDtoList.Select(waiverTypeDto => new WaiverType
    //            {
    //                WaiverTypeID = waiverTypeDto.WaiverTypeID,
    //                WaiverTypeDescription = waiverTypeDto.WaiverTypeDescription,
    //                IsActive = waiverTypeDto.IsActive,


    //            }).ToList();
    //        }
    //        catch (Exception)
    //        {
    //            throw;
    //        }
    //    }

    //    public override List<WaiverTypeDTO> Map(List<WaiverType> element)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
}
