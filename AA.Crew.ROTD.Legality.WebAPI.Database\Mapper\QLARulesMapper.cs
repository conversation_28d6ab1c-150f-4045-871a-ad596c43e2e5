using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class QLARulesMapper : MapperBase<List<QLARulesDTO>, List<QLARules>>
    {
        public override List<QLARules> Map(List<QLARulesDTO> qlaRulesDtoList)
        {
            try
            {
                return qlaRulesDtoList.Select(qlaRulesDto => new QLARules
                {
                    QLARuleID = qlaRulesDto.QLARuleID,
                    RuleName = qlaRulesDto.QLARule,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<QLARulesDTO> Map(List<QLARules> element)
        {
            throw new NotImplementedException();
        }
    }
}
