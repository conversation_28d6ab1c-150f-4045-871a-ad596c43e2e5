﻿using AA.Crew.ROTD.Legality.WebAPI.Model.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class LegalitiesQLAResponse : ResponseBase
    {
        public string RawRequest { get; set; }
        public string RawResponse { get; set; }
        public List<string> errorMessages { get; set; }
        public List<Employeerespons> employeeResponses { get; set; }
    }
    public class Employeerespons
    {
        public string airlineCode { get; set; }
        public int employeeID { get; set; }
        public List<string> errors { get; set; }
        public List<Qlarespons> qlaResponses { get; set; }
    }

    public class Qlarespons
    {
        public List<string> errorMessages { get; set; }
        public bool isContractual { get; set; }
        public bool isLegal { get; set; }
        public bool isQualified { get; set; }
        public int requestId { get; set; }
        public List<Ruleresult> ruleResults { get; set; }
        public bool valid { get; set; }
        public List<ActionsToDo> actionsToDo { set; get; }
    }

    public class ActionsToDo
    {        
        public string action { get; set; }
        public string contractMonth { get; set; }
        public string startDate{ get; set; }
        public string endDate { get; set; }
        public string endTime { get; set; }     
        public string startTime { get; set; }   
        public string type { get; set; }
        public string code { get; set; }
        public string groupCode { get; set; }
    }

    public class Pickup
    {
        public string airlineCode { get; set; }
        public string contractMonth { get; set; }
        public int activityId { get; set; }
        public string activityCode { get; set; }
        public string activityType { get; set; }
        public string activityOriginationDate { get; set; }
        public string positionCode { get; set; }
        public string startDateTime { get; set; }
        public string endDateTime { get; set; }
    }

    public class AffectedBy
    {
        public string airlineCode { get; set; }
        public string contractMonth { get; set; }
        public int activityId { get; set; }
        public string activityCode { get; set; }
        public string activityType { get; set; }
        public string activityOriginationDate { get; set; }
        public string positionCode { get; set; }
        public string startDateTime { get; set; }
        public string endDateTime { get; set; }
    }

    public class MessageObjects
    {
        public List<Pickup> pickups { get; set; }       
        public AffectedBy affectedBy { get; set; }
        public string affectedType { get; set; }
        public Nullable<DateTime> affectedStartDateTime { get; set; }
    }

    public class Ruleresult
    {
        public string rule { get; set; }
        public string result { get; set; }
        public List<string> messages { get; set; }
        public MessageObjects messageObjects { get; set; }
    }
   
}
