﻿
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests
{
    public static class Parameters
    {
        private static bool IsInitialized = false;
        public static string Environment;

        public static string TestDataPath;
        public static string TestResultsPath;

        /// <summary>
        /// Auto wire all RunSettings Parameters from .runsettings file
        /// </summary>
        /// <param name="testContext"></param>
        /// <param name="testDataContext"></param>
        public static void InitializeParameters(TestContext testContext)
        {
            if (IsInitialized)
                return;

            Dictionary<string, string> systemInfo = new Dictionary<string, string>();

            FieldInfo[] fields = typeof(Parameters).GetFields(BindingFlags.Static | BindingFlags.Public);

            foreach (var fi in fields)
            {
                var propValue = testContext.Properties[fi.Name];

                if (fi.Name == "Environment")
                {
                    //Ensure that RUNSETTINGS file is Selected
                    Assert.IsNotNull(propValue, $"\n\nRUNSETTINGS FILE IS NOT SELECTED.\n Please choose any one .runsettings file.\n");
                }
                else
                {  //Ensure that the key is present in RUNSETTINGS file
                    Assert.IsNotNull(propValue, $"\n\nRUNSETTINGS Key '{fi.Name}' is missing in '{Environment}.runsettings' file.\n");
                }



                // Bind value to property
                fi.SetValue(fi.Name, propValue);

                // Add RunSettings Info to Extent Report 
                if (!fi.Name.ToLower().Contains("password"))
                {
                    systemInfo.Add(fi.Name, Convert.ToString(propValue));
                }

            }


            //SqlHelper.connectionString = dbConnectionString;
            //SqlHelper.commandTimeout = Convert.ToInt32(sqlCommandTimeout);
            InitializeReportsVariables();
            IsInitialized = true;
        }

        /// <summary>
        /// Populate the parameters required to run the test cases, earlier it was from run settings. Now, its from app.config
        /// </summary>
        public static void InitializeReportsVariables()
        {
            if (TestDataPath == null)
            {
                TestDataPath = ConfigFactory.GetConfigurationSection("Values")["RPVTestDataPath"];
            }
            if (TestResultsPath == null)
            {
                TestResultsPath = ConfigFactory.GetConfigurationSection("Values")["RPVTestResultsPath"];
            }
        }

    }
}
