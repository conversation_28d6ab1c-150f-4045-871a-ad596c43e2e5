﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AA.Crew.ConfigurationProvider.Interface" Version="5.22.11012.101" />
    <PackageReference Include="AA.Crew.ConfigurationProvider.Json" Version="5.22.11012.101" />
    <PackageReference Include="AA.Crew.Reserves.TimeConversion" Version="1.22.10421.103" />
    <PackageReference Include="AA.Crew.ROMS.LogHelper" Version="1.24.11112.102" />
    <PackageReference Include="AA.Crew.ROMS.Model" Version="1.23.11213.101" />
    <PackageReference Include="AA.Crew.WebClient" Version="5.22.11012.101" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Rest.ClientRuntime" Version="3.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Database\AA.Crew.ROTD.Legality.WebAPI.Database.csproj" />
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Model\AA.Crew.ROTD.Legality.WebAPI.Model.csproj" />
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Service\AA.Crew.ROTD.Legality.WebAPI.Service.csproj" />
  </ItemGroup>

</Project>
