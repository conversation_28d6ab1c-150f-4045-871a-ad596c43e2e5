﻿<?xml version="1.0" encoding="UTF-8"?>
<extentreports>
  <configuration>
    <!-- report theme -->
    <!-- standard, dark -->
    <theme>standard</theme>

    <!-- enables timeline -->
    <!-- defaults to true -->
    <enableTimeline>true</enableTimeline>

    <!-- document encoding -->
    <!-- defaults to UTF-8 -->
    <encoding>UTF-8</encoding>

    <!-- protocol for script and stylesheets -->
    <!-- defaults to https -->
    <protocol>https</protocol>

    <!-- title of the document -->
    <documentTitle>ROMS - Automation Test Results</documentTitle>

    <!-- report name - displayed at top-nav -->
    <reportName>ROMS Regression Tests</reportName>

    <!-- custom javascript -->
    <scripts>
      <![CDATA[
                $(document).ready(function() {
                  $(".text-debug").parent().parent().toggleClass("text-debug");
                  $(".text-warning").parent().parent().toggleClass("text-warning");
                  
                });
            ]]>
    </scripts>

    <!-- custom styles -->
    <styles>
      <![CDATA[
                img.r-img { display: block; width: 33%; -webkit-box-shadow: 0px 0px 5px 3px rgba(166,166,166,1);  margin: 10px 0px 5px 0;}
                .fail { background-color : #FF6347 }
                .pass {  }
                .error { color : Red }
                tr :has(.text-warning) { background-color  : yellow; }
                .ps-container {overflow: auto !important;}
            ]]>
    </styles>
  </configuration>
</extentreports>
