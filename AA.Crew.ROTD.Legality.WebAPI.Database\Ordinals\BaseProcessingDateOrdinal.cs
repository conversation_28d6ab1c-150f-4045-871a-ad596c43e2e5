using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct BaseProcessingDateOrdinal
    {
        /* Oridinal variables */

        internal Int32 BaseCD;
        internal Int32 ProcessingDate;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.BaseCD = sqlDataReader.GetOrdinal("BaseCD");
            this.ProcessingDate = sqlDataReader.GetOrdinal("ProcessingDate");


            this.Initialized = true;
        }
    }
}
