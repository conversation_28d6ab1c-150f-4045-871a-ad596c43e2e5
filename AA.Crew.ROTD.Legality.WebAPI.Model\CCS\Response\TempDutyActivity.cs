﻿using Newtonsoft.Json;
using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class TempDutyActivity
    {

        /// <summary>
        /// Activity start date
        /// </summary>
        [JsonConverter(typeof(JsonDateConverter))]
        private DateTime startDate;

        /// <summary>
        /// Activity end date
        /// </summary>
        [JsonConverter(typeof(JsonDateConverter))]
        private DateTime endDate;

        /// <summary>
        /// Indicates crew base
        /// </summary>
        private string crewBase;

        /// <summary>
        /// "ContractMonth to which this Actvity belongs to
        /// </summary>
        private string contractMonth;

        /// <summary>
        /// bool flag to indicate whether this Temporary Duty is International or Domestic
        /// example = "true,false"
        /// </summary>
        private bool internationalTDY;

    }
}
