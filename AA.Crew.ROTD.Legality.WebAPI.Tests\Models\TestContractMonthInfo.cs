﻿using AA.Crew.ROTD.Legality.WebAPI.Model.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests.Models
{
    public class TestContractMonth
    {
        public string contractMonth { get; set; }
        public string contractMonthType { get; set; }
        public string startDate { get; set; }
        public string endDate { get; set; }
        public string selectionType { get; set; }
        public int iMax { get; set; }
        public int vMax { get; set; }
        public string currentBase { get; set; }
        public string fleetCode { get; set; }
    }

    public class TestContractMonthInfo
    {
        public List<TestContractMonth> contractMonths { get; set; }
    }
}
