﻿using Newtonsoft.Json;
using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class ServiceQuals
    {
        private static long serialVersionUID = -4983155938794102452L;

        public int number { get; set; }

        public string description { get; set; }

        [Json<PERSON>onverter(typeof(JsonDateConverter))]
        public DateTime initDomDate { get; set; }

        [JsonConverter(typeof(JsonDateConverter))]
        public DateTime? initIntlDate { get; set; }

        [Json<PERSON>onverter(typeof(JsonDateConverter))]
        public DateTime recurDomDate { get; set; }

        [J<PERSON><PERSON>onverter(typeof(JsonDateConverter))]
        public DateTime? recurIntlDate { get; set; }
    }
}
