﻿using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class CrewRap
    {

        private static long serialVersionUID = 4947943304211881661L;

        /// <summary>
        /// Rolling contract month duration to determine each month data
        /// allowableValues = "MMMYYYY", 
        /// example = "MAR2017"
        /// </summary>
        private string contractMonth;

        /// <summary>
        /// ROTA RAP shift Type
        /// example = "A/B/C/D"
        /// </summary>
        private string rapShift;

        /// <summary>
        /// rap start date and time
        /// </summary>
        private DateTime startDateTime;

        /// <summary>
        /// rap end date and time
        /// </summary>
        private DateTime endDateTime;

        /// <summary>
        /// Rap start date and time in GMT
        /// </summary>
        private DateTime startDateTimeInGMT;

        /// <summary>
        /// Rap end date and time in GMT
        /// </summary>
        private DateTime endDateTimeInGMT;

        public string getContractMonth()
        {
            return contractMonth;
        }

        public void setContractMonth(string contractMonth)
        {
            this.contractMonth = contractMonth;
        }

        public DateTime getStartDateTime()
        {
            return startDateTime;
        }

        public void setStartDateTime(DateTime startDateTime)
        {
            this.startDateTime = startDateTime;
        }

        public DateTime getEndDateTime()
        {
            return endDateTime;
        }

        public void setEndDateTime(DateTime endDateTime)
        {
            this.endDateTime = endDateTime;
        }

        public string getRapShift()
        {
            return rapShift;
        }

        public void setRapShift(string rapShift)
        {
            this.rapShift = rapShift;
        }

        public DateTime getStartDateTimeInGMT()
        {
            return startDateTimeInGMT;
        }

        public void setStartDateTimeInGMT(DateTime startDateTimeInGMT)
        {
            this.startDateTimeInGMT = startDateTimeInGMT;
        }

        public DateTime getEndDateTimeInGMT()
        {
            return endDateTimeInGMT;
        }

        public void setEndDateTimeInGMT(DateTime endDateTimeInGMT)
        {
            this.endDateTimeInGMT = endDateTimeInGMT;
        }

    }
}
