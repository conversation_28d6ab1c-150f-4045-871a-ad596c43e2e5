using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityCrewMemberActivityOrdinal
    {
        /* Oridinal variables */

        internal Int32 ReservesCrewMemberActivityID;
        internal Int32 ReservesCrewMemberActivityTypeID;
        internal Int32 ActivityCode;
        internal Int32 StartDateTime;
        internal Int32 EndDateTime;
        internal Int32 HomeBaseFAReducedRestEndTime;
        internal Int32 HomeBaseRestEndTime;
        internal Int32 DurationInDays;
        internal Int32 ActivityType;
        internal Int32 ReservesCrewMemberID;
        internal Int32 EmployeeID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.ReservesCrewMemberActivityID = sqlDataReader.GetOrdinal("ReservesCrewMemberActivityID");
            this.ReservesCrewMemberActivityTypeID = sqlDataReader.GetOrdinal("ReservesCrewMemberActivityTypeID");
            this.ActivityCode = sqlDataReader.GetOrdinal("ActivityCode");
            this.StartDateTime = sqlDataReader.GetOrdinal("StartDateTime");
            this.EndDateTime = sqlDataReader.GetOrdinal("EndDateTime");
            this.HomeBaseFAReducedRestEndTime = sqlDataReader.GetOrdinal("HomeBaseFAReducedRestEndTime");
            this.HomeBaseRestEndTime = sqlDataReader.GetOrdinal("HomeBaseRestEndTime");
            this.DurationInDays = sqlDataReader.GetOrdinal("DurationInDays");
            this.ActivityType = sqlDataReader.GetOrdinal("ActivityType");
            this.ReservesCrewMemberID = sqlDataReader.GetOrdinal("ReservesCrewMemberID");
            this.EmployeeID = sqlDataReader.GetOrdinal("EmployeeID");


            this.Initialized = true;
        }
    }
}
