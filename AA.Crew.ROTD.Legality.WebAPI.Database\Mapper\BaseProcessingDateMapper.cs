using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class BaseProcessingDateMapper : MapperBase<BaseProcessingDateDTO, getLegalityBaseProcessingDateByRunID_Result>
    {
        public override getLegalityBaseProcessingDateByRunID_Result Map(BaseProcessingDateDTO baseProcessingDateDto)
        {
            try
            {
                return new getLegalityBaseProcessingDateByRunID_Result
                {
                    BaseCD = baseProcessingDateDto.BaseCD,
                    ProcessingDate = baseProcessingDateDto.ProcessingDate
                };
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override BaseProcessingDateDTO Map(getLegalityBaseProcessingDateByRunID_Result element)
        {
            throw new NotImplementedException();
        }
    }
}
