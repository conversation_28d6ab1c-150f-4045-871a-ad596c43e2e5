using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityProcessBidCrewWaiverMapper : MapperBase<List<LegalityProcessBidCrewWaiverDTO>, List<BidCrewWaiver>>
    {
        public override List<BidCrewWaiver> Map(List<LegalityProcessBidCrewWaiverDTO> legalityProcessBidCrewWaiverDtoList)
        {
            try
            {
                return legalityProcessBidCrewWaiverDtoList.Select(legalityProcessBidCrewWaiverDto => new BidCrewWaiver
                {
                    BidCrewWaiverId = legalityProcessBidCrewWaiverDto.processBidCrewWaiverID,
                    BidTypeId = legalityProcessBidCrewWaiverDto.BidTypeID,
                    CrewMemberId = legalityProcessBidCrewWaiverDto.CrewMemberID,
                    IsActive = legalityProcessBidCrewWaiverDto.WTActive.Value,
                    StartDate = legalityProcessBidCrewWaiverDto.CreateDate,
                    WaiverTypeDescription = legalityProcessBidCrewWaiverDto.WaiverTypeDescription,
                    WaiverTypeID = legalityProcessBidCrewWaiverDto.WaiverTypeID,
                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityProcessBidCrewWaiverDTO> Map(List<BidCrewWaiver> element)
        {
            throw new NotImplementedException();
        }
    }
}
