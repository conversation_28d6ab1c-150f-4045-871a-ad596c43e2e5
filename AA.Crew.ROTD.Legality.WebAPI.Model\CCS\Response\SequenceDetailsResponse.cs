﻿using AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Common;
using AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Sequence;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class SequenceDetailsResponse
    {

        /// <summary>
        /// Two Letter code defines the airline carrier",
        /// example = "AA,MQ"
        /// </summary>
        public string airlineCode { get; set; }

        /// <summary>
        /// Three character IATA airport code which denotes the _base airport from which the sequence originates.",
        /// allowableValues = "{A-Z}",  example = "DFW,JFK"
        /// </summary>
        public string @base { get; set; }

        /// <summary>
        /// Rolling contract month duration to determine each month data",
        /// allowableValues = "MMMYYYY", 
        /// example = "MAR2017"
        /// </summary>
        public string contractMonth { get; set; }

        /// <summary>
        /// Origin date of the sequence
        /// </summary>
        [JsonConverter(typeof(JsonDateConverter))]
        public DateTime sequenceOriginDate { get; set; }

        /// <summary>
        /// Unique identifier to determine each sequence"
        /// </summary>
        public int? sequenceNumber { get; set; }

        /// <summary>
        /// Position/Seat of CrewMember for the given Sequence
        /// </summary>
        public string positionCode { get; set; }

        /// <summary>
        /// Division denotes either sequence is Domestic or International
        /// </summary>
        public string division { get; set; }

        /// <summary>
        /// Employee Number
        /// </summary>
        public int employeeID { get; set; }

        /// <summary>
        /// Sequence Duration in Days
        /// </summary>
        public int durationInDays { get; set; }

        /// <summary>
        ///  Start Date and Time for the given Sequence
        /// </summary>
        public CCSDateTime sequenceStartDateTime { get; set; }

        /// <summary>
        /// End Date and Time for the given Sequence
        /// </summary>
        public CCSDateTime sequenceEndDateTime { get; set; }

        /// <summary>
        /// signIn Time for the given Sequence
        /// </summary>
        public DateTime signInDateTime { get; set; }

        /// <summary>
        /// debrief Time for the given Sequence
        /// </summary>
        public DateTime debriefDateTime { get; set; }

        /// <summary>
        /// Provides the count of legs for each duty period
        /// example = "1-1/2
        /// </summary>
        public string legsPerDutyPeriod { get; set; }

        /// <summary>
        /// Provides the count of legs for all duty period
        /// </summary>
        public int numberOfLegs { get; set; }

        /// <summary>
        /// Start Date and Time of the Departure airport of the First Leg
        /// </summary>
        public CCSDateTime firstLegDepartureTime { get; set; }

        /// <summary>
        /// Departure Airport code of the First leg in sequence
        /// </summary>
        public string firstLegDepartureAirport { get; set; }

        /// <summary>
        /// Provides SignIn Time in GMT
        /// </summary>
        public DateTime signInTimeInGMT { get; set; }

        /// <summary>
        /// Provides SignIn Time in SequenceBase Time Zone
        /// </summary>
        public DateTime signInTimeBase { get; set; }

        /// <summary>
        /// All stopover airports for the given sequence <br>"
        ///Regular RON (RON duration < 24 Hours). Example: RDU-ORD-STL- <br>"
        ///Long RON (RON duration >= 24 Hours). Example: RDU/ORD/STL <br>"
        ///Turn City (1 Duty Period, No RON). Example: (ORD) <br>"
        ///<b>Sequence Position with 1 to N Duty Periods</b> <br>"
        ///- Identify arrival airport (RON) of last active leg of all duty period except the last duty period <br>"
        ///- If layover time at RON city is greater than or equal to 24 hours, then add a forward '/' "
        ///after RON city in <b>layoverAirport</b> "
        ///data element to indicate it’s a long layover."
        ///- If layover time is less than 24 hours, then add a hyphen '-' after RON city in <b>layoverAirport</b> data element. "
        ///<b>Sequence Position with 1 Duty Period/no over night</b> <br>"
        ///- Number of duty periods is 1 and number of legs is 2 <br>"
        ///- Duty Period’s first leg arrival airport EQUALS to duty periods last leg departure airport<br>"
        ///- Enclose <b>layOverAirport</b> data element in service response with bracket '(' indicating this is not an overnight. 
        /// </summary>
        public string layoverStations { get; set; }

        /// <summary>
        /// List of RON cities, ronCity = All stopover airports for the given sequence
        /// </summary>
        public List<string> ronCities { get; set; }

        /// <summary>
        /// Hex value with bits representing division requirements for this sequence position.
        /// </summary>
        public string landingQuals { get; set; }

        /// <summary>
        ///Division Qualification requirements for this sequence position
        /// /// </summary>
        public List<string> divisionRequirements { get; set; }

        /// <summary>
        ///Denotes the equipment group of an aircraft
        /// </summary>
        public string equipmentGroup { get; set; }

        /// <summary>
        ///Total credit that will be provided as part of flying this sequence in the current month.
        /// </summary>
        public int creditThisMonth { get; set; }

        /// <summary>
        ///Total credit that will be provided as part of flying this sequence in the next month.
        /// </summary>
        public int creditNextMonth { get; set; }

        /// <summary>
        ///Pay Credit for scheduled flight in minutes
        /// </summary>
        public int scheduledFlight { get; set; }

        /// <summary>
        ///Position Info for first sequence 
        /// </summary>
        public string positionSequenceInfo1 { get; set; }

        /// <summary>
        ///Position Info for first sequence in binary format
        /// </summary>
        public string positionSequenceInfo1Binary { get; set; }

        /// <summary>
        ///Position Info for second sequence
        /// </summary>
        public string positionSequenceInfo2 { get; set; }

        /// <summary>
        ///Two character add code used when a Crew is assigned to a Sequence which gives an
        ///indication about how/why the crew got assigned to this sequence . Add code OT means Crew has been
        ///assigned this Sequence which previously existed in OT (open time) pool. Add code TT means,
        ///Crew got this sequence assigned through Trip Trade
        ///</summary>
        public string addCode { get; set; }

        /// <summary>
        ///Boolean flag to indicate whether StandBy is Assigned or not
        /// </summary>
        public bool assignedStandbyShift { get; set; }

        /// <summary>
        ///Boolean flag to indicate whether StandBy is Awarded or not
        /// </summary>
        public bool awardedStandbyShift { get; set; }

        /// <summary>
        ///Details of the List of flightDutyPeriods
        /// </summary>
        public List<FlightDutyPeriod> flightDutyPeriods { get; set; }

        /// <summary>
        ///Denotes the type of the cabin in the aircraft.
        /// </summary>
        public string cabinType { get; set; }

        /// <summary>
        ///Speaker Qualification and Requirement information at sequence level 
        /// </summary>
        public SpeakerRequirement speakerRequirement { get; set; }

        /// <summary>
        ///Service Qualifications
        /// </summary>
        public List<ServiceQuals> serviceQuals { get; set; }

        /// <summary>
        ///Boolean Indicator when set to TRUE indicates that the sequence fails continuity.
        ////A sequence is termed as failed continuity when the last flight leg of a Sequence doesn't arrive at
        ////the crews _base airport.
        /// </summary>
        public bool failsContinuity { get; set; }


        /// <summary>
        ///Boolean flag to verify galley status, "
        ///Galley : on large planes in an isolated kitchen responsible for all meals on plane
        /// </summary>
        public bool galleyPosition { get; set; }

        /// <summary>
        ///Total filghtLegs available in given sequence
        /// </summary>
        public int totalLegCount { get; set; }

        /// <summary>
        ///Summary of all pay credits and flight leg block times corresponding to a sequence 
        /// </summary>
        public PayCredit sequencePayCredit { get; set; }

        /// <summary>
        ///Total Block(flying) time at sequence level, Not yet implemented
        /// </summary>
        public int sequenceBlockTime { get; set; }

        /// <summary>
        ///Pay time at sequence level, Not yet implemented
        /// </summary>
        public int sequencePayTime { get; set; }

        /// <summary>
        ///Duty time at sequence level, Not yet implemented
        /// </summary>
        public string sequenceDutyTime { get; set; }

        /// <summary>
        ///Boolean flag to check whether sequence is International or domestic
        /// </summary>
        public bool international { get; set; }

        /// <summary>
        ///Number of minutes crewMember is away from _base station 
        /// </summary>
        public int timeAwayFromBase { get; set; }

        /// <summary>
        //Boolean flag to query to whether _base is Satellite, Satellite :A satellite terminal is a building
        ///detached from other airport buildings, so that aircraft can park around its entire circumference
        /// </summary>
        public bool isSatellite { get; set; }

        /// <summary>
        ///Boolean flag to query  either _base is CoTerminal,
        ///CoTerminal :Airport adjacent to another Airport 
        /// </summary>
        public bool isCoterminal { get; set; }

        /// <summary>
        ///Boolean flag to indicate a sequence is IPD: International Premium Destination
        /// </summary>
        public bool isIPD { get; set; }

        /// <summary>
        ///Boolean flag to indicate a sequence is NIPD : Non-International Premium Destination. 
        /// </summary>
        public bool isNIPD { get; set; }

        /// <summary>
        /// Boolean flag to query ODAN Sequences,ODAN : sequences consist of a single,
        /// stand-alone duty period which shall not be incorporated with any other duty period.
        /// </summary>
        public bool isODAN { get; set; }

        /// <summary>
        ///Boolean flag to query RedEye Sequences, RedEye :A Red-Eye duty period shall
        /// + " have no more than 2 scheduledflight segments and no more than one (1) scheduled aircraft connection
        /// </summary>
        public bool isRedEye { get; set; }

        /// <summary>
        /// Boolean flag to query RedFlag Sequences, RedFlag:An Open Sequence originating during
        /// the crewmember’s,standby assignment then that sequence is known as as a Red Flag Sequence
        /// </summary>
        public bool isRedFlag { get; set; }

        /// <summary>
        ///Boolean flag to indicate either sequence is training 
        /// </summary>
        public bool isTrainingSequence { get; set; }

        /// <summary>
        ///Boolean flag to verify deadHead status at firstLeg level
        /// </summary>
        public bool firstLegDeadHeadIndicator { get; set; }

        /// <summary>
        ///Boolean flag to verify deadHead status at lastLeg level
        /// </summary>
        public bool lastLegDeadHeadIndicator { get; set; }

        /// <summary>
        ///Destination Airport Code
        /// </summary>
        public string lastLegArrivalAirport { get; set; }

        /// <summary>
        ///Arrival date and time of the last leg of the last duty period in the sequence
        /// </summary>
        public CCSDateTime lastLegArrivalTime { get; set; }

        /// <summary>
        ///Time when the position is opened
        /// </summary>
        public DateTime timeOpened { get; set; }

        /// <summary>
        // Number of open positions corresponding to sequence. This count includes both speaker and non-speaker positions.
        /// </summary>
        public int totalOpenPositionsForSeq { get; set; }

        /// <summary>
        ///Number of open positions corresponding to cabin. This count includes both speaker and non-speaker positions
        /// </summary>
        public int totalOpenPositionsForCabin { get; set; }

        /// <summary>
        /// Two character removal code used when a Crew is removed from a Sequence which gives an
        /// indication about how/why the crew got removed to this sequence . Removal code OT means Crew has been
        /// removed from the Sequence and the sequence will be maintained in OT (open time) pool. Removal code
        /// TT means Crew is dropping this Sequence for the purpose of Trip Trade 
        /// </summary>
        public string removalCode { get; set; }

        /// <summary>
        ///Boolean flag to verify either sequence requires multiple equipments
        /// </summary>
        public bool multipleEquipments { get; set; }

        /// <summary>
        ///Boolean flag to verify deadHead status 
        /// </summary>
        public bool noShowDHD { get; set; }

        /// <summary>
        ///Boolean flag to verify certain sequence positions corresponds to student Flight Attendants  |
        /// </summary>
        public bool observationTrip { get; set; }

        /// <summary>
        ///Boolean flag to indicate if this sequence position is inhibited for Trip Trade or not
        /// </summary>
        public bool ttInhibited { get; set; }

        /// <summary>
        ///Boolean flag to indicate if this sequence position is purser or not
        /// </summary>
        public bool purser { get; set; }

        /// <summary>
        ///Boolean flag to indicate whether this FA/Pilot is qualified to fly this sequence position or Not
        /// </summary>
        public bool notQualified { get; set; }

        /// <summary>
        ///Indicates the language required for the position on that particular open sequence
        /// </summary>
        public string requiredLanguage { get; set; }

        /// <summary>
        ///Boolean flag to indicate if Crew has signed in for this sequence position or not
        /// </summary>
        public bool signedIn { get; set; }

        /// <summary>
        ///Indicates if the sequence is InProgress, Completed or Scheduled
        /// </summary>
        public string sequenceStatus { get; set; }

        /// <summary>
        ///Indicates the service qualification hexadecimal value
        /// </summary>
        public string serviceQualsHex { get; set; }

        /// <summary>
        ///indicates if the sequence is deleted
        /// </summary>
        public bool deleted { get; set; }

        /// <summary>
        ///Indicates if a sequence is blocked by Initial Operation Experience 
        /// </summary>
        public bool isBlockedforIOE { get; set; }

        /// <summary>
        ///Indicates if the sequence is a paper sequence. (Should always be False)
        /// </summary>
        public bool isSuperNumerary { get; set; }

        /// <summary>
        ///Premium Sequence Indicator,indicates if the sequence is premium or not. 0 indicates not premium
        /// </summary>
        public int premiumInd { get; set; }

        /// <summary>
        ///The system or user ID who/which assigned the premium designation
        /// </summary>
        public string premiumAssignedBy { get; set; }

        /// <summary>
        // Provides premium sequence assigned date and time
        /// </summary>
        public DateTime premiumAssignedTime { get; set; }

    }
}
