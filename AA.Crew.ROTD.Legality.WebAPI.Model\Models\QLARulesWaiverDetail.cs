using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class QLARulesWaiverDetail
    {
        public long QLARulesWaiverDetailsID { get; set; }
        public Nullable<int> WaiverTypeID { get; set; }
        public Nullable<long> QLARuleID { get; set; }
    
        public virtual QLARule QLARule { get; set; }
        public virtual WaiverType WaiverType { get; set; }
    }
}
