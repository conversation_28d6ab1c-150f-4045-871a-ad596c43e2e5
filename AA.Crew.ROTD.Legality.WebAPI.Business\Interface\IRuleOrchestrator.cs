﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Interface
{
    public interface IRuleOrchestrator
    {
        //LegalitiesQLARequest LegalitiesQLARequest { get; set; }
        //List<QLARequest> QLARequest { get; set; }
        //List<QLAResponse> LegalityQLAResponse { get; set; }
        //List<QLARuleResult> LegalityRuleResult { get; set; }
        //ROTDContextualRuleSet LegalityRules { get; set; }
        //List<IROTDRules> MapRules(List<Bid> bids, FlightAttendant flightAttendant, int runContextId);
        //Activity IsValidFARAPProcessingDate(FlightAttendant flightAttendant, List<Activity> faActivity, AA.Crew.Legalities.Entities.BaseDate _base, out bool IsValidFARAP);
        //ROTDPhase MapROTDRules(List<AA.Crew.Legalities.Entities.Sequence> sequence, List<Bid> bids, List<FlightAttendant> flightAttendant, List<Activity> faActivity, List<ContractMonth> contractMonthList, List<StandByCredit> standByCreditList, List<BidCrewWaiver> bidCrewWaiver, List<FALanguageDetails> falanguage, bool IsTheProcessInvokedFromDBUI, AA.Crew.Legalities.Entities.BaseDate Base, List<RAPShifts> RAPShift, List<RAPShifts> previousMonthRAPShift, List<StandBy> standby = null);
        //ROTDPhase LoadData(int subPhase, string BaseCD, DateTime ProcessingDate, bool IsTheProcessInvokedFromDBUI,int runContextId);
        //ROTDPhase TestHarnessLoadData(int subPhase, string BaseCD, DateTime ProcessingDate, bool IsTheProcessInvokedFromDBUI, int legalityRunID, int runContextID);
        //LegalitiesQLAResponse GetCrewSequenceAndExecuteCcs(List<FlightAttendant> flightAttendant, List<Entities.Sequence> sequence, List<StandBy> standBy, string inBase, DateTime bidOrginationDate, int runid, int phaseId);
        //void MappingContextualEntites(LegalitiesQLAResponse CCSResponse);
        //LegalitiesQLAResponse GetROTDPhaseQLARequestResponse(int runId, List<Entities.LegalityQLASupportingData> qlaSupportingData, ROTDLegalityPhase legalityPhase, List<Entities.Sequence> sequence, List<StandBy> standBy, List<FlightAttendant> flightAttendants, DateTime processingDate);
        bool ROTDLegalityLoadData(Int32 runId, int runContextId, long awardProgressStepID);
        Task<AppSettings> GetApplicationSetting(string name);
        //bool IsBaseCoTerminal { get; set; }

    }
}
