using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityBidStatusOrdinal
    {
        /* Oridinal variables */

        internal Int32 ReservesCrewMemberBidStatusID;
        internal Int32 CalloutTime;
        internal Int32 MaxCredit;
        internal Int32 ActualPayProjection;
        internal Int32 ReservesCrewMemberID;
        internal Int32 CrewStatusID;
        internal Int32 BidMonthIndicatorID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.ReservesCrewMemberBidStatusID = sqlDataReader.GetOrdinal("ReservesCrewMemberBidStatusID");
            this.CalloutTime = sqlDataReader.GetOrdinal("CalloutTime");
            this.MaxCredit = sqlDataReader.GetOrdinal("MaxCredit");
            this.ActualPayProjection = sqlDataReader.GetOrdinal("ActualPayProjection");
            this.ReservesCrewMemberID = sqlDataReader.GetOrdinal("ReservesCrewMemberID");
            this.CrewStatusID = sqlDataReader.GetOrdinal("CrewStatusID");
            this.BidMonthIndicatorID = sqlDataReader.GetOrdinal("BidMonthIndicatorID");


            this.Initialized = true;
        }
    }
}
