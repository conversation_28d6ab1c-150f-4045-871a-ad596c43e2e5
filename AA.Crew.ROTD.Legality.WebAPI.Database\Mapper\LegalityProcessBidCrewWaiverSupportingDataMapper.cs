using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityProcessBidCrewWaiverSupportingDataMapper : MapperBase<List<LegalityProcessBidCrewWaiverSupportingDataDTO>, List<BidCrewWaiverSupportingData>>
    {
        public override List<BidCrewWaiverSupportingData> Map(List<LegalityProcessBidCrewWaiverSupportingDataDTO> legalityProcessBidCrewWaiverSupportingDataDtoList)
        {
            try
            {
                return legalityProcessBidCrewWaiverSupportingDataDtoList.Select(legalityProcessBidCrewWaiverSupportingDataDto => new BidCrewWaiverSupportingData
                {
                    BidCrewWaiverId = legalityProcessBidCrewWaiverSupportingDataDto.ProcessBidCrewWaiverID,
                    BaseCoTerminal = legalityProcessBidCrewWaiverSupportingDataDto.BaseCoTerminal,
                    TimeToDeparture = legalityProcessBidCrewWaiverSupportingDataDto.TimeToDeparture,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityProcessBidCrewWaiverSupportingDataDTO> Map(List<BidCrewWaiverSupportingData> element)
        {
            throw new NotImplementedException();
        }
    }
}
