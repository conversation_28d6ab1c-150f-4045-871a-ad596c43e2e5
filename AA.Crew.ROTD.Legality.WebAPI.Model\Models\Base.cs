using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class Base
    {
        public Base()
        {
            this.BaseDates = new HashSet<BaseDate>();
            this.Gates = new HashSet<Gate>();
            this.StandByTypes = new HashSet<StandByType>();
        }
    
        public int BaseID { get; set; }
        public string BaseCD { get; set; }
        public string BaseName { get; set; }
        public Nullable<int> TimeZoneID { get; set; }
        public string TimeZone { get; set; }
    
        public virtual ICollection<BaseDate> BaseDates { get; set; }
        public virtual ICollection<Gate> Gates { get; set; }
        public virtual ICollection<StandByType> StandByTypes { get; set; }
    }
}
