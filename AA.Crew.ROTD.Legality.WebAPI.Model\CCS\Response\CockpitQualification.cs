﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class CockpitQualification // implements  
    {

        /// <summary>
        /// Equipment Group for which the pilot is qualified for a contract month
        /// </summary>
        public string equipmentGroup { get; set; }

        /// <summary>
        ///90 days landing expiration date. On the day of operation pilot should have
        ///completed three takeoffs and three landings within preceding 90 days to
        ///be CA/FO eligible for an equipment
        /// </summary>
        [J<PERSON><PERSON>onverter(typeof(JsonDateConverter))]
        public DateTime landingExpirationDate { get; set; }

        /// <summary>
        /// Last flown date as a pilot in an equipment
        /// </summary>
        [JsonConverter(typeof(JsonDateConverter))]
        public DateTime lastFlownDate { get; set; }

        /// <summary>
        /// Current Cockpit Qualification
        /// </summary>
        public CurrentQualification currentQualification { get; set; }

        /// <summary>
        /// Contract Month for which pilot is qualified
        /// allowableValues = "MMMYYYY"
        /// example = "JAN2019"
        /// </summary>
        public string contractMonth { get; set; }

        /// <summary>
        /// List of division qualifications such as US,FE, LA, HI, GUC JAC etc.
        /// </summary>
        public List<string> divisionQuals { get; set; }

        /// <summary>
        //Captains and first officers will not be scheduled together unless either the captain or first officer have accumulated 75 hours
        ///in that position and aircraft type.The FAA may waive this requirement in the event of a new fleet type or a new aircraft type
        ///at a domicile.
        /// </summary>
        public bool restricted75hr { get; set; }

        /// <summary>
        /// Line qualification seat. C-Captain qualified, F-First Officer, E-Flight Engineer
        /// </summary>
        public string position { get; set; }

    }
}
