using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityQLASupportingDataonlyMapper : MapperBase<List<LegalityQLASupportingDataonlyDTO>, List<LegalityQLASupportingData>>
    {
        public override List<LegalityQLASupportingData> Map(List<LegalityQLASupportingDataonlyDTO> legalityQLASupportingDataonlyDtoList)
        {
            try
            {
                return legalityQLASupportingDataonlyDtoList.Select(legalityQLASupportingDataonlyDto => new LegalityQLASupportingData
                {
                    RunId = legalityQLASupportingDataonlyDto.RunId,
                    EmployeeId = legalityQLASupportingDataonlyDto.EmployeeId,
                    AffectedType = legalityQLASupportingDataonlyDto.AffectedType,
                    ContractMonth = legalityQLASupportingDataonlyDto.ContractMonth,
                    ActivityId = legalityQLASupportingDataonlyDto.ActivityId,
                    ActivityCode = legalityQLASupportingDataonlyDto.Activitycode,
                    ActivityType = legalityQLASupportingDataonlyDto.ActivityType,
                    ActivityOriginationDate = legalityQLASupportingDataonlyDto.ActivityOriginationDate,
                    PositionCode = legalityQLASupportingDataonlyDto.PositionCode,
                    PickupContractMonth = legalityQLASupportingDataonlyDto.PickupContractMonth,
                    PickupActivityId = legalityQLASupportingDataonlyDto.PickupActivityId,
                    PickupActivityCode = legalityQLASupportingDataonlyDto.PickupActivityCode,
                    PickupActivityType = legalityQLASupportingDataonlyDto.PickupActivityType,
                    PickupActivityOriginationDate = legalityQLASupportingDataonlyDto.PickupActivityOriginationDate,
                    PickupPositionCode = legalityQLASupportingDataonlyDto.PickupPositionCode,
                    ActivityReportDateTime = legalityQLASupportingDataonlyDto.ActivityReportDateTime,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityQLASupportingDataonlyDTO> Map(List<LegalityQLASupportingData> element)
        {
            throw new NotImplementedException();
        }
    }
}
