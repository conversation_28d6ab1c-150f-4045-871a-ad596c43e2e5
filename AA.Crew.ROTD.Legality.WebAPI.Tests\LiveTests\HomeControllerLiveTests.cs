﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests.LiveTests
{
    [TestClass]
    public class HomeControllerLiveTests
    {
        public string ROMSAPIUrl { get; set; }
        [TestInitialize]
        public void Setup()
        {
            ROMSAPIUrl = "https://localhost:44390/ROMS/";
            //ROMSAPIUrl = ConfigFactory.GetConfigurationSection("Values")["ROMSAPIURL"] + "Ping/";
        }

        private async Task<HttpResponseMessage> PingROMSAPI()
        {
            HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            HttpResponseMessage response = await client.GetAsync(ROMSAPIUrl);
            return response;
        }

        [TestMethod]
        public void ROMS_HomePing()
        {
            var response = PingROMSAPI().Result;
            Assert.IsTrue(response.IsSuccessStatusCode, "Expected ROMS ping to work");
            Assert.AreEqual(response.StatusCode, System.Net.HttpStatusCode.OK);
            Assert.IsTrue(response.Content.ReadAsStringAsync().Result.Contains("Hello from API"));
        }
    }

}