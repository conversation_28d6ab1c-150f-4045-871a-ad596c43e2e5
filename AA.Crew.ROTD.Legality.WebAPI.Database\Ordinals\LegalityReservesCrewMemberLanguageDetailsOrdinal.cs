using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityReservesCrewMemberLanguageDetailsOrdinal
    {
         /* Oridinal variables */ 

        internal Int32 LanguageID;
internal Int32 IsExcluded;
internal Int32 EmployeeID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.LanguageID = sqlDataReader.GetOrdinal("LanguageID");
this.IsExcluded = sqlDataReader.GetOrdinal("IsExcluded");
this.EmployeeID = sqlDataReader.GetOrdinal("EmployeeID");


            this.Initialized = true;
        }
    }
}
