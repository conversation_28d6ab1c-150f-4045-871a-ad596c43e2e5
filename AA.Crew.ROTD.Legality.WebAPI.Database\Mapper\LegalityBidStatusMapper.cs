using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityBidStatusMapper : MapperBase<List<LegalityBidStatusDTO>, List<FlightAttendantBidStatus>>
    {
        public override List<FlightAttendantBidStatus> Map(List<LegalityBidStatusDTO> legalityBidStatusDtoList)
        {
            try
            {
                return legalityBidStatusDtoList.Select(legalityBidStatusDto => new FlightAttendantBidStatus
                {
                    ActualPayProjection = Convert.ToDouble(legalityBidStatusDto.ActualPayProjection),
                    BidMonthIndicatorID = legalityBidStatusDto.BidMonthIndicatorID,
                    CalloutTime = Convert.ToDouble(legalityBidStatusDto.CalloutTime),
                    CrewStatusID = legalityBidStatusDto.CrewStatusID,
                    MaxCredit = Convert.ToDouble(legalityBidStatusDto.MaxCredit),
                    ReservesCrewMemberBidStatusID = legalityBidStatusDto.ReservesCrewMemberBidStatusID,
                    ReservesCrewMemberID = legalityBidStatusDto.ReservesCrewMemberID,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityBidStatusDTO> Map(List<FlightAttendantBidStatus> element)
        {
            throw new NotImplementedException();
        }
    }
}
