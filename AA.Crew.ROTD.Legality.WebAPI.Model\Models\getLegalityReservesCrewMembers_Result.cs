using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityReservesCrewMembers_Result
    {
        public long EmployeeID { get; set; }
        public Nullable<int> SeniorityNumber { get; set; }
        public long RunID { get; set; }
        public Nullable<int> AVLDays { get; set; }
        public Nullable<int> ASGSequence { get; set; }
        public Nullable<bool> IsVolunteer { get; set; }
        public long ReservesCrewMemberID { get; set; }
        public string Name { get; set; }
        public Nullable<bool> IsSick { get; set; }
        public Nullable<int> ASGDays { get; set; }
        public Nullable<int> AVLDaysWithFT { get; set; }
        public Nullable<int> ASGDaysWithFT { get; set; }
        public Nullable<int> ASGStandby { get; set; }
        public Nullable<long> RunExcludeReasonID { get; set; }
        public Nullable<bool> IsAggressive { get; set; }
        public Nullable<bool> ETBonFD { get; set; }
    }
}
