using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalitySequenceLanguageDetailsOrdinal
    {
        /* Oridinal variables */

        internal Int32 SequencePositionDetailsID;
        internal Int32 LanguageID;
        internal Int32 IsExcluded;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.SequencePositionDetailsID = sqlDataReader.GetOrdinal("SequencePositionDetailsID");
            this.LanguageID = sqlDataReader.GetOrdinal("LanguageID");
            this.IsExcluded = sqlDataReader.GetOrdinal("IsExcluded");


            this.Initialized = true;
        }
    }
}
