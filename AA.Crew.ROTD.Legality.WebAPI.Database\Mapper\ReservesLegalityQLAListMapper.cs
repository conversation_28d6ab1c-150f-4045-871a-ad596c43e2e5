using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class ReservesLegalityQLAListMapper : MapperBase<List<ReservesLegalityQLAListDTO>, List<LegalityQLADetail>>
    {
        public override List<LegalityQLADetail> Map(List<ReservesLegalityQLAListDTO> reservesLegalityQLAListDtoList)
        {
            try
            {
                return reservesLegalityQLAListDtoList.Select(reservesLegalityQLAListDto => new LegalityQLADetail
                {
                    LegalityQLADetailsID = reservesLegalityQLAListDto.LegalityQLADetailsID,
                    IsLegal = reservesLegalityQLAListDto.IsLegal,
                    IsContractual = reservesLegalityQLAListDto.IsContractual,
                    IsQualified = reservesLegalityQLAListDto.IsQualified,
                    Request = reservesLegalityQLAListDto.Request,
                    Response = reservesLegalityQLAListDto.Response,
                    CreatedBy = reservesLegalityQLAListDto.CreatedBy,
                    CreateDate = reservesLegalityQLAListDto.CreateDate,
                    UpdatedBy = reservesLegalityQLAListDto.UpdatedBy,
                    UpdatedDate = reservesLegalityQLAListDto.UpdatedDate,
                    ReservesCrewSequenceLegalityID = reservesLegalityQLAListDto.ReservesCrewSequenceLegalityID,
                    LegalityPhaseID = reservesLegalityQLAListDto.LegalityPhaseID,
                    FosRAP = reservesLegalityQLAListDto.FosRAP,
                    IsCurrentRAP = reservesLegalityQLAListDto.IsCurrentRAP,
                    LanguageID = reservesLegalityQLAListDto.LanguageID,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<ReservesLegalityQLAListDTO> Map(List<LegalityQLADetail> element)
        {
            throw new NotImplementedException();
        }
    }
}
