using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityBidCrewWaiver_Result
    {
        public long BidCrewWaiverID { get; set; }
        public long CrewMemberID { get; set; }
        public System.DateTime CreateDate { get; set; }
        public Nullable<System.DateTime> EndDate { get; set; }
        public int BidTypeID { get; set; }
        public int WaiverTypeID { get; set; }
        public string WaiverTypeDescription { get; set; }
        public Nullable<bool> WTActive { get; set; }
        public bool BDWActive { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
    }
}
