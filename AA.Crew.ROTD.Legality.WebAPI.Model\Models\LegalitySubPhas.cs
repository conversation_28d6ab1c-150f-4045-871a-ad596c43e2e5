using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalitySubPhas
    {
        public LegalitySubPhas()
        {
            this.LegalityPhaseContractDetails = new HashSet<LegalityPhaseContractDetail>();
        }
    
        public long LegalitySubPhaseID { get; set; }
        public string LegalitySubPhases { get; set; }
    
        public virtual ICollection<LegalityPhaseContractDetail> LegalityPhaseContractDetails { get; set; }
    }
}
