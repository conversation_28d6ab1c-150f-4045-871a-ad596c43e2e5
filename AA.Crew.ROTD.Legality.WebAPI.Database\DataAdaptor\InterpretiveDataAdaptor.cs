﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor
{
    internal class InterpretiveDataAdaptor : AdaptorBase, IInterpretiveDataAdaptor
    {
        #region Private data types
        private const string GETLEGALITYBASEPROCESSINGDATE_SP = "[ROTD].[getLegalityBaseProcessingDateByRunID]";
        private SqlDataReader sqlDR = null;
        #endregion

        public InterpretiveDataAdaptor(string connectionString) : base(connectionString)
        {
        }

        public void InitalizeBaseProcessingSP()
        {
            this.SqlCommand.CommandText = GETLEGALITYBASEPROCESSINGDATE_SP;
            this.SqlCommand.Parameters.Clear();
        }
        public async Task<getLegalityBaseProcessingDateByRunID_Result> GetBaseProcessingDate(Int64 runId)
        {
            getLegalityBaseProcessingDateByRunID_Result baseData = new getLegalityBaseProcessingDateByRunID_Result();
            try
            {
                //Initalize the DataReader
                this.InitalizeBaseProcessingSP();
                this.SqlCommand.Parameters.Add(new SqlParameter("RunID", runId));

                this.sqlDR = await this.SqlCommand.ExecuteReaderAsync();

                //Declare Ordinal stucts to hold the column position while reading data.
                Ordinals.BaseDateOrdinals baseDateOrdinals = new Ordinals.BaseDateOrdinals();
                while (this.sqlDR.Read()) //For each Record returned
                {
                    //Determine the the ordinals for the select statement have been assigned.
                    if (!baseDateOrdinals.Initialized)
                    {
                        //If not then assign them.
                        baseDateOrdinals.Initalize(this.sqlDR);
                    }

                    baseData.BaseCD = this.sqlDR.GetString(baseDateOrdinals.BaseCD);
                    baseData.ProcessingDate = this.sqlDR.GetDateTime(baseDateOrdinals.ProcessingDate);
                }
            }
            catch (SqlException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return baseData;
        }
    }
}
