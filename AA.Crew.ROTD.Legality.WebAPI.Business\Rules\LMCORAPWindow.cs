﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    public class LMCORAPWindow : IROTDRules
    {
        private List<string> retRuleName;
        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faCurRap, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate baseDate, ref bool possibleIsLegal, ref string rapCode, ref int isCurrentRAP, List<QLARuleResult> qlaRuleResult, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleName = new List<string>();
            bool flag = true;

            var _2hours = TimeSpan.FromHours(2);
            TimeSpan seqCallOutTime = new TimeSpan();
            TimeSpan stbyCallOutTime = new TimeSpan();

            if (bidCrewWaiver != null)
            {
                var waiverData = bidCrewWaiver.Where(e => e.CrewMemberId == flightAttendant.EmployeeNumber && e.WaiverTypeID == (int)WaiverTypes.LessThanMinimumCallout).Select(x => x.WaiverSupportingData).FirstOrDefault();

                if (waiverData != null && waiverData.Count > 0)
                {
                    if (sequence != null)
                    {
                        if (waiverData.Where(y => y.BaseCoTerminal == sequence.StartBase).Count() > 0)
                            seqCallOutTime = waiverData.Where(y => y.BaseCoTerminal == sequence.StartBase).Select(t => t.TimeToDeparture).FirstOrDefault();
                        else
                            flag = false;
                    }

                    if (standby != null)
                    {
                        if (waiverData.Where(y => y.BaseCoTerminal == standby.Base).Count() > 0)
                            stbyCallOutTime = waiverData.Where(y => y.BaseCoTerminal == standby.Base).Select(t => t.TimeToDeparture).FirstOrDefault();
                        else
                            flag = false;
                    }
                }

                else
                    flag = false;
            }
            else
                flag = false;


            if (flag && faCurRap != null)
            {
                if (faCurRap.StartDate.HasValue && faCurRap.EndDate.HasValue)
                {
                    if (sequence != null)
                    {
                        if ((sequence.SatelliteStation || !IsBaseCoTerminal))
                        {
                            if (sequence.SequenceStartDateTime < faCurRap.StartDate.Value.Add(seqCallOutTime) || (sequence.SequenceReportDateTime > faCurRap.EndDate.Value.Add(_2hours)))
                            {
                                flag = false;
                            }
                        }
                        else if (IsBaseCoTerminal)
                        {
                            if (sequence.SequenceStartDateTime < faCurRap.StartDate.Value.Add(seqCallOutTime) || (sequence.SequenceReportDateTime > faCurRap.EndDate.Value.Add(_2hours)))
                            {
                                flag = false;
                            }
                        }
                    }
                    else if (standby != null)
                    {
                        if (!IsBaseCoTerminal)
                        {
                            if (standby.ReportTime < faCurRap.StartDate.Value.Add(stbyCallOutTime) || (standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration)) > faCurRap.EndDate.Value.Add(_2hours)))
                            {
                                flag = false;
                            }
                        }
                        else if (IsBaseCoTerminal)
                        {
                            if (standby.ReportTime < faCurRap.StartDate.Value.Add(stbyCallOutTime) || (standby.ReportTime.Add(TimeSpan.FromHours(standby.Duration)) > faCurRap.EndDate.Value.Add(_2hours)))
                            {
                                flag = false;
                            }
                        }
                    }

                } 
            }


            if (!flag)
            {
                possibleIsLegal = false;
                retRuleName.Add(ruleName);
            }
            return retRuleName;
        }

        public string RuleName { get; set; }

    }
}
