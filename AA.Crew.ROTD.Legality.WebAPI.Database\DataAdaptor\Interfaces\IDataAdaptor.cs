﻿using System;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces
{
    public interface IDataAdaptor : IDisposable
    {
        /// <summary>
        /// Opens a connection to the Database        
        /// </summary>
        /// <exception cref="Exceptions.DataConnectionException">Connection failure exception</exception>
        Task Connect();


        /// <summary>
        /// Closes a connection to the Database        
        /// </summary>
        /// <exception cref="Exceptions.DataConnectionException">Connection failure exception</exception>
        void Close();
    }
}
