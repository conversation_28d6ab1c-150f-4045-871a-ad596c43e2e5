﻿using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Request;
using ContractMonth = AA.Crew.ROTD.Legality.WebAPI.Model.ContractMonth;

namespace AA.Crew.ROTD.Legality.WebAPI.Service.Interfaces
{
    public interface ICrewDataServiceProvider
    {
        Task<ContractMonth> GetContractMonthForGivenDate(DateTime processDate);
        Task<List<RAPShifts>> GetRAPShift(string BaseCD, string ContractMonth);
    }
}