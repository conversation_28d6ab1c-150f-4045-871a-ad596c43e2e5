using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityReservesCrewMembersOrdinal
    {
        /* Oridinal variables */

        internal Int32 EmployeeID;
        internal Int32 SeniorityNumber;
        internal Int32 RunID;
        internal Int32 AVLDays;
        internal Int32 ASGSequence;
        internal Int32 IsVolunteer;
        internal Int32 ReservesCrewMemberID;
        internal Int32 Name;
        internal Int32 IsSick;
        internal Int32 ASGDays;
        internal Int32 AVLDaysWithFT;
        internal Int32 ASGDaysWithFT;
        internal Int32 ASGStandby;
        internal Int32 RunExcludeReasonID;
        internal Int32 IsAggressive;
        internal Int32 ETBonFD;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.EmployeeID = sqlDataReader.GetOrdinal("EmployeeID");
            this.SeniorityNumber = sqlDataReader.GetOrdinal("SeniorityNumber");
            this.RunID = sqlDataReader.GetOrdinal("RunID");
            this.AVLDays = sqlDataReader.GetOrdinal("AVLDays");
            this.ASGSequence = sqlDataReader.GetOrdinal("ASGSequence");
            this.IsVolunteer = sqlDataReader.GetOrdinal("IsVolunteer");
            this.ReservesCrewMemberID = sqlDataReader.GetOrdinal("ReservesCrewMemberID");
            this.Name = sqlDataReader.GetOrdinal("Name");
            this.IsSick = sqlDataReader.GetOrdinal("IsSick");
            this.ASGDays = sqlDataReader.GetOrdinal("ASGDays");
            this.AVLDaysWithFT = sqlDataReader.GetOrdinal("AVLDaysWithFT");
            this.ASGDaysWithFT = sqlDataReader.GetOrdinal("ASGDaysWithFT");
            this.ASGStandby = sqlDataReader.GetOrdinal("ASGStandby");
            this.RunExcludeReasonID = sqlDataReader.GetOrdinal("RunExcludeReasonID");
            this.IsAggressive = sqlDataReader.GetOrdinal("IsAggressive");
            this.ETBonFD = sqlDataReader.GetOrdinal("ETBonFD");


            this.Initialized = true;
        }
    }
}
