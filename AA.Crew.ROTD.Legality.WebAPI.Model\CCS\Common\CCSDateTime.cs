﻿using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Common
{
    public class CCSDateTime
    {

        private static long serialVersionUID = 2136373614429948220L;

        /// <summary>
        /// DateTime is an immutable date-time object that represents a date-time
        /// </summary>
        public DateTime localTime { get; set; }


        /// <summary>
        /// Greenwich mean time (GMT), which is equivalent to universal time (UT)
        /// </summary>
        public DateTime gmt { get; set; }

        /// <summary>
        /// The time required by a qualified individual working for completion of a given work cycle with no allowance for delay and personal needs
        /// </summary>
        public DateTime baseTime { get; set; }


    }
}
