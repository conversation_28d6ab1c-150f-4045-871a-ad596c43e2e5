﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Request
{
    public class GetCrewMembersRequest : RequestBase
    {
        public CrewMemberKey[] crewMemberKeysDTO { get; set; }

        public string[] contractMonths { get; set; }

        [JsonProperty(ItemConverterType = typeof(StringEnumConverter))]
        public List<CrewMemberRequestOptions> gets { get; set; }
    }
}
