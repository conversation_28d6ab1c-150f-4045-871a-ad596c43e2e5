﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Collections;
using System.Linq;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject
{
    [DataContract]
    public class FlightAttendant
    {
        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public int EmployeeNumber { get; set; }

        [DataMember]
        public bool isSpeaker { get; set; }

        [DataMember]
        public int SeniorityNumber { get; set; }

        [DataMember]
        public int AvailableDays { get; set; }

        [DataMember]
        public bool IsVolunteer { get; set; }

        [DataMember]
        public bool IsSick { get; set; }

        [DataMember]
        public int StandbyGroup { get; set; }

        [DataMember]
        public int ProcessingGroup { get; set; }

        public bool isAwarded { get; set; }

        [DataMember]
        public string AdditionalFADetails { get; set; }

        [DataMember]
        public long ReservesCrewMemberID { get; set; }
        [DataMember]
        public int ASGSequence { get; set; }

        [DataMember]
        public int ASGDays { get; set; }
        [DataMember]
        public int AVLDaysWithFT { get; set; }
        [DataMember]
        public int ASGDaysWithFT { get; set; }
        [DataMember]
        public int ActualPayProjectionCurrentMonth { get; set; }
        [DataMember]
        public int MaxCreditCurrentMonth { get; set; }
        [DataMember]
        public int ActualPayProjectionNextPrevMonth { get; set; }

        [DataMember]
        public int MaxCreditNextPrevMonth { get; set; }
        [DataMember]
        public int ASGStandby { get; set; }
        public long BaseDateId { get; set; }

        [DataMember]
        public List<BidSequenceMapper> BidSequenceMappers { get; set; }

        [DataMember]
        public List<BidStandByMapper> BidStandByMappers { get; set; }

        [DataMember]
        public List<FALanguageDetails> fALanguageDetails { get; set; }

        [DataMember]
        public bool hasOperationalLanguage { get; set; }

        [DataMember]
        public List<FlightAttendantBidStatus> FAStatus { get; set; }

        [DataMember]
        public Activity FARAPActivity { get; set; }

        [DataMember]
        public List<Activity> FACurrentRAPs { get; set; }

        public bool IsRAP { get; set; }
        //public bool IsActivitySequence { get; set; }
        //public bool IsActivityStandby { get; set; }
        //public bool IsActivitySatellite { get; set; }

        [DataMember]
        public CalcRAP CALCRapStatus { get; set; }

    }


    [DataContract]
    public class Language
    {
        [DataMember]
        public int LanguageID { get; set; }

        [DataMember]
        public string LanguageName { get; set; }

    }

    [DataContract]
    public class QLAContextRuleResult
    {
        public string RequestId { get; set; }
        public int ActivityID { get; set; }
        public string Rule { get; set; }
        public string Result { get; set; }
        public string Messages { get; set; }
        public bool isContext { get; set; }
        public long EmployeeID { get; set; }
        public long ReservesCrewMemberID { get; set; }

    }


    [DataContract]
    public class SequenceLanguageDetails
    {
        [DataMember]
        public int SeqPosLangID { get; set; }

        [DataMember]
        public int SequencePosDetailID { get; set; }

        [DataMember]
        public int LanguageID { get; set; }

        [DataMember]
        public string SeqPosition { get; set; }

        [DataMember]
        public bool IsExcluded { get; set; }
    }

    [DataContract]
    public class FALanguageDetails
    {
        [DataMember]
        public Language FALang { get; set; }

        [DataMember]
        public FlightAttendant FA { get; set; }

        [DataMember]
        public int FALangID { get; set; }

        [DataMember]
        public bool IsExcluded { get; set; }
    }

    [DataContract]
    public class OperatingLanguage
    {
        public long RapDayCategoryID { get; set; }
        public string RapDayCategory { get; set; }
        public int RapDaysID { get; set; }
        public int LanguageID { get; set; }
        public string LanguageCD { get; set; }
    }

    [DataContract]
    public class LegalCombinations
    {
        [DataMember]
        public int LegalCombinationsID { get; set; }

        [DataMember]
        public Sequence SequenceDetail { get; set; }

        [DataMember]
        public int MovableDays { get; set; }

        [DataMember]
        public FlightAttendant FADetail { get; set; }

        [DataMember]
        public bool IsASG { get; set; }

        [DataMember]
        public bool IsASI { get; set; }
    }
    public class CalcRAP
    {
        public string RAPShift { get; set; }

        public int IsCurrentStatus { get; set; }

    }
    public class ActivityType
    {
        public int ActivityTypeID { get; set; }


        public string ActivityTypeDescription { get; set; }

    }

    public class BidSequenceStandByMapper
    {
        public List<BidSequenceMapper> sequenceMapper { get; set; }
        public List<BidStandByMapper> standByMapper { get; set; }
        public int employeeNumber { get; set; }
    }

    public class ContextualList
    {
        public string Employeenumber { get; set; }
        public List<Sequence> AwardList { get; set; }


        public List<Sequence> AssignList { get; set; }

        public List<Sequence> NotLegalList { get; set; }

        public List<RulesWithSeqStdby> Rules { get; set; }

        public List<StandBy> StdAwardList { get; set; }


        public List<StandBy> StdAssignList { get; set; }

        public List<StandBy> StdNotLegalList { get; set; }
    }

    public class RulesWithSeqStdby
    {
        public int SeqStdByID { get; set; }
        public string RuleName { get; set; }
    }

    public class Activity
    {
        [DataMember]
        public Int64 ActivityID { get; set; }

        public long Employeenumber { get; set; }

        [DataMember]
        public Int64 ActivityTypeID { get; set; }

        [DataMember]
        public string ActivityCode { get; set; }

        [DataMember]
        public string ActivityType { get; set; }

        [DataMember]
        public Nullable<int> DurationInDays
        { get; set; }
        [DataMember]
        public Nullable<DateTime> HomeBaseFAReducedRestEndTime { get; set; }

        [DataMember]
        public Nullable<DateTime> HomeBaseRestEndTime { get; set; }

        [DataMember]
        public Nullable<DateTime> StartDate
        { get; set; }

        [DataMember]
        public Nullable<DateTime> EndDate
        { get; set; }
        public long ReservesCrewMemberID { get; set; }
    }

    [DataContract]
    public class Sequence
    {
        public Sequence()
        {

        }

        public Sequence(Sequence seq)
        {
            this.SequenceID = seq.SequenceID;
            this.SequenceNumber = SequenceNumber;
            this.SequenceName = seq.SequenceName;
            this.SequencePosition = seq.SequencePosition;
            this.SequencePositionDetailsID = seq.SequencePositionDetailsID;
            this.NoOfDays = seq.NoOfDays;
            this.StartBase = seq.StartBase;
            this.DestBase = seq.DestBase;
            this.OriginationDate = seq.OriginationDate;
            this.SequenceStartDateTime = seq.SequenceStartDateTime;
            this.SequenceEndDateTime = seq.SequenceEndDateTime;
            this.EquipmentType = seq.EquipmentType;
            this.NumberofLanding = seq.NumberofLanding;
            this.isSpeaker = seq.isSpeaker;
            this.IsSpecialSequence = seq.IsSpecialSequence;
            this.IsStandby = seq.IsStandby;
            this.CreditThisMonth = seq.CreditThisMonth;
            this.CreditNextMonth = seq.CreditNextMonth;
            this.CoTerminalStation = seq.CoTerminalStation;
            this.SatelliteStation = seq.SatelliteStation;
            this.SequenceReportDateTime = seq.SequenceReportDateTime;
        }

        [DataMember]
        public long SequenceID { get; set; }

        [DataMember]
        public long SequenceNumber { get; set; }

        [DataMember]
        public string SequenceName { get; set; }

        [DataMember]
        public string SequencePosition { get; set; }

        [DataMember]
        public Int64 SequencePositionDetailsID { get; set; }

        [DataMember]
        public int NoOfDays { get; set; }

        [DataMember]
        public string StartBase { get; set; }

        [DataMember]
        public string DestBase { get; set; }

        [DataMember]
        public DateTime SequenceStartDateTime { get; set; }

        [DataMember]
        public DateTime OriginationDate { get; set; }

        [DataMember]
        public DateTime SequenceEndDateTime { get; set; }

        [DataMember]
        public string EquipmentType { get; set; }

        [DataMember]
        public int NumberofLanding { get; set; }

        [DataMember]
        public bool isSpeaker { get; set; }

        [DataMember]
        public Language Speaker { get; set; }

        [DataMember]
        public bool IsSpecialSequence { get; set; }

        public bool IsAssigned { get; set; }

        [DataMember]
        public bool IsStandby { get; set; }

        [DataMember]
        public double CreditThisMonth { get; set; }

        [DataMember]
        public double CreditNextMonth { get; set; }

        public bool IsAwarded { get; set; }

        public bool isAwarded { get; set; }

        [DataMember]
        public string AdditionalSequenceDetails { get; set; }

        [DataMember]
        public DateTime SequenceReportDateTime { get; set; }

        [DataMember]
        public bool CoTerminalStation { get; set; }
        [DataMember]
        public bool SatelliteStation { get; set; }

        [DataMember]
        public List<SequenceLanguageDetails> SequenceLanguageDetails;
    }

    public class StandBy
    {
        public StandBy()
        {

        }
        public StandBy(StandBy stdBy)
        {
            this.StandByID = stdBy.StandByID;
            this.AirportGate = stdBy.AirportGate;
            this.Base = stdBy.Base;
            this.ReportTime = stdBy.ReportTime;
            this.MinAVLDays = stdBy.MinAVLDays;
            this.CreditThisMonth = stdBy.CreditThisMonth;
            this.CreditNextMonth = stdBy.CreditNextMonth;
            this.CoTerminalStation = stdBy.CoTerminalStation;
            this.Duration = stdBy.Duration;
            this.ShiftDurationHrs = stdBy.ShiftDurationHrs;
            this.ActivityID = stdBy.ActivityID;
        }

        public long StandByID { get; set; }
        public string AirportGate { get; set; }
        public string Base { get; set; }
        public DateTime ReportTime { get; set; }
        public int MinAVLDays { get; set; }
        public int CreditThisMonth { get; set; }
        public int CreditNextMonth { get; set; }
        public bool CoTerminalStation { get; set; }
        public bool IsAssigned { get; set; }
        public bool IsAwarded { get; set; }
        public int Duration { get; set; }
        public int ActivityID { get; set; }
        public string ShiftDurationHrs { get; set; }
    }
    public class StandByCredit
    {
        public int NoOfDays { get; set; }
        public int Credit { get; set; }
    }
    public class ContractMonth
    {
        public string Month { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class RcsDetails
    {
        public int RcsDetailsID { get; set; }
        public int ReservesCrewSequenceLegalityID { get; set; }
        public long QLADetailsID { get; set; }
        public int ReservesCrewSequenceContractDetailID { get; set; }
        public int FosRAP { get; set; }
        public bool IsCurrentRAP { get; set; }
        public int LanguageID { get; set; }
    }

    public enum ROTDPhases
    {
        NonVolunteer = 1, IsVolunteer = 2, Future = 3, ETB = 4, ETB_Future = 5, LineHolder = 6
    }

    public enum ROTDPostQLAStates
    {
        TrueAlways = 1,
        TrueWaiver = 2,
        NotLegal = 3,
        FD1 = 4,
        FD2 = 5
    }

    public enum WaiverTypes
    {
        LessThanMinimumCallout = 11
    }

    public class PostQLAStatesConst
    {
        public const string TrueAlways = "True (Always)";
        public const string TrueWaiver = "True (Waiver)";
        public const string NotLegal = "Not Legal";
        public const string FD1 = "FD (1)";
        public const string FD2 = "FD (2+)";
    }

    public class BidTypes
    {
        public const int DAILY_BID = 1;
        public const int STANDING_BID = 2;
        public const int AGRESSIVE_BID = 3;
    }

    public class BidCriteria
    {
        public const int USING_DAY_OFF = 1;
        public const int MAX_DAYS_AVL = 2;
        public const int MIN_DAYS_AVL = 3;
        public const int SPECIFIC_OPEN_SEQUENCE = 4;
        public const int EQUIPMENT_TYPE = 5;
        public const int SEQUENCE_DURATION = 6;
        public const int BEFORE_REPORT_TIME = 7;
        public const int AFTER_REPORT_TIME = 8;
        public const int BEFORE_RELEASE_TIME = 9;
        public const int AFTER_RELEASE_TIME = 10;
        public const int RON_CITY = 11;
        public const int DEAD_HEAD = 12;
        public const int LAYOVER_DURATION_MIN = 13;
        public const int LAYOVER_DURATION_MAX = 14;
        public const int LAYOVER_CITY_INCLUDE = 15;
        public const int LAYOVER_CITY_EXCLUDE = 16;
        public const int NUMBER_LANDING_MIN = 17;
        public const int NUMBER_LANDING_MAX = 18;
        public const int REMAIN_ON_CALL = 19;
        public const int STANDBY = 20;
        public const int SPECIAL_SEQUENCES = 21;
    }

    [DataContract]
    public class Bid
    {
        [DataMember]
        public int BidNumber { get; set; }

        [DataMember]
        public double BidRank { get; set; }

        [DataMember]
        public int BidCategoryId { get; set; }

        [DataMember]
        public int BidTypeId { get; set; }

        [DataMember]
        public string BidValue { get; set; }

        [DataMember]
        public FlightAttendant FADetail { get; set; }

        [DataMember]
        public bool IsLODO { get; set; }

        [DataMember]
        public bool IsVolunteer { get; set; }

        public bool isMapped { get; set; }

        public bool isWaive35by7 { get; set; }
        public bool isWaiveHomeBaseRest { get; set; }
        public bool isAllowDoubleUp { get; set; }
        public bool isAwardintoFDGD { get; set; }

    }

    public class BidCrewWaiver
    {
        public long BidCrewWaiverId { get; set; }
        public long CrewMemberId { get; set; }
        public int BidTypeId { get; set; }
        public DateTime StartDate { get; set; }
        public int WaiverTypeID { get; set; }
        public string WaiverTypeDescription { get; set; }
        public bool IsActive { get; set; }
        public int BidCategoryID { get; set; }
        public List<BidCrewWaiverSupportingData> WaiverSupportingData { get; set; }
    }

    public class BidCrewWaiverSupportingData
    {
        public long BidCrewWaiverId { get; set; }
        public int WaiverTypeID { get; set; }
        public string BaseCoTerminal { get; set; }
        public TimeSpan TimeToDeparture { get; set; }

    }
    public class AggressiveBidCrewWaiver
    {
        public long EmployeeID { get; set; }
        public string FAName { get; set; }
        public int BidTypeId { get; set; }
        public string BidTypeName { get; set; }
        public DateTime OperatingDate { get; set; }
        public int BidCategoryID { get; set; }
    }
    public class BidWaiverType
    {
        public int WaiverTypeID { get; set; }
        public string WaiverTypeDescription { get; set; }

    }
    public class BidWaiver
    {
        public FlightAttendant FADetail { get; set; }

        public DateTime OrginationDate { get; set; }

        public int BidCategoryId { get; set; }

        public bool Waive35 { get; set; }

        public bool WaiveHomeBaseRest { get; set; }

        public bool AllowDoubleUp { get; set; }

        public bool AwardintoFDGD { get; set; }

        public bool Allowmultiplesequenceaward { get; set; }

        public bool AllowLessthanMinimumCallOut { get; set; }

        public bool AllowPickuponFDGD { get; set; }
    }
    public class BidSequenceMapper
    {
        public Sequence sequence { get; set; }
        public Bid bid { get; set; }

        public BidWaiver bidWaiver { get; set; }
        public bool isRapOrRocBid { get; set; }
        public BidMoveType bidMoveType { get; set; }
    }

    public class BidStandByMapper
    {
        public StandBy standBy { get; set; }
        public Bid bid { get; set; }

        public BidWaiver bidWaiver { get; set; }
        public bool isRapOrRocBid { get; set; }
        public BidMoveType bidMoveType { get; set; }
    }

    public class FABidReason
    {
        public FlightAttendant Fa { get; set; }
        public List<BidReason> BidReasons { get; set; }
    }

    public class BidReason
    {
        public BidSequenceMapper BidSequenceMapper { get; set; }
        public BidResult BidResult { get; set; }
        public string Reason { get; set; }
    }

    public enum RuleCategory { NoBids = 0, Bids = 1, Volunteer = 2, AllforInterpretive = 3, IsSick = 4 }

    public enum ROTDRuleCategory { NoRAP = 0, RAP = 1, MinCallRAP = 2, MinCallNoRAP = 3}

    public enum ActivityTypes { Sequence = 1, Standby = 2, FDGD = 3, Training = 4, RAP = 5, Vacation = 6, US = 7, SEQ, STB, SAT }

    public enum DropActivityType { Ghost = 1, Sequence = 2, Standby = 3, RAP = 4 }

    public enum CrewType { NonVolunteer = 1, Volunteer = 2, IsSick = 3 }

    public enum BidResult
    {
        None = 0,
        Awarded,
        Denied
    }
    public enum BidMoveType
    {
        None = 0,
        MoveUp = 1,
        MoveDown = 2,
        SameCategory = 3,
        ROC = 4,
        SpecialSequence = 5,
        Standby = 6,
    }
    public enum CCSDetails
    {
        AA,
        SEQ,
        STB,
        VAC,
        GD,
        FD,
        SCK
    }

    public enum ActivityCode { US, SEQ, D24, MVD, TRN, FLD, GLD, GFD }

    public enum BidCategory
    {
        Undefined = -1,
        Speaker = 1,
        NonSpeaker = 2
    }

    public class BidSequence
    {
        public List<BidSequenceMapper> BidMapperList { get; set; }
        public BidSequenceType SequenceType { get; set; }
    }

    public enum BidSequenceType
    {
        NONE,
        NONTI,
        TI,
        ROC,
        STANDBY
    }

    public class DataState
    {
        public FlightAttendant FA { get; set; }
        public int OriginalGroup { get; set; }
        public bool IsReleased { get; set; }
        public Assignment Award { get; set; }
    }

    [DataContract]
    public class Assignment
    {
        [DataMember]
        public FlightAttendant FA { get; set; }

        [DataMember]
        public Sequence sequence { get; set; }

        [DataMember]
        public AssignmentStatus status { get; set; }

        [DataMember]
        public List<BidReason> BidReasons { get; set; }

        [DataMember]
        public int AssignmentOrder { get; set; }

    }
    public enum StepType
    {
        Regular,
        Sick

    }
    public class AssignmentGroup
    {
        public List<Assignment> Awards { get; set; }
        public List<Sequence> Sequences { get; set; }
        public List<FlightAttendant> Fas { get; set; }
        public List<LegalCombinations> LegalCombinaitons { get; set; }
        public int StepNumber { get; set; }
        public StepType StepType { get; set; }

    }

    public enum AssignmentStatus
    {
        SEQUENCE_AWARDED,
        SEQUENCE_ASSIGNED,
        ROC_ASSIGNED,
        ROC_AWARDED,
        STANDBY_ASSIGNED,
        STANDBY_AWARDED,
        STANDBY_HOLD,
        UNASSIGNED,
        NONE
    }

    public class BidFailureReasons
    {
        public const string SEQUENCE_ALREADY_ASSIGNED = "Violation: Already Awarded.";
        public const string SEQUENCE_NOT_RELEASED = "Violation: [Bid More Days] Pairing assigned to group.";
        public const string FA_LOCKED_GROUP = "Violation: [Bid Less Days] Reserve assigned to group";
        public const string MD_INCREASE = "Violation: Forces Reserve into Movable Day.";
        public const string OVERALL_SEQUENCE_DECREASE = "Violation: Reduces Maximum Assignments.";
        public const string BID_AWRDED = "Awarded {0} ";
        public const string VOL_BID_DECLINED = "Violation: Already Awarded.";
        public const string ROC_DECLINED = "Violation: Reduces Maximum Assignments";
        public const string STANDBY_ALREADY_ASSIGNED = "Violation: Already Awarded.";

        public const string OVERALL_STANDBY_DECREASE = "Violation: Reduces Maximum Assignments.";
    }

    public enum LegalityPhasesStatus
    {
        Contextual,
        Interpretive
    }

    public enum LegalityPhase
    {
        All = 1,
        IsVolunteer,
        Future,
        ETB,
        ETB_Future,
        ROTAContextual = 5, ROTAInterpretive = 6, ROTAQLA = 7
    }

    public enum ROTDLegalityPhase
    {
        NonVolunteer = 1,
        IsVolunteer = 2,
        Future = 3,
        ETB = 4,
        ETB_Future = 5,
        LineHolder = 6,
        NonVolunteerStandBy = 7,
        LineHolderStandBy = 8,
        LineHolder_Future = 9
    }

    public static class ROTDRULE
    {

        public static Dictionary<long, string> DictionaryWaiverCode = new Dictionary<long, string>()
        {
        {1,"True (Always)"},
        {2,"True (Waiver)"},
        {3,"Not Legal"},
        {4,"FD (1)"},
        {5,"FD (2+)"}
        };
    }



    public enum AuditColumns
    {
        CreatedByID = 1, UpdatedByID = 1
    }
    public class ReservesCrewSequenceLegality
    {
        public int ReservesCrewSequenceLegalityID { get; set; }
        public int ReservesCrewMemberID { get; set; }
        public Int64 BaseDateID { get; set; }
        public Int64 SequencePositionDetailsID { get; set; }
        public long SequenceorStandByID { get; set; }
        public long StandByID { get; set; }
        public string SequencePosition { get; set; }
        public bool AssignmentLegality { get; set; }
        public bool AwardLegality { get; set; }
        public int LegalityPhaseID { get; set; }
        public long SequenceNumber { get; set; }
        public int EmployeeID { get; set; }
        public bool isOver35By7 { get; set; }
        public LegalityPhases legalityPhases { get; set; }
        public DateTime SequenceOriginationDate { get; set; }
    }

    public class ROTALegalityResults
    {
        public long EmployeeId { get; set; }
        public long SequenceNumber { get; set; }
        public string SequencePosition { get; set; }
        public long BaseDateID { get; set; }
        public bool IsOver35By7 { get; set; }
        public DateTime SequenceOriginationDate { get; set; }
        public long StandByID { get; set; }
        public int MinAVLDays { get; set; }
        public string ShiftDurationHrs { get; set; }
        public string ActivityType { get; set; }
        public List<LegalityContext> LegalityContext {get;set;}
    }

    public class LegalityContext
    {
        public long LegalityContextID { get; set; }
        public string LegalityContextName { get; set; }
        public bool IsLegal { get; set; }
        public List<string> FailedRules { get; set; }
    }

    [DataContract]
    public class RAPShifts: ICloneable
    {
        [DataMember]
        public string Shift { get; set; }
        [DataMember]
        public DateTime StartDateTime { get; set; }
        [DataMember]
        public DateTime EndDateTime { get; set; }
        [DataMember]
        public string ContractMonth { get; set; }
        public int IsCurrentStatus { get; set; }

        public object Clone()
        {
            return new RAPShifts { 
                StartDateTime= this.StartDateTime, 
                EndDateTime = this.EndDateTime, 
                Shift = this.Shift, 
                ContractMonth = this.ContractMonth, 
                IsCurrentStatus = this.IsCurrentStatus 
            };
        }
    }


    public class BaseDate
    {
        [DataMember]
        public long BaseDateID { get; set; }
        [DataMember]
        public string BaseName { get; set; }
        [DataMember]
        public DateTime ProcessingDate { get; set; }

        public DateTime CurrentSysTime { get; set; }
    }

    public class LegalityContextDetails
    {
        public int LegalityContextDetailsID { get; set; }
        public int ReservesCrewSequenceLegalityID { get; set; }
        public bool IsLegal { get; set; }
        public string Message { get; set; }
        public string AwardMessage { get; set; }
        public string AssignMessage { get; set; }
        public string ContextMessage { get; set; }

        public ReservesCrewSequenceLegality crewSequenceLegality { get; set; }
    }
    public class ROTDLegalityContextDetails
    {
        public int LegalityContextDetailsID { get; set; }
        public int ReservesCrewSequenceLegalityID { get; set; }
        public bool IsLegal { get; set; }
        public string Message { get; set; }
        public string ContextMessage { get; set; }

        public ROTDReservesCrewSequenceLegality crewSequenceLegality { get; set; }
    }

    public class ROTDReservesCrewSequenceLegality
    {
        public int ROTDReservesCrewSequenceLegalityID { get; set; }
        public int ReservesCrewMemberID { get; set; }
        public Int64 RunID { get; set; }
        public Int64 SequencePositionDetailsID { get; set; }
        public long StandByID { get; set; }
        public bool IsOver35By7 { get; set; }
        public bool IsOver35By7LH { get; set; }
        public int EmployeeID { get; set; }

        public LegalityPhases legalityPhases { get; set; }
    }

    public class ROTDLegalityInterpretDetails
    {
        public int LegalityInterpretDetailsID { get; set; }
        public int ReservesCrewSequenceLegalityID { get; set; }
        public bool IsLegal { get; set; }
        public string Message { get; set; }
        public string SequencePosition { get; set; }
        public ROTDReservesCrewSequenceLegality crewSequenceLegality { get; set; }
    }


    public class LegalityInterpretDetails
    {
        public int LegalityInterpretDetailsID { get; set; }
        public int ReservesCrewSequenceLegalityID { get; set; }
        public bool IsLegal { get; set; }
        public string Message { get; set; }
        public string AwardMessage { get; set; }
        public string AssignMessage { get; set; }
        public string SequencePosition { get; set; }
        public ReservesCrewSequenceLegality crewSequenceLegality { get; set; }
        public ROTALegalityResults LegalityResults { get; set; }
    }
    public class LegalityPhases
    {
        public int LegalityPhaseID { get; set; }
        public string LegalityPhase { get; set; }
    }
    public class QLARuleConst
    {
        public const string TOUCHFD = "TOUCHFD";
        public const string TOUCHLH = "TOUCHLH";
        public const string TOUCHGD = "TOUCHGD";
        public const string ISOVRMAX = "ISOVRMAX";
        public const string ISOVRMAXB = "ISOVRMAXB";
        public const string WILLOVRP = "WILLOVRP";
        public const string WVOV35X7 = "35X7WVOV";
        public const string BLOC35X7 = "35X7BLOC";
        public const string BLOC35X7LH = "35X7LH";
        public const string MLTDUTY = "MLTDUTY";
        public const string TOUCHGST = "TOUCHGST";
        public const string TOUCHREL = "TOUCHREL";
        public const string TOUCHVAC = "TOUCHVAC";
        public const string DBLBACK = "DBLBACK";
        public const string DBLFRONT = "DBLFRONT";
        public const string TOUCHSTB = "TOUCHSTB";
        public const string HOMEREST = "HOMEREST";
        public const string SEQNFND = "SEQNFND";
        public const string ISUNDER = "ISUNDER";
        public const string ISUNDERB = "ISUNDERB";
        public const string WILLUNDP = "WILLUNDP";
        public const string TOUCHAVL = "TOUCHAVL";
        public const string SPEAKERQUAL = "SPEAKER QUAL";
        public const string NOOFFDAY = "NOOFFDAY";
        public const string TOUCHRAP = "TOUCHRAP";
        public const string LINEHOLDER = "LINEHOLDER";
    }
    public class QLAResultConst
    {
        public const string IL = "IL";
        public const string NQ = "NQ";
        public const string NC = "NC";
    }

    public class AffectedTypeConst
    {
        public const string FUTURE = "FUTURE";
        public const string ETB = "ETB";
        public const string RESTRICTED = "RESTRICTED";
        public const string LINEHOLDER = "LINEHOLDER";
    }

    public class QLARules
    {
        public string RuleName { get; set; }
        public long QLARuleID { get; set; }
    }

    public class ContractDetailsTable
    {
        public long ReservesCrewSequenceLegalityContractDetailsID { get; set; }
        public Nullable<long> ContractSectionsID { get; set; }
        public Nullable<long> LegalityPhaseID { get; set; }
        public Nullable<long> ReservesCrewSequenceLegalityID { get; set; }
        public Nullable<int> FosRAP { get; set; }
        public Nullable<int> LanguageID { get; set; }
        public Nullable<bool> IsCurrentRAP { get; set; }
    }

    public class QLARequest
    {
        public string EmployeeID { get; set; }
        public long ActivityID { get; set; }
        public string RequestId { get; set; }
        public string PositionCode { get; set; }
        public string ActivityType { get; set; }
        public DateTime ActivityOriginationDate { get; set; }
        public bool IsAssign { get; set; }
        public bool IsAward { get; set; }
        public string ActivityCode { get; set; }
        public List<Pickup> pickupDutyList { get; set; }
        public List<Drop> dropdutylist { get; set; }
    }

    public class Drop
    {
        public string airlineCode { get; set; }
        public string contractMonth { get; set; }
        public int activityId { get; set; }
        public string activityCode { get; set; }
        public string activityType { get; set; }
        public string activityOriginationDate { get; set; }
        public string positionCode { get; set; }
        public string startDateTime { get; set; }
        public string endDateTime { get; set; }
    }
    public class QLAResponse
    {
        public string EmployeeID { get; set; }
        public string RequestId { get; set; }
        public bool Valid { get; set; }
        public bool IsLegal { get; set; }
        public bool IsContractual { get; set; }
        public bool IsQualified { get; set; }
        public bool IsAssign { get; set; }
        public bool IsAward { get; set; }
        public bool IscurrentFA { get; set; }
        public string CalRAPActivityCode { get; set; }
        public string FosRAPActivityCode { get; set; }
        public int IsCurrentRAP { get; set; }
        public long ruleIdentity { get; set; }
    }
    public class Pickup
    {
        public string airlineCode { get; set; }
        public string contractMonth { get; set; }
        public int activityId { get; set; }
        public string activityCode { get; set; }
        public string activityType { get; set; }
        public string activityOriginationDate { get; set; }
        public string positionCode { get; set; }
        public string startDateTime { get; set; }
        public string endDateTime { get; set; }
    }

    public class AffectedBy
    {
        public string airlineCode { get; set; }
        public string contractMonth { get; set; }
        public int activityId { get; set; }
        public string activityCode { get; set; }
        public string activityType { get; set; }
        public string activityOriginationDate { get; set; }
        public string positionCode { get; set; }
        public string startDateTime { get; set; }
        public string endDateTime { get; set; }
    }

    public class MessageObjects
    {
        public List<Pickup> pickups { get; set; }
        public AffectedBy affectedBy { get; set; }
        public string affectedType { get; set; }
        public Nullable<DateTime> affectedStartDateTime { get; set; }
    }

    public class QLARuleResult
    {
        public string RequestId { get; set; }
        public int ActivityID { get; set; }
        public string Rule { get; set; }
        public string Result { get; set; }
        public string Messages { get; set; }
        public bool isContext { get; set; }
        public long ruleIdentity { get; set; }
        public MessageObjects messageObjects { get; set; }
        public int SequenceID { get; set; }
    }
    public class ContextualResult
    {
        public string EmployeeNo { get; set; }
        public long SequenceorStandByID { get; set; }
        public string SequencePosition { get; set; }
        public bool IsAssign { get; set; }
        public bool IsAward { get; set; }
        public long SequencePositionDetailsID { get; set; }
        public long SequenceNumber { get; set; }
        public DateTime SequenceOriginationDate { get; set; }
        public string ContextualFailedMessage { get; set; }
    }

    #region [ROTD Data]

    public class ROTDPhase
    {
        public long phaseId { get; set; }
        public string phase { get; set; }
        public string errorMessage { get; set; }
        public List<ROTDPhaseDetail> QLAResponse { get; set; }
    }

    public class ROTDPhaseDetail
    {

        public long ROTDPhaseDetailId { get; set; }
        public string RequestID { get; set; }
        public string EmployeeNo { get; set; }
        public long ActivityID { get; set; }
        public string ActivityType { get; set; }
        public string SequencePosition { get; set; }
        public string FosRAP { get; set; }
        public string CalculateRAP { get; set; }
        public int IsCurrentRAP { get; set; }
        public Nullable<int> LanguageID { get; set; }
        public DateTime SequenceOriginationDate { get; set; }
        public List<ROTDContractSections> ContractSections { get; set; }

    }
    public class QLARequestActivityType
    {
        public const string Seq = "Seq";
        public const string Stb = "Stb";
    }
    public class ROTDSubPhase
    {
        public long subPhaseId { get; set; }
        public string subPhase { get; set; }

    }

    public class ROTDContractSections
    {
        public long contractSectionId { get; set; }
        public string contractSection { get; set; }
        public bool isLegal { get; set; }
        public List<ROTDRuleDetails> ruleDetails { get; set; }
    }

    public class ContractSections
    {
        public string ContractSection { get; set; }
        public long ContractSectionsID { get; set; }
        public int LeaglityPhaseID { get; set; }
    }

    public class ROTDRules
    {
        public long ruleId { get; set; }
        public string ruleName { get; set; }
    }

    public class ROTDRuleDetails
    {
        public string ruleName { get; set; }
        public long ruleId { get; set; }
        public bool isLegal { get; set; }
        public string value { get; set; }
        public bool isOver35By7 { get; set; }
        public bool isOver35By7LH { get; set; }

    }

    public class WaiverDetails
    {
        public string QLARule { get; set; }

        public string WaiverTypeDescription { get; set; }
    }

    public class PostQLAMappingDetails
    {
        public long PostQLAMappingID { get; set; }
        public long PostQLAStateID { get; set; }
        public long QLARuleID { get; set; }
        public long ContractSectionsID { get; set; }
        public string ContractSection { get; set; }
        public long LegalityPhaseID { get; set; }
        public string QLARule { get; set; }
        public string PostQLAState { get; set; }
        public string WaiverTypeID { get; set; }
    }

    public class CoTerminalBase
    {
        public int BaseID { get; set; }
        public string BaseCD { get; set; }
        public string BaseName { get; set; }
    }

    public class LegalityQLASupportingData
    {
        public long LegalityQLASupportingDataID { get; set; }
        public Nullable<long> LegalityQLARulesID { get; set; }
        public long RunId { get; set; }
        public long EmployeeId { get; set; }
        public string AffectedType { get; set; }
        public string ContractMonth { get; set; }
        public int ActivityId { get; set; }
        public string ActivityCode { get; set; }
        public string ActivityType { get; set; }
        public string ActivityOriginationDate { get; set; }
        public string PositionCode { get; set; }
        public string PickupContractMonth { get; set; }
        public int PickupActivityId { get; set; }
        public string PickupActivityCode { get; set; }
        public string PickupActivityType { get; set; }
        public string PickupActivityOriginationDate { get; set; }
        public string PickupPositionCode { get; set; }
        public DateTime StartDateTime { get; set; }
        public string EndDateTime { get; set; }
        public DateTime PickupStartDateTime { get; set; }
        public string PickupEndDateTime { get; set; }
        public Nullable<DateTime> ActivityReportDateTime { get; set; }
        public long LeaglityPhaseID { get; set; }
    }

    public enum ContractSectionsRotd
    {
        _12k2b_Sequence = 1,
        _12K2c_Sequence = 2,
        _12K2c_standby = 3,
        _12K2e = 4,
        _12k2g_Sequence = 5,
        _12k2h_Sequence = 6,
        _12k2f_Sequence_dropFuture = 7,
        _12k2g_Sequence_dropFuture = 8,
        _12k2h_Sequence_dropFuture = 9,
        _12k2e_Volunteer = 10,
        _12K2i_Sequence_dropETB = 11,
        _12K2i_Sequence_dropFuture_dropETB = 12,
        _12Q_Sequence_dropFuture_ResLeg = 23,
        _12k2b_Standby = 14,
        _12K2e_Standby = 15,
        _12Q_Sequence_Assign = 16,
        _12Q_Standby_ResLeg = 17,
        _12Q_Standby_Assign = 18,
        _12Q_Sequence_dropFuture = 19,
        _12Q_Sequence_ResLeg = 20,

        _12k2bq_Sequence = 21,// _12Q_Sequence_Award
        _12k2bq_Standby = 22,// _12Q_Standby_Award
        _12k2eq_Sequence = 24,
        _12k2eq_Standby = 25,
    }

 

    #endregion
    public class BidCrewWaiverType
    {
        public const int Waive35 = 1;
        public const int WaiveHomeBaseRest = 2;
        public const int AwardintoFDGD = 3;
        public const int Allowmultiplesequenceaward = 4;
        public const int AllowDoubleUp = 5;
        public const int AllowLessthanMinimumCallOut = 6;
        public const int AllowPickuponFDGD = 7;
        public const int WaiveReduceRAPToRAPRest = 9;
        public const int WaiveAllowModifiedStartTime = 10;
        public const int LessthanMinimumCallOut = 11;
    }

    public class ROTARuleResults
    {
        public string RuleName { get; set; }

        public int AwardState { get; set; }
        public int AssignState { get; set; }
        public bool AwardValue { get; set; }
        public bool AssignValue { get; set; }
        public string WaiverType { get; set; }
        public bool IsLegal { get; set; }
    }

    public class InterpretRules
    {
        public long RuleID { get; set; }
        public string RuleName { get; set; }
        public int Award { get; set; }
        public int Assign { get; set; }
    }

    public enum CrewStatus
    {
        RRSV = 1,
        LH = 2
    }
    public enum BidMonthIndicator
    {
        CurrentMonth = 1,
        NextMonth = 2,
        PreviousMonth = 3
    }
    [DataContract]
    public class FlightAttendantBidStatus
    {
        [DataMember]
        public long ReservesCrewMemberBidStatusID { get; set; }
        [DataMember]
        public Nullable<double> CalloutTime { get; set; }
        [DataMember]
        public Nullable<double> MaxCredit { get; set; }
        [DataMember]
        public Nullable<double> ActualPayProjection { get; set; }
        [DataMember]
        public Nullable<long> ReservesCrewMemberID { get; set; }
        [DataMember]
        public Nullable<long> CrewStatusID { get; set; }
        [DataMember]
        public Nullable<long> BidMonthIndicatorID { get; set; }
        [DataMember]
        public string ContractMonth { get; set; }
        [DataMember]
        public Nullable<DateTime> ContractMonthStartDate { get; set; }
        [DataMember]
        public Nullable<DateTime> ContractMonthEndDate { get; set; }

    }

    public class QLAErrors
    {
        public int employeeID { get; set; }
        public List<string> errorMessages { get; set; }
    }

    public class LegalityErrors
    {
        public string employeeId { get; set; }
        public long activityId { get; set; }
        public string activityType { get; set; }
        public string appType { get; set; }
        public string errorMessage { get; set; }
    }

    #region ETB Entity

    public enum EventTypes { Vacation = 1, Sequence, DaysOff }
    public enum TradeTypes { Drop = 1, Pickup, Swap }
    public enum TradeVisibilities { Private = 1, Public, Crew }

    public class LegalityETBContextual
    {
        public string TradeID { get; set; }
        public string TradeResponseID { get; set; }
        public string EMPID { get; set; }
        public bool isLegal { get; set; }
        public string FailedRules { get; set; }
        public string FailerCode { get; set; }
    }

    public class LegalityUIETBContextual
    {
        public string EmpNo { get; set; }
        public string Activity { get; set; }//sequence, vacation, offday
        public string ActivityID { get; set; } //Empty If activity except sequence
        public string Position { get; set; }// Empty If activity except sequence
        public string Report { get; set; }//Sequence -> report,Vacation, offday-> start date
        public string Trade { get; set; }//Pickup, drop
        public bool isLegal { get; set; }//legal, not legal
        public string FailedRule { get; set; }
    }
    public class DFPData
    {
        public string DfpDate { set; get; }
        public string DfpType { set; get; }
    }

    public class LegalityETBInterpretive
    {
        public string EmpID { get; set; }
        public bool IsLegal { get; set; }
        public List<string> FailedRules { get; set; }
        public string SystemFailure { set; get; }
        public List<string> errorMessage { set; get; }
        public List<ActionsToDo> actionsToDo { set; get; }
        public string lstFOSFA1 { get; set; }
        public string lstFOSFA2 { get; set; }
    }

    public class LegalityETBInterp
    {
        public string EmpID { get; set; }
        public bool IsLegal { get; set; }
        public List<string> FailedRules { get; set; }       
    }

    public class RowLegalityETBInterp
    {
        public string EmpId { get; set; }

        public List<ColumnLegalityETBInterp> Columns { get; set; }
        
    }
    public class ColumnLegalityETBInterp
    {
        public string EmpId { get; set; }
        public bool IsLegal { get; set; }
        public List<string> RowFailedMessage { get; set; }
        public List<string> ColumnFailedMessage { get; set; }
    }
    public class ActionsToDo
    {
        public string action { get; set; }
        public string contractMonth { get; set; }
        public string startDate { get; set; }
        public string endDate { get; set; }
        public string endTime { get; set; }
        public string startTime { get; set; }
        public string type { get; set; }

        public string code { get; set; }
        public string groupCode { get; set; }
        public decimal TradeResponseID { get; set; }        
        public decimal TradeID { get; set; }

    }


    public class LegalityETBRequest
    {
        public string EmployeeId { get; set; }
        public string ActivityType { get; set; }//sequence, vacation, offday
        public string ActivityID { get; set; } //Empty If activity except sequence
        public string Position { get; set; }// Empty If activity except sequence
        public string RequestID { get; set; }
        public string ActivityOriginationDate { get; set; }
        public string AirlineCode { get; set; }
        public string ContractMonth { get; set; }
    }


    #endregion  ETB Entity

    #region [Common]
    public static class Common
    {
        public static string GetAllException(this Exception ex)
        {
            System.Text.StringBuilder sb = new System.Text.StringBuilder();

            try
            {
                if (ex == null)
                    sb.Append("Exception is null");

                while (ex != null)
                {
                    if (!string.IsNullOrEmpty(ex.Message))
                    {
                        if (sb.Length > 0)
                            sb.Append(" Exception Message : ");
                        else
                            sb.Append("Exception Message : ");

                        sb.Append(ex.Message);
                    }

                    if (!string.IsNullOrEmpty(ex.StackTrace))
                    {
                        if (sb.Length > 0)
                            sb.Append(" Stack Trace : ");

                        sb.Append(ex.StackTrace);
                    }

                    ex = ex.InnerException;
                }
            }
            catch (Exception exception)
            {
                sb.Append(exception.Message ?? "");
            }

            return sb.ToString();
        }
    }
    #endregion

    [Serializable]
    public class RunList
    {
        public long RunID { get; set; }
        public string RunName { get; set; }
        public int RunContextID { get; set; }
    }

    public class LegalityQLADetailsTableIDs
    {
        public long LegalityQLADetailsID { get; set; }
        public long ReservesCrewSequenceLegalityID { get; set; }
    }

    //public class FAEqualityComparer : IEqualityComparer<FlightAttendant>
    //{
    //    public bool Equals(FlightAttendant x, FlightAttendant y)
    //    {
    //        return x.EmployeeNumber == y.EmployeeNumber;
    //    }

    //    public int GetHashCode(FlightAttendant obj)
    //    {
    //        unchecked
    //        {
    //            if (obj == null)
    //                return 0;
    //            int hashCode = obj.EmployeeNumber.GetHashCode();
    //            hashCode = (hashCode * 397) ^ obj.EmployeeNumber.GetHashCode();
    //            return hashCode;
    //        }
    //    }
    //}

    //public class SequenceEqualityComparer : IEqualityComparer<Sequence>
    //{
    //    public bool Equals(Sequence x, Sequence y)
    //    {
    //        return x.SequenceID == y.SequenceID;
    //    }

    //    public int GetHashCode(Sequence obj)
    //    {
    //        unchecked
    //        {
    //            if (obj == null)
    //                return 0;
    //            int hashCode = obj.SequenceID.GetHashCode();
    //            hashCode = (hashCode * 397) ^ obj.SequenceID.GetHashCode();
    //            return hashCode;
    //        }
    //    }
    //}

    #region RAP Award
    public class RAPAAwards
    {
        public string FlightAttendant { get; set; }
        public long Seniority { get; set; }
        public string Days { get; set; }

        public string Language { get; set; }
        public string Equip { get; set; }
        public string Visa { get; set; }
        public string ModifiedRAP { get; set; }
        public List<ReserveContextRapCode> contextRap { get; set; }


    }

    public class RAPAGroup
    {
        public int Days { get; set; }
        public string Language { get; set; }

        public string Equipment { get; set; }
        public string Visa { get; set; }

    }

    public class ReserveContextRapCode
    {
        public int contextID { get; set; }
        public string A { get; set; }
        public string B { get; set; }
        public string C { get; set; }
        public string D { get; set; }
    }

    public enum RAPCodes
    {
        A = 1,
        B = 2,
        C = 3,
        D = 4
    }

    #endregion
}

