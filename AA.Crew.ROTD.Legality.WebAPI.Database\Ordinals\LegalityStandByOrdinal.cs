using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityStandByOrdinal
    {
        /* Oridinal variables */

        internal Int32 StandByID;
        internal Int32 Airport_Gate;
        internal Int32 ReportTime;
        internal Int32 CreatedDate;
        internal Int32 MinAVLDays;
        internal Int32 ShiftDurationHrs;
        internal Int32 BaseStation;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.StandByID = sqlDataReader.GetOrdinal("StandByID");
            this.Airport_Gate = sqlDataReader.GetOrdinal("Airport_Gate");
            this.ReportTime = sqlDataReader.GetOrdinal("ReportTime");
            this.CreatedDate = sqlDataReader.GetOrdinal("CreatedDate");
            this.MinAVLDays = sqlDataReader.GetOrdinal("MinAVLDays");
            this.ShiftDurationHrs = sqlDataReader.GetOrdinal("ShiftDurationHrs");
            this.BaseStation = sqlDataReader.GetOrdinal("BaseStation");


            this.Initialized = true;
        }
    }
}
