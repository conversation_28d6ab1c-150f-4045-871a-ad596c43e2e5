using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityCrewSequence_Result
    {
        public long ReservesCrewSequenceLegalityID { get; set; }
        public long ReservesCrewMemberID { get; set; }
        public long RunID { get; set; }
        public long SequencePositionDetailsID { get; set; }
        public int StandByID { get; set; }
        public bool IsLegal { get; set; }
        public bool IsOver35By7 { get; set; }
        public long SequenceID { get; set; }
        public int SequenceNumber { get; set; }
        public string SequencePosition { get; set; }
        public long EmployeeID { get; set; }
        public Nullable<System.TimeSpan> ShiftStartTime { get; set; }
        public string ShiftDurationHrs { get; set; }
    }
}
