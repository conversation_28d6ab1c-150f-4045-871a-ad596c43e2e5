using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityReservesCrewMemberActivity_Result
    {
        public long ReservesCrewMemberActivityID { get; set; }
        public Nullable<long> ReservesCrewMemberActivityTypeID { get; set; }
        public string ActivityCode { get; set; }
        public Nullable<System.DateTime> StartDateTime { get; set; }
        public Nullable<System.DateTime> EndDateTime { get; set; }
        public Nullable<System.DateTime> HomeBaseFAReducedRestEndTime { get; set; }
        public Nullable<System.DateTime> HomeBaseRestEndTime { get; set; }
        public Nullable<int> DurationInDays { get; set; }
        public Nullable<long> ReservesCrewMemberID { get; set; }
        public long EmployeeID { get; set; }
        public string ActivityType { get; set; }
    }
}
