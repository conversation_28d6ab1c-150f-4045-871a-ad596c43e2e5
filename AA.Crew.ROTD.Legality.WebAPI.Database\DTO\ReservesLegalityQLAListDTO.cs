using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class ReservesLegalityQLAListDTO
    {
        public long LegalityQLADetailsID { get; set; }
        public bool IsLegal { get; set; }
        public bool IsContractual { get; set; }
        public bool IsQualified { get; set; }
        public string Request { get; set; }
        public string Response { get; set; }
        public long CreatedBy { get; set; }
        public DateTime CreateDate { get; set; }
        public long UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public long ReservesCrewSequenceLegalityID { get; set; }
        public int LegalityPhaseID { get; set; }
        public int FosRAP { get; set; }
        public bool IsCurrentRAP { get; set; }
        public int LanguageID { get; set; }

    }
}
