﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class LegalityReservesCrewMembersDTO
    {
        public int EmployeeID { get; set; }
        public int SeniorityNumber { get; set; }
        public long RunID { get; set; }
        public int AVLDays { get; set; }
        public int ASGSequence { get; set; }
        public bool IsVolunteer { get; set; }
        public long ReservesCrewMemberID { get; set; }
        public string Name { get; set; }
        public bool IsSick { get; set; }
        public int ASGDays { get; set; }
        public int AVLDaysWithFT { get; set; }
        public int ASGDaysWithFT { get; set; }
        public int ASGStandby { get; set; }
        public long RunExcludeReasonID { get; set; }
        public bool IsAggressive { get; set; }
        public bool ETBonFD { get; set; }
    }
}
