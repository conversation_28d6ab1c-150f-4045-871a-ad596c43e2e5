﻿using AA.Crew.ROTD.Legality.WebAPI.Database.Factories;
using AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces;
using AA.Crew.ROTD.Legality.WebAPI.Database.Provider;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Factories
{
    public class DataProviderFactory : IDataProviderFactory
    {
        private IDataAdaptorFactory Factory = null;
        private ILogger<LegalityInterpretiveDataProvider> _logingProvider = null;
        public DataProviderFactory(IDataAdaptorFactory factory, ILogger<LegalityInterpretiveDataProvider> loggingProvider)
        {
            this.Factory = factory;
            this._logingProvider = loggingProvider;
        }

        #region AppSettings
        public IAppSettingsDataProvider GetAppSettingsDataProvider()
        {
            return new AppSettingsDataProvder(this.Factory);
        }
        #endregion AppSettings

        #region ROTDInterpretiveDataProvider
        public IInterpretiveDataProvider GetROTDInterpretiveDataProvider() 
        {
            return new InterpretiveDataProvider(this.Factory);
        }

        public ILegalityInterpretiveDataProvider GetLegalityInterpretiveDataProvider()
        {
            return new LegalityInterpretiveDataProvider(this.Factory, this._logingProvider);
        }
        #endregion ROTDInterpretiveDataProvider
    }
}
