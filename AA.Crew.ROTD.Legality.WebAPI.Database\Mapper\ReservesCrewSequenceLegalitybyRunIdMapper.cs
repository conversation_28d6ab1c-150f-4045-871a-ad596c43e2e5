using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class ReservesCrewSequenceLegalitybyRunIdMapper : MapperBase<List<ReservesCrewSequenceLegalitybyRunIdDTO>, List<ReservesCrewSequenceLegality>>
    {
        public override List<ReservesCrewSequenceLegality> Map(List<ReservesCrewSequenceLegalitybyRunIdDTO> reservesCrewSequenceLegalitybyRunIdDtoList)
        {
            try
            {
                return reservesCrewSequenceLegalitybyRunIdDtoList.Select(reservesCrewSequenceLegalitybyRunIdDto => new ReservesCrewSequenceLegality
                {
                    ReservesCrewSequenceLegalityID = reservesCrewSequenceLegalitybyRunIdDto.ReservesCrewSequenceLegalityID,
                    ReservesCrewMemberID = reservesCrewSequenceLegalitybyRunIdDto.ReservesCrewMemberID,
                    RunID = reservesCrewSequenceLegalitybyRunIdDto.RunID,
                    SequencePositionDetailsID = reservesCrewSequenceLegalitybyRunIdDto.SequencePositionDetailsID,
                    StandByID = reservesCrewSequenceLegalitybyRunIdDto.StandByID,
                    IsOver35By7 = reservesCrewSequenceLegalitybyRunIdDto.IsOver35By7,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<ReservesCrewSequenceLegalitybyRunIdDTO> Map(List<ReservesCrewSequenceLegality> element)
        {
            throw new NotImplementedException();
        }
    }
}
