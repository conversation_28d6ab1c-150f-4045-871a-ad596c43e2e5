using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityCrewSequenceOrdinal
    {
        /* Oridinal variables */

        internal Int32 ReservesCrewSequenceLegalityID;
        internal Int32 ReservesCrewMemberID;
        internal Int32 RunID;
        internal Int32 SequencePositionDetailsID;
        internal Int32 StandByID;
        internal Int32 IsOver35By7;
        internal Int32 SequenceID;
        internal Int32 SequenceNumber;
        internal Int32 SequencePosition;
        internal Int32 EmployeeID;
        internal Int32 ShiftStartTime;
        internal Int32 ShiftDurationHrs;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.ReservesCrewSequenceLegalityID = sqlDataReader.GetOrdinal("ReservesCrewSequenceLegalityID");
            this.ReservesCrewMemberID = sqlDataReader.GetOrdinal("ReservesCrewMemberID");
            this.RunID = sqlDataReader.GetOrdinal("RunID");
            this.SequencePositionDetailsID = sqlDataReader.GetOrdinal("SequencePositionDetailsID");
            this.StandByID = sqlDataReader.GetOrdinal("StandByID");
            this.IsOver35By7 = sqlDataReader.GetOrdinal("IsOver35By7");
            this.SequenceID = sqlDataReader.GetOrdinal("SequenceID");
            this.SequenceNumber = sqlDataReader.GetOrdinal("SequenceNumber");
            this.SequencePosition = sqlDataReader.GetOrdinal("SequencePosition");
            this.EmployeeID = sqlDataReader.GetOrdinal("EmployeeID");
            this.ShiftStartTime = sqlDataReader.GetOrdinal("ShiftStartTime");
            this.ShiftDurationHrs = sqlDataReader.GetOrdinal("ShiftDurationHrs");


            this.Initialized = true;
        }
    }
}
