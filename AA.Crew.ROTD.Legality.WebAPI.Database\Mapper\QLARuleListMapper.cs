using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class QLARuleListMapper : MapperBase<List<QLARuleListDTO>, List<LegalityQLARule>>
    {
        public override List<LegalityQLARule> Map(List<QLARuleListDTO> qlaRuleListDtoList)
        {
            try
            {
                return qlaRuleListDtoList.Select(qlaRuleListDto => new LegalityQLARule
                {
                    LegalityQLARulesID = qlaRuleListDto.LegalityQLARulesID,
                    LegalityQLADetailsID = qlaRuleListDto.LegalityQLADetailsID,
                    Result = qlaRuleListDto.Result,
                    QLARuleName = qlaRuleListDto.QLARuleName,
                    QLARuleID = qlaRuleListDto.QLARuleID,
                    Message = qlaRuleListDto.Message,
                    SequenceID = qlaRuleListDto.SequenceID
                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<QLARuleListDTO> Map(List<LegalityQLARule> element)
        {
            throw new NotImplementedException();
        }
    }
}
