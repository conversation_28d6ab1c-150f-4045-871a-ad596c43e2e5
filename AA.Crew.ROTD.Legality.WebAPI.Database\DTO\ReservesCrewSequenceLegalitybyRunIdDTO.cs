using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class ReservesCrewSequenceLegalitybyRunIdDTO
    {
        public long ReservesCrewSequenceLegalityID { get; set; }
        public long ReservesCrewMemberID { get; set; }
        public long RunID { get; set; }
        public long SequencePositionDetailsID { get; set; }
        public int StandByID { get; set; }
        public bool IsOver35By7 { get; set; }

    }
}
