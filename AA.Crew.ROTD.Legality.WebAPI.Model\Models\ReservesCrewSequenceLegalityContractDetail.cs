using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class ReservesCrewSequenceLegalityContractDetail
    {
        public long ReservesCrewSequenceLegalityContractDetailsID { get; set; }
        public Nullable<long> ContractSectionsID { get; set; }
        public Nullable<long> LegalityPhaseID { get; set; }
        public Nullable<bool> IsLegal { get; set; }
        public Nullable<long> ReservesCrewSequenceLegalityID { get; set; }
        public Nullable<int> FosRAP { get; set; }
        public Nullable<int> CalcRAP { get; set; }
        public Nullable<int> LanguageID { get; set; }
        public Nullable<bool> IsCurrentRAP { get; set; }
        public Nullable<bool> IsOver35By7 { get; set; }
        public Nullable<bool> IsOver35By7LH { get; set; }
    
        public virtual ContractSection ContractSection { get; set; }
        public virtual LegalityPhas LegalityPhas { get; set; }
        public virtual ReservesCrewSequenceLegality ReservesCrewSequenceLegality { get; set; }
        public virtual ReservesCrewSequenceLegalityContractDetail ReservesCrewSequenceLegalityContractDetails1 { get; set; }
        public virtual ReservesCrewSequenceLegalityContractDetail ReservesCrewSequenceLegalityContractDetail1 { get; set; }
        public virtual RapCode RapCode { get; set; }
        public virtual RapCode RapCode1 { get; set; }
    }
}
