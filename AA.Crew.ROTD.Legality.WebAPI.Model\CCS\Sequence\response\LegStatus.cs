﻿namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Sequence.Response
{
    public enum LegStatus
    {
        ACTIVE,
        CANCELLED,
        NEXT_LEG_FAILED_CONTINUITY,
        SHORT_DP,
        CARRY_OVER,
        START_OF_DP,
        END_OF_DP,
        END_OF_SEQ,
        REMOVED,
        STUB, G<PERSON>UND_INTERRUPT,
        AIR_INTERRUPT, DIVERSION,
        DEADHEAD, OAL_DEADHEAD,
        SURFACE,
        DIVERT_CONTINUE,
        RAMP_CONGESTION_DELAY,
        ATC_DELAY,
        DEICING_DELAY,
        DEADHEAD_BOARDING,
        LANG_PREM_PAY,
        TRAINING,
        IPD,
        UNKNOWN
    }
}
