using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityQLAMessage
    {
        public long LegalityQLAMessageID { get; set; }
        public long RunID { get; set; }
        public long LegalityPhaseID { get; set; }
        public string QLARequest { get; set; }
        public string QLAResponse { get; set; }
        public long CreatedBy { get; set; }
        public System.DateTime CreatedDate { get; set; }
    }
}
