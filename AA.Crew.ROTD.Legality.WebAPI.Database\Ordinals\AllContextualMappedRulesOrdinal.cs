using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct AllContextualMappedRulesOrdinal
    {
        /* Oridinal variables */

        internal Int32 RuleTypeID;
        internal Int32 RuleTypeName;
        internal Int32 RuleID;
        internal Int32 RuleName;
        internal Int32 RuleClass;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.RuleTypeID = sqlDataReader.GetOrdinal("RuleTypeID");
            this.RuleTypeName = sqlDataReader.GetOrdinal("RuleTypeName");
            this.RuleID = sqlDataReader.GetOrdinal("RuleID");
            this.RuleName = sqlDataReader.GetOrdinal("RuleName");
            this.RuleClass = sqlDataReader.GetOrdinal("RuleClass");


            this.Initialized = true;
        }
    }
}
