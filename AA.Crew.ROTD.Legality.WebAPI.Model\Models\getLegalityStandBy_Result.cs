using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityStandBy_Result
    {
        public int StandByID { get; set; }
        public string Airport_Gate { get; set; }
        public System.TimeSpan ReportTime { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public int MinAVLDays { get; set; }
        public string ShiftDurationHrs { get; set; }
        public string BaseStation { get; set; }
    }
}
