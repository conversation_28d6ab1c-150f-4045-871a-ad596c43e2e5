﻿using AA.Crew.ROTD.Legality.WebAPI.Model.DBEntities;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.Legalities.ROTD.Data.Interfaces
{
    public interface IMappedRules
    {
        List<PostQLAMapping> GetROTDLegalityRules();
        ROTDContextualRuleSet GetAllContextualMappedRules(string applicationType);
        List<LegalityContractSection> GetLegalitiesROTDContractSection();
        List<WaiverTypeDetails> GetLegalitiesWaiverType();
    }
}
