﻿using AA.Crew.Reserves.QLA.Client;
using IServiceCallFactory = AA.Crew.Reserves.QLA.Client.IServiceCallFactory;
using ServiceCallFactory = AA.Crew.Reserves.QLA.Client.ServiceCallFactory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AA.Crew.Reserves.QLA.Model.Request;
using AA.Crew.Reserves.QLA.Model.Response;
using Newtonsoft.Json;

namespace AA.Crew.ROTD.Legality.WebAPI.Service
{
    public class LegalityService : ILegalityService
    {
        private IServiceCallFactory serviceCallFactory = null;
        private readonly object lockObject = new object();
        private StringBuilder qlaRequestTimeLogString = new StringBuilder();
        private LegalitiesQLAResponse objLegalitiesQLAResponse = null;
        private Dictionary<string, string> qlaRequesResponseDictionary = null;
        private List<LegalitiesQLARequest> failedLegalitiesRequests = null;

        public LegalityService(IServiceCallFactory callFactory)
        {
            serviceCallFactory = callFactory;
        }

        public async Task<LegalitiesQLAResponse> ValidateLegality(LegalitiesQLARequest request, string ccsQlaEndPoint)
        {
            try
            {
                LegalitiesQLAResponse splitQLAResponse = new LegalitiesQLAResponse();
                var qlaService = serviceCallFactory.GetQLAService(ccsQlaEndPoint);
                if (request != null)
                {
                    var res = qlaService.ValidateLegality(request).GetAwaiter().GetResult();
                    splitQLAResponse = res.Result;
                    splitQLAResponse.RawRequest = res.RawRequest;
                    splitQLAResponse.RawResponse = res.RawResponse;
                }

                return splitQLAResponse;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
