using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct QLARuleListOrdinal
    {
        /* Oridinal variables */

        internal Int32 LegalityQLARulesID;
        internal Int32 LegalityQLADetailsID;
        internal Int32 Result;
        internal Int32 QLARuleName;
        internal Int32 RowNumber;
        internal Int32 QLARuleID;
        internal Int32 Message;
        internal Int32 SequenceID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.LegalityQLARulesID = sqlDataReader.GetOrdinal("LegalityQLARulesID");
            this.LegalityQLADetailsID = sqlDataReader.GetOrdinal("LegalityQLADetailsID");
            this.Result = sqlDataReader.GetOrdinal("Result");
            this.QLARuleName = sqlDataReader.GetOrdinal("QLARuleName");
            this.RowNumber = sqlDataReader.GetOrdinal("RowNumber");
            this.QLARuleID = sqlDataReader.GetOrdinal("QLARuleID");
            this.Message = sqlDataReader.GetOrdinal("Message");
            this.SequenceID = sqlDataReader.GetOrdinal("SequenceID");

            this.Initialized = true;
        }
    }
}
