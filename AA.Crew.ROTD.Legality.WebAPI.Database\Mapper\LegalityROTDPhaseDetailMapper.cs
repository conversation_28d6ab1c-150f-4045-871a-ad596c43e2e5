using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    //public class LegalityROTDPhaseDetailMapper : MapperBase<List<LegalityROTDPhaseDetailDTO>, List<LegalityROTDPhaseDetail>>
    //{
    //    public override List<LegalityROTDPhaseDetail> Map(List<LegalityROTDPhaseDetailDTO> legalityROTDPhaseDetailDtoList)
    //    {
    //        try
    //        {
    //            return legalityROTDPhaseDetailDtoList.Select(legalityROTDPhaseDetailDto => new LegalityROTDPhaseDetail
    //            {
    //                ReservesCrewSequenceLegalityID = legalityROTDPhaseDetailDto.ReservesCrewSequenceLegalityID,
    //                ReservesCrewMemberID = legalityROTDPhaseDetailDto.ReservesCrewMemberID,
    //                RunID = legalityROTDPhaseDetailDto.RunID,
    //                SequencePositionDetailsID = legalityROTDPhaseDetailDto.SequencePositionDetailsID,
    //                StandbyID = legalityROTDPhaseDetailDto.StandbyID,
    //                IsOver35By7 = legalityROTDPhaseDetailDto.IsOver35By7,


    //            }).ToList();
    //        }
    //        catch (Exception)
    //        {
    //            throw;
    //        }
    //    }

    //    public override List<LegalityROTDPhaseDetailDTO> Map(List<LegalityROTDPhaseDetail> element)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
}
