using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class RapCodeMapper : MapperBase<List<RapCodeDTO>, List<RapCode>>
    {
        public override List<RapCode> Map(List<RapCodeDTO> rapCodeDtoList)
        {
            try
            {
                return rapCodeDtoList.Select(rapCodeDto => new RapCode
                {
                    RapCodeID = rapCodeDto.RapCodeID,
                    RapCode1 = rapCodeDto.RapCode,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<RapCodeDTO> Map(List<RapCode> element)
        {
            throw new NotImplementedException();
        }
    }
}
