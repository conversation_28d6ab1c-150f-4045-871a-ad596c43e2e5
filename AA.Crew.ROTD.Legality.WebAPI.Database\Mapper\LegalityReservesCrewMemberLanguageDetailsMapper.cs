using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityReservesCrewMemberLanguageDetailsMapper : MapperBase<List<LegalityReservesCrewMemberLanguageDetailsDTO>, List<FALanguageDetails>>
    {
        public override List<FALanguageDetails> Map(List<LegalityReservesCrewMemberLanguageDetailsDTO> legalityReservesCrewMemberLanguageDetailsDtoList)
        {
            try
            {
                return legalityReservesCrewMemberLanguageDetailsDtoList.Select(legalityReservesCrewMemberLanguageDetailsDto => new FALanguageDetails
                {
                    FALang = new Language { LanguageID = legalityReservesCrewMemberLanguageDetailsDto.LanguageID },
                    IsExcluded = legalityReservesCrewMemberLanguageDetailsDto.IsExcluded,
                    FA = new FlightAttendant { EmployeeNumber = Convert.ToInt32(legalityReservesCrewMemberLanguageDetailsDto.EmployeeID) }

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityReservesCrewMemberLanguageDetailsDTO> Map(List<FALanguageDetails> element)
        {
            throw new NotImplementedException();
        }
    }
}
