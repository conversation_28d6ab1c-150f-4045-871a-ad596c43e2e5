﻿using AventStack.ExtentReports;
using AventStack.ExtentReports.Reporter;
using System.Collections.Generic;
using System.IO;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests.Helpers
{
    public enum TestStatus
    {
        Pass = 0,
        Fail = 1,
        Fatal = 2,
        Error = 3,
        Warning = 4,
        Info = 5,
        Skip = 6,
        Debug = 7
    }

    public class ReportingHelpers
    {
        // protected IWebDriver _driver;

        public static ExtentReports extent;
        public static ExtentTest test;
        public ExtentTest childTest;

        //static string _reportsPath = @"Reports\\";
        static string screenshotFolderPath;// = @"Reports\Screenshots";

        public ReportingHelpers()
        {

        }

        public ReportingHelpers(string reportsPath)
        {
            //var dir = Directory.CreateDirectory(reportsPath);
            //screenshotFolderPath = Path.Combine(dir.FullName, "Screenshots");
            //Directory.CreateDirectory(screenshotFolderPath);

            // Initialize Extent Reports
            extent = new ExtentReports();

            //  Attach HTML Reporter
            var html = new ExtentHtmlReporter(reportsPath);
            html.LoadConfig("Config\\html-config.xml");
            //html.Config.JS = "function docReady(fn) {     if (document.readyState === 'complete' || document.readyState === 'interactive') {         setTimeout(fn, 1);     } else {    document.addEventListener('DOMContentLoaded', fn);    } }    (docReady(function() {$('.text-debug').parent().siblings().css('color', 'red'); }); )(); ";
            extent.AttachReporter(html);

        }

        public void AddSystemInfo(Dictionary<string, string> systemInfo)
        {
            // Add System Info
            foreach (var info in systemInfo)
            {
                extent.AddSystemInfo(info.Key, info.Value);
            }
        }

        public void CreateTest(string categoryName, string testName, string description = "")
        {
            test = extent.CreateTest(testName, description);
            test.AssignCategory(categoryName);
            //test.Log(Status.Info, $"Begin Test:  {testName}");
        }
        public void AssignCategory(string categoryName)
        {
            test.AssignCategory(categoryName);
        }
        public void CreateNode(string nodeName, string description)
        {
            childTest = test.CreateNode(nodeName, description);
            childTest.AssignCategory("ChildTest");
        }
        public void ExitChildNode()
        {
            childTest = null;
        }

        public void FlushReports()
        {
            //Below method cleansup and Writes the Extent Reports to disk.
            extent.Flush();
        }
        public void WriteFormattedLog(string infoMessage, TestStatus status = TestStatus.Info)
        {
            WriteLog("<xmp>" + infoMessage + "</xmp>", status);
        }
        public void WriteLog(string infoMessage, TestStatus status = TestStatus.Info)
        {
            if (string.IsNullOrEmpty(infoMessage)) return;

            //string msg = infoMessage.Replace("[", "[<b>").Replace("]", "</b>]");

            //var match = Regex.Match(msg, "Expected:<(.+?)>. Actual:<(.+?)>");
            //if (match.Success)
            //    msg = msg.Replace("<", "[").Replace(">", "]");

            if (status == TestStatus.Fail)
            {
                if (childTest != null && TestBase.IsChildTestInProgress)
                    childTest.Fail(string.Format("<span style=\"color:red\">{0}</span>", infoMessage));
                else
                    test.Fail(string.Format("<span style=\"color:red\">{0}</span>", infoMessage));
            }
            else if (status == TestStatus.Fail || status == TestStatus.Error || status == TestStatus.Debug)
            {
                if (childTest != null && TestBase.IsChildTestInProgress)
                    childTest.Log((Status)status, string.Format("<span style=\"color:red\">{0}</span>", infoMessage));
                else
                    test.Log((Status)status, string.Format("<span style=\"color:red\">{0}</span>", infoMessage));
            }
            else
            {
                if (childTest != null && TestBase.IsChildTestInProgress)
                    childTest.Log((Status)status, infoMessage);
                else
                    test.Log((Status)status, infoMessage);

            }

        }

        public void WriteError(string infoMessage, bool includeScreenshot = false)
        {
            //test.AddScreenCaptureFromPath(TakeScreenshot());
            test.Error(infoMessage);
        }


    }
}

