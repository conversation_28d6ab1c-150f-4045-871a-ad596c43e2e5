﻿using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.Response;
using System;
using System.Globalization;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests.BusinessTests.TestDataBuilder
{
    public class TestDataBuilder
    {
        internal ContractMonth BuildContractMonth(string contractMonth, string contractMonthType, string startDate, string endDate)
        {
            return new ContractMonth()
            {
                Month = contractMonth,
                StartDate = GetDateFromString(startDate),
                EndDate = GetDateFromString(endDate)
            };
        }


        private DateTime GetDateFromString(string dateString)
        {
            return DateTime.Parse(dateString, null, DateTimeStyles.RoundtripKind);
        }
    }
}
