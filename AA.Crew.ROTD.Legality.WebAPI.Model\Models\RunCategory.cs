using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class RunCategory
    {
        public RunCategory()
        {
            this.Runs = new HashSet<Run>();
        }
    
        public long RunCategoryID { get; set; }
        public string RunCategory1 { get; set; }
    
        public virtual ICollection<Run> Runs { get; set; }
    }
}
