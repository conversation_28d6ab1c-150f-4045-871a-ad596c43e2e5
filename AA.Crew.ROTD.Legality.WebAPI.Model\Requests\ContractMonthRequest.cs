﻿using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Request
{
    /// <summary>
    /// Request object to CCS API GetContractMonthForGivenDate
    /// </summary>
    public class ContractMonthRequest
    {
        /// <summary>
        /// Airline Code
        /// </summary>
        public string AirlineCode { get; set; }
        /// <summary>
        /// The date in which the contract month falls
        /// </summary>
        public DateTime Date { get; set; }
    }
}
