﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AA.Crew.Reserves.QLA.Client" Version="1.25.10307.105" />
    <PackageReference Include="AA.Crew.Reserves.QLA.Model" Version="1.25.10307.105" />
    <PackageReference Include="AA.Crew.ROMS.Client" Version="1.23.11218.105" />
    <PackageReference Include="AA.Crew.ROMS.ROTA.WebApi.Client" Version="1.24.11003.101" />
    <PackageReference Include="Microsoft.Rest.ClientRuntime" Version="3.0.3" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.7" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AA.Crew.ROTD.Legality.WebAPI.Model\AA.Crew.ROTD.Legality.WebAPI.Model.csproj" />
  </ItemGroup>

</Project>
