﻿using System;
using System.Text.Json.Serialization;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{
    public class EmployeeActivity : IEquatable<EmployeeActivity>
    {
        /// <summary>
        /// Crew member Base
        /// example = "DFW"
        /// </summary>
        public string @base { get; set; }

        /// <summary>
        /// Activity Id
        /// </summary>
        public int? activityId { get; set; }

        /// <summary>
        /// Denotes type of Activity 
        /// example = "A"
        /// </summary>
        public string activityType { get; set; }

        /// <summary>
        /// Activity code
        /// example = "V2"
        /// </summary>
        public string activityCode { get; set; }

        /// <summary>
        /// Activity group code
        /// example = "VC"
        /// </summary>
        public string activityGroupCode { get; set; }

        /// <summary>
        /// Denotes description of Activity
        /// example = "ACCRU VC"
        /// </summary>
        public string activityDescription { get; set; }

        /// <summary>
        /// Denotes type of DFP(DutyFreePeriod)
        /// example = "GD,FD,D24"
        /// </summary>
        public string dfpType { get; set; }

        /// <summary>
        /// Activity start date and time
        /// </summary>
        public DateTime? startDateTime { get; set; }

        /// <summary>
        /// Activity end date and time
        /// </summary>
        public DateTime? endDateTime { get; set; }

        /// <summary>
        /// Denotes duration between startDate and EndDate in terms of days
        /// </summary>
        public int? durationInDays { get; set; }

        /// <summary>
        /// Denotes duration between startDate and EndDate in terms of minutes
        /// </summary>
        public int? durationInMinutes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime? homeBaseRestEndTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime? homeBaseFAReducedRestEndTime { get; set; }

        /// <summary>
        /// Number of standby days
        /// </summary>
        public int numberOfStandByDays { get; set; }

        /// <summary>
        /// Gate Information for StandBy Activity
        /// </summary>
        public string standByGate { get; set; }

        /// <summary>
        /// bool flag to indicate Awarded standby shift
        /// </summary>
        public bool? awardedStandbyShift { get; set; }

        /// <summary>
        /// bool flag to indicate Assigned standby shift
        /// </summary>
        public bool? assignedStandbyShift { get; set; }

        /// <summary>
        /// bool flag to indicate whether crew member is of type red flag
        /// example = "true,false"
        /// </summary>
        public bool? redFlag { get; set; }

        /// <summary>
        /// bool flag to indicate whether crew member is on duty
        /// example = "true,false"
        /// </summary>
        public bool? isDuty { get; set; }

        /// <summary>
        /// 2 Character add code used while assigning sequence (HU) to crew member
        /// </summary>
        public string addCode { get; set; }

        /// <summary>
        /// 2 Character remove code used while removing a crew member from sequence (2G)
        /// </summary>
        public string removeCode { get; set; }

        /// <summary>
        /// Denotes Employee Activity current contractMonth
        /// </summary>
        public string contractMonth { get; set; }

        /// <summary>
        /// Activity start date and time in GMT
        /// </summary>
        public DateTime? activityStartDateTimeInGMT { get; set; }

        /// <summary>
        /// Activity end date and time in GMT
        /// </summary>
        public DateTime? activityEndDateTimeInGMT { get; set; }

        [JsonIgnore]
        public DateTime? originationDate { get; set; }

        /// <summary>
        /// Denotes open ended planned absence indicator code
        /// </summary>
        public string durationCode { get; set; }

        /// <summary>
        /// Provides SignIn Time in GMT
        /// </summary>
        public DateTime? signInTimeInGMT { get; set; }

        /// Provides SignIn Time in CrewBase Time Zone
        /// </summary>
        public DateTime? signInTimeBase { get; set; }


        //// TODO: MIGRATION PENDING : PROPERTY UNAVAILABLE IN CCS CLOUD API REPONSE
        //public string removalCode { get; set; }
        //public string MiscIndicator { get; set; }
        //public string PositionCode { get; set; }
        //public int ActivityIndentifier { get; set; }
        //public bool onSchedule { get; set; }


        public override bool Equals(object obj)
        {
            return Equals(obj as EmployeeActivity);
        }

        public bool Equals(EmployeeActivity other)
        {
            return other != null &&
                   activityCode == other.activityCode &&
                   activityId == other.activityId &&
                   activityType == other.activityType &&
                   originationDate == other.originationDate &&
                   startDateTime == other.startDateTime;
        }

        public override int GetHashCode()
        {
            return Tuple.Create(activityCode, activityId, activityType, originationDate, startDateTime).GetHashCode();
        }

    }
}
