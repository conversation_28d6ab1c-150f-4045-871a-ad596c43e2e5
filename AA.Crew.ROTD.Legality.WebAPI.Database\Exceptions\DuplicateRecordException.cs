﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Exceptions
{
    public class DuplicateRecordException : DataProviderException
    {
        /// <summary>
        /// Represents Duplicate entry
        /// </summary>
        /// <param name="message"></param>
        /// <param name="innerException"></param>
        public DuplicateRecordException(String message, Exception innerException) : base(message, innerException)
        {

        }
    }
}
