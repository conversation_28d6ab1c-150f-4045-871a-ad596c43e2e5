using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    //public class QLARulesWaiverDetailsMapper : MapperBase<List<QLARulesWaiverDetailsDTO>, List<QLARulesWaiverDetails>>
    //{
    //    public override List<QLARulesWaiverDetails> Map(List<QLARulesWaiverDetailsDTO> qlaRulesWaiverDetailsDtoList)
    //    {
    //        try
    //        {
    //            return qlaRulesWaiverDetailsDtoList.Select(qlaRulesWaiverDetailsDto => new QLARulesWaiverDetails
    //            {
    //                QLARulesWaiverDetailsID = qlaRulesWaiverDetailsDto.QLARulesWaiverDetailsID,
    //                WaiverTypeID = qlaRulesWaiverDetailsDto.WaiverTypeID,
    //                QLARuleID = qlaRulesWaiverDetailsDto.QLARuleID,


    //            }).ToList();
    //        }
    //        catch (Exception)
    //        {
    //            throw;
    //        }
    //    }

    //    public override List<QLARulesWaiverDetailsDTO> Map(List<QLARulesWaiverDetails> element)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
}
