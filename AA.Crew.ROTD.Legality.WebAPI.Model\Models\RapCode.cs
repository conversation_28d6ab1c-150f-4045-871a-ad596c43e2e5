using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class RapCode
    {
        public RapCode()
        {
            this.ReservesCrewSequenceLegalityContractDetails = new HashSet<ReservesCrewSequenceLegalityContractDetail>();
            this.ReservesCrewSequenceLegalityContractDetails1 = new HashSet<ReservesCrewSequenceLegalityContractDetail>();
            this.RapCodeTimes = new HashSet<RapCodeTime>();
        }
    
        public int RapCodeID { get; set; }
        public string RapCode1 { get; set; }
    
        public virtual ICollection<ReservesCrewSequenceLegalityContractDetail> ReservesCrewSequenceLegalityContractDetails { get; set; }
        public virtual ICollection<ReservesCrewSequenceLegalityContractDetail> ReservesCrewSequenceLegalityContractDetails1 { get; set; }
        public virtual ICollection<RapCodeTime> RapCodeTimes { get; set; }
    }
}
