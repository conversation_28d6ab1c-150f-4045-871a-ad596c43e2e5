using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityQLADetail
    {
        public LegalityQLADetail()
        {
            this.LegalityPostQLAStatus = new HashSet<LegalityPostQLAStatu>();
            this.LegalityQLARules = new HashSet<LegalityQLARule>();
        }
    
        public long LegalityQLADetailsID { get; set; }
        public bool IsLegal { get; set; }
        public bool IsContractual { get; set; }
        public bool IsQualified { get; set; }
        public string Request { get; set; }
        public string Response { get; set; }
        public long CreatedBy { get; set; }
        public System.DateTime CreateDate { get; set; }
        public long UpdatedBy { get; set; }
        public System.DateTime UpdatedDate { get; set; }
        public Nullable<long> ReservesCrewSequenceLegalityID { get; set; }
        public Nullable<int> LegalityPhaseID { get; set; }
        public Nullable<int> FosRAP { get; set; }
        public Nullable<bool> IsCurrentRAP { get; set; }
        public Nullable<int> LanguageID { get; set; }
    
        public virtual ICollection<LegalityPostQLAStatu> LegalityPostQLAStatus { get; set; }
        public virtual ICollection<LegalityQLARule> LegalityQLARules { get; set; }
        public virtual ReservesCrewSequenceLegality ReservesCrewSequenceLegality { get; set; }
    }
}
