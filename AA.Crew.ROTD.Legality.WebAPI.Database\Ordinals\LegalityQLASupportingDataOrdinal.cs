using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityQLASupportingDataOrdinal
    {
        /* Oridinal variables */

        internal Int32 RunId;
        internal Int32 EmployeeId;
        internal Int32 AffectedType;
        internal Int32 ContractMonth;
        internal Int32 ActivityId;
        internal Int32 ActivityCode;
        internal Int32 ActivityType;
        internal Int32 ActivityOriginationDate;
        internal Int32 PositionCode;
        internal Int32 StartDateTime;
        internal Int32 EndDateTime;
        internal Int32 PickupContractMonth;
        internal Int32 PickupActivityId;
        internal Int32 PickupActivityCode;
        internal Int32 PickupActivityType;
        internal Int32 PickupActivityOriginationDate;
        internal Int32 PickupPositionCode;
        internal Int32 PickupStartDateTime;
        internal Int32 PickupEndDateTime;
        internal Int32 ActivityReportDateTime;
        internal Int32 LegalityPhaseId;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.RunId = sqlDataReader.GetOrdinal("RunId");
            this.EmployeeId = sqlDataReader.GetOrdinal("EmployeeId");
            this.AffectedType = sqlDataReader.GetOrdinal("AffectedType");
            this.ContractMonth = sqlDataReader.GetOrdinal("ContractMonth");
            this.ActivityId = sqlDataReader.GetOrdinal("ActivityId");
            this.ActivityCode = sqlDataReader.GetOrdinal("ActivityCode");
            this.ActivityType = sqlDataReader.GetOrdinal("ActivityType");
            this.ActivityOriginationDate = sqlDataReader.GetOrdinal("ActivityOriginationDate");
            this.PositionCode = sqlDataReader.GetOrdinal("PositionCode");
            this.StartDateTime = sqlDataReader.GetOrdinal("StartDateTime");
            this.EndDateTime = sqlDataReader.GetOrdinal("EndDateTime");
            this.PickupContractMonth = sqlDataReader.GetOrdinal("PickupContractMonth");
            this.PickupActivityId = sqlDataReader.GetOrdinal("PickupActivityId");
            this.PickupActivityCode = sqlDataReader.GetOrdinal("PickupActivityCode");
            this.PickupActivityType = sqlDataReader.GetOrdinal("PickupActivityType");
            this.PickupActivityOriginationDate = sqlDataReader.GetOrdinal("PickupActivityOriginationDate");
            this.PickupPositionCode = sqlDataReader.GetOrdinal("PickupPositionCode");
            this.PickupStartDateTime = sqlDataReader.GetOrdinal("PickupStartDateTime");
            this.PickupEndDateTime = sqlDataReader.GetOrdinal("PickupEndDateTime");
            this.ActivityReportDateTime = sqlDataReader.GetOrdinal("ActivityReportDateTime");
            this.LegalityPhaseId = sqlDataReader.GetOrdinal("LegalityPhaseId");


            this.Initialized = true;
        }
    }
}
