﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class LegalityCrewMemberActivityDTO
    {
        public long ReservesCrewMemberActivityID { get; set; }
        public long ReservesCrewMemberActivityTypeID { get; set; }
        public string ActivityCode { get; set; }
        public Nullable<System.DateTime> StartDateTime { get; set; }
        public Nullable<System.DateTime> EndDateTime { get; set; }
        public Nullable<System.DateTime> HomeBaseFAReducedRestEndTime { get; set; }
        public Nullable<System.DateTime> HomeBaseRestEndTime { get; set; }
        public Nullable<int> DurationInDays { get; set; }
        public long ReservesCrewMemberID { get; set; }
        public long EmployeeID { get; set; }
        public string ActivityType { get; set; }
    }
}
