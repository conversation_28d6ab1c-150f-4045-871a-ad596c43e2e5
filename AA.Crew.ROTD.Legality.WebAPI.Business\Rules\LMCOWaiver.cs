﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    public class LMCOWaiver : IROTDRules
    {
        public string RuleName { get; set; }

        private List<string> retRuleName;
        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faActivity, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate baseDate, ref bool possibleIsLegal, ref string rapCode, ref int IsCurrentRAP, List<QLARuleResult> qlaRuleResult, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleName = new List<string>();
            bool flag = true;

            if (bidCrewWaiver != null)
            {
                // var waiverData = bidCrewWaiver.Where(e => e.CrewMemberId == flightAttendant.EmployeeNumber && e.WaiverTypeID  == BidCrewWaiverType.LessthanMinimumCallOut).Select(x => x.WaiverSupportingData).FirstOrDefault();
                var lmcoWaiver = bidCrewWaiver.Where(e => e.CrewMemberId == flightAttendant.EmployeeNumber && e.WaiverTypeID == BidCrewWaiverType.LessthanMinimumCallOut); //Just check if FA has added Less than minimum callout waiver
                if (lmcoWaiver != null && lmcoWaiver.Any())
                    flag = true;
                else
                    flag = false;
            }
            else
                flag = false;


            if (!flag)
            {
                possibleIsLegal = false;
                retRuleName.Add(ruleName); //Adds rule name LMCO Waiver
            }
            return retRuleName;
        }
    }
}
