using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class BaseDate
    {
        public BaseDate()
        {
            this.Runs = new HashSet<Run>();
            this.RapCodeTimes = new HashSet<RapCodeTime>();
        }
    
        public long BaseDateID { get; set; }
        public int BaseID { get; set; }
        public System.DateTime ProcessingDate { get; set; }
        public Nullable<System.DateTime> StartDateTime { get; set; }
        public Nullable<System.DateTime> EndDateTime { get; set; }
        public Nullable<long> InitiatedEmployeeID { get; set; }
        public string InitiatedFirstName { get; set; }
        public string InitiatedLastName { get; set; }
        public Nullable<long> ModifiedEmployeeID { get; set; }
        public string ModifiedLastName { get; set; }
        public string ModifiedFirstName { get; set; }
        public Nullable<System.DateTime> CancelledProcessingDate { get; set; }
        public Nullable<long> CancelledEmployeeID { get; set; }
        public string CancelledFirstName { get; set; }
        public string CancelledLastName { get; set; }
    
        public virtual Base Base { get; set; }
        public virtual ICollection<Run> Runs { get; set; }
        public virtual ICollection<RapCodeTime> RapCodeTimes { get; set; }
    }
}
