#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

#Depending on the operating system of the host machines(s) that will build or run the containers, the image specified in the FROM statement may need to be changed.
#For more information, please see https://aka.ms/containercompat

#Build command local local:  docker build --progress=plain -t rtdlegalaties:1.0.1 .

# docker build command to build the dockerfile
# --progress=plain  , display logs details
# -t rtdlegalaties:1.0.1 , name of the buuild image and the version
# .   current location where the local dockerfile.
# Do not push to packages built in local to package.aa.com


FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
RUN apt update

WORKDIR /src
RUN echo "Dot net version"
RUN dotnet --version

COPY ["NuGet.Config", "AA.Crew.ROTD.Legality.WebAPI/"]
COPY ["AA.Crew.ROTD.Legality.WebAPI/AA.Crew.ROTD.Legality.WebAPI.csproj", "AA.Crew.ROTD.Legality.WebAPI/"]
COPY ["NuGet.Config", "AA.Crew.ROTD.Legality.WebAPI.Business/"]
COPY ["AA.Crew.ROTD.Legality.WebAPI.Business/AA.Crew.ROTD.Legality.WebAPI.Business.csproj", "AA.Crew.ROTD.Legality.WebAPI.Business/"]
COPY ["NuGet.Config", "AA.Crew.ROTD.Legality.WebAPI.Database/"]
COPY ["AA.Crew.ROTD.Legality.WebAPI.Database/AA.Crew.ROTD.Legality.WebAPI.Database.csproj", "AA.Crew.ROTD.Legality.WebAPI.Database/"]
COPY ["NuGet.Config", "AA.Crew.ROTD.Legality.WebAPI.Model/"]
COPY ["AA.Crew.ROTD.Legality.WebAPI.Model/AA.Crew.ROTD.Legality.WebAPI.Model.csproj", "AA.Crew.ROTD.Legality.WebAPI.Model/"]

COPY . .
WORKDIR "/src/AA.Crew.ROTD.Legality.WebAPI"
RUN dotnet restore "AA.Crew.ROTD.Legality.WebAPI.csproj"

FROM build AS publish
RUN dotnet publish "AA.Crew.ROTD.Legality.WebAPI.csproj" -c Release -o /app/publish --no-restore

COPY EntrustSSL-TLS-RSA-RootCA.crt /app/publish/
COPY Entrust-EVTLS-Issuing-RSA-CA1.crt /app/publish
COPY Entrust-OVTLS-Issuing-RSA-CA1.crt /app/publish


RUN dotnet dev-certs https --clean
RUN dotnet dev-certs https --trust
RUN dotnet dev-certs https -ep /app/publish/AA.Crew.ROTD.Legality.WebAPI.pfx -p R0T@123* -q
RUN chmod -R o+r /app/publish/AA.Crew.ROTD.Legality.WebAPI.pfx

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

COPY --from=publish /app/publish/AA.Crew.ROTD.Legality.WebAPI.pfx /usr/local/share/ca-certificates/

COPY --from=publish /app/publish/EntrustSSL-TLS-RSA-RootCA.crt /usr/local/share/ca-certificates/EntrustSSL-TLS-RSA-RootCA.crt
COPY --from=publish /app/publish/Entrust-EVTLS-Issuing-RSA-CA1.crt /usr/local/share/ca-certificates/Entrust-EVTLS-Issuing-RSA-CA1.crt
COPY --from=publish /app/publish/Entrust-OVTLS-Issuing-RSA-CA1.crt /usr/local/share/ca-certificates/Entrust-OVTLS-Issuing-RSA-CA1.crt


RUN update-ca-certificates
RUN groupadd -g 65532 appgroup && \
    useradd -r -u 65532 -g appgroup appuser
RUN chown -R 65532:65532 /app
USER 65532:65532
ENTRYPOINT ["dotnet", "AA.Crew.ROTD.Legality.WebAPI.dll"]
