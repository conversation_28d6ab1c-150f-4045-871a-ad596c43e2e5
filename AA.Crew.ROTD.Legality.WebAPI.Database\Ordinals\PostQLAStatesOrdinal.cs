using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct PostQLAStatesOrdinal
    {
        /* Oridinal variables */

        internal Int32 PostQLAStateID;
        internal Int32 PostQLAState;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.PostQLAStateID = sqlDataReader.GetOrdinal("PostQLAStateID");
            this.PostQLAState = sqlDataReader.GetOrdinal("PostQLAState");


            this.Initialized = true;
        }
    }
}
