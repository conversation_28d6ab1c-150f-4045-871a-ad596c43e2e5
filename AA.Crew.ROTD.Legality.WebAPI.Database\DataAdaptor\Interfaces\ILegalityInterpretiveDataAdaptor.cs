﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces
{
    public interface ILegalityInterpretiveDataAdaptor : ITransactionalAdaptor
    {
        Task<List<LegalityStandByDTO>> GetLegalityStandBy(long runID, DateTime bidOperatingDate);
        Task<List<QLARuleListDTO>> GetQLARuleList(decimal rulesCount, int phaseID, int runId);
        Task<List<ReservesCrewSequenceLegalityContractDetailsDTO>> GetReservesCrewSequenceLegalityContractDetails(decimal contractCount, int phaseID, int runId);
        Task<List<LegalityReservesCrewMembersDTO>> GetLegalityReservesCrewMembers(long runID);
        Task<List<LegalityCrewMemberActivityDTO>> GetLegalityCrewMemberActivity(long runID);
        Task<List<LegalitySequenceDTO>> GetLegalitySequence(long runID);
        Task<List<LegalityBidCrewWaiverDTO>> GetLegalityBidCrewWaiver(long runID);
        Task<List<LegalityProcessBidCrewWaiverDTO>> GetLegalityProcessBidCrewWaiver(long runID);
        Task<List<LegalityProcessBidCrewWaiverSupportingDataDTO>> GetLegalityProcessBidCrewWaiverSupportingData(long runID);
        Task<List<LegalityProcessAggressiveBidCrewWaiverDTO>> GetLegalityProcessAggressiveBidCrewWaiver(long runID);
        Task<List<LegalityPostQLAMappingDTO>> GetLegalityPostQLAMapping(long runID);
        Task<List<LegalityBidStatusDTO>> GetLegalityBidStatus(long runID);
        Task<List<LegalitySequenceLanguageDetailsDTO>> GetLegalitySequenceLanguageDetails(long runID);
        Task<List<LegalityCrewSequenceByRunIDDTO>> GetLegalityCrewSequenceByRunID(long runID);
        Task<BaseProcessingDateDTO> GetBaseProcessingDate(long runID);
        Task<List<LegalityCrewSequenceDTO>> GetLegalityCrewSequence(long runID);
        Task<List<LegalityQLASupportingDataDTO>> GetLegalityQLASupportingData(long runID);
        Task<long> GetLegalityGetRunId(string baseCD, string processingDate);
        Task<List<AllContextualMappedRulesDTO>> GetAllContextualMappedRules(string applicationType);
        Task<List<WaiverTypeDTO>> getWaiverType();
        Task<List<ReservesCrewSequenceLegalitybyRunIdDTO>> getReservesCrewSequenceLegalitybyRunId(long runId);
        Task<List<RapCodeDTO>> getRapCode();
        Task<List<QLARulesWaiverDetailsDTO>> getQLARulesWaiverDetails();
        Task<List<QLARulesDTO>> getQLARules();
        Task<List<PostQLAStatesDTO>> getPostQLAStates(long runId);
        Task<List<LegalityQLASupportingDataonlyDTO>> getLegalityQLASupportingDataonly(long runId);
        Task<List<LegalityQLASupportingDataVolunteerDTO>> getLegalityQLASupportingDataVolunteer(long runId);
        Task<List<LegalityQLASupportingDataNonVolunteerStandByDTO>> getLegalityQLASupportingDataNonVolunteerStandBy(long runId);
        Task<List<LegalityPhasesDTO>> getLegalityPhases();
        Task<List<ContractSectionsDTO>> getContractSections();
        Task<List<LegalityReservesCrewMemberLanguageDetailsDTO>> getLegalityReservesCrewMemberLanguageDetails(long runId);
        Task<List<ReservesLegalityQLAListDTO>> GetReservesLegalityQLAList(long runId, int phaseId);
        Task<List<ReservesCrewSequenceLegalitybyRunIdDTO>> getReservesLegalityVolunteerList(long runId);
        Task<List<CoTerminalDTO>> getBaseWithCoTerminals();
        void SaveLegalityQLAMessage(long runId, long legalityPhaseId, string rawRequest, string rawResponse, long updatedById, DateTime updatedDate);
        Task<bool> UpdateAwardProgressStepAsInProgressDaily(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime startDateTime);
        Task<bool> UpdateAwardProgressStepAsCompleteDaily(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime endDateTime);
        Task<bool> UpdateAwardProgressStepAsErrorDaily(string baseCode, DateTime processingDate, long awardProgressStepID);
    }
}
