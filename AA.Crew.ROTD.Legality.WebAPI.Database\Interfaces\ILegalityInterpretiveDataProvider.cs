﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using entity = AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using AA.Crew.ROTD.Legality.WebAPI.Model.DBEntities;
using Sequence = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence;
using LegalityQLASupportingData = AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces
{
    public interface ILegalityInterpretiveDataProvider
    {
        Task<List<StandBy>> GetLegalityStandBy(long runID, DateTime bidOperatingDate);
        Task<List<entity.LegalityQLARule>> GetQLARuleList(decimal rulesCount, int phaseID, int runId);
        Task<List<ContractDetailsTable>> GetReservesCrewSequenceLegalityContractDetails(decimal contractCount, int phaseID, int runId);
        Task<List<FlightAttendant>> GetReservesCrewMemberDetails(List<Activity> lstFaActivity, long runID);
        Task<List<Activity>> GetLegalityCrewMemberActivity(long runID);
        Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.Sequence>> GetSequenceDetails(long runID, string baseCd);
        Task<List<BidCrewWaiver>> GetLegalityProcessBidCrewWaiver(long runID);
        Task<List<BidCrewWaiverSupportingData>> GetLegalityProcessBidCrewWaiverSupportingData(long runID);
        Task<List<AggressiveBidCrewWaiver>> GetLegalityProcessAggressiveBidCrewWaiver(long runID);
        Task<List<PostQLAMappingDetails>> GetLegalityPostQLAMapping(long runID);
        Task<List<FlightAttendantBidStatus>> GetLegalityBidStatus(long runID);
        Task<List<SequenceLanguageDetails>> GetLegalitySequenceLanguageDetails(long runID);
        Task<getLegalityBaseProcessingDateByRunID_Result> GetBaseProcessingDate(long runID);
        Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> GetLegalityQLASupportingData(long runID);
        Task<long> GetLegalityGetRunId(string baseCD, string processingDate);
        Task<List<ROTDContextualRule>> GetAllContextualMappedRules(string applicationType);
        //TODO: Update return type
        //Task<List<WaiverTypeDTO>> getWaiverType();
        Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.Entities.ReservesCrewSequenceLegality>> getReservesCrewSequenceLegalitybyRunId(long runId);
        Task<List<RapCode>> getRapCode();
        Task<List<QLARules>> getQLARules();
        Task<List<PostQLAState>> getPostQLAStates(long runId);
        Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataonly(long runId);
        Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataVolunteer(long runId);
        Task<List<AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject.LegalityQLASupportingData>> getLegalityQLASupportingDataNonVolunteerStandBy(long runId);
        Task<List<LegalityPhases>> getLegalityPhases();
        Task<List<ContractSection>> getContractSections();
        Task<List<LegalityQLADetail>> GetReservesLegalityQLAList(long runId, int phaseId);
        void SaveROTDLegality(List<ROTDLegalityContextDetails> contextualDetails, int runId, int phaseId);
        List<RcsDetails> SaveQLADetails(List<Model.BusinessObject.Sequence> sequence, List<StandBy> standBy, List<ROTDLegalityContextDetails> contextualDetails, List<QLAResponse> lstQlaResponse, List<QLARequest> lstQlaRequest, List<QLARuleResult> lstQlaRuleResult, List<RcsDetails> lstRcs, int runId, int phaseID, ROTDPhase lstROTDPhase);
        void SaveROTDInterpretiveLegality(List<Sequence> sequence, List<StandBy> standBy, List<ROTDLegalityContextDetails> contextualDetails, ROTDPhase lstROTDPhase, List<RcsDetails> lstRcs, int runId, int phaseID, List<LegalityQLASupportingData> qlaSupportingData, DateTime processingDate);
        void SetContextDetailsReservesCrewSequenceLegalityID(List<ROTDLegalityContextDetails> contextualDetails, int runId);
        void SaveQLAMessage(Int64 runId, Int64 legalityPhaseId, Dictionary<string, string> qlaRequestResponse);
        void SaveQLAMessage(Int64 runId, Int64 legalityPhaseId, string rawRequest, string rawResponse);
        Task<List<CoTerminalBase>> GetCoTerminalBases();
        Task<bool> UpdateAwardProgressAsInProgress(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime startDateTime);
        Task<bool> UpdateAwardProgressAsComplete(string baseCode, DateTime processingDate, long awardProgressStepID, DateTime endDateTime);
        Task<bool> UpdateAwardProgressAsError(string baseCode, DateTime processingDate, long awardProgressStepID);
    }
}
