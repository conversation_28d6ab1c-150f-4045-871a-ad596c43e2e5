using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct ReservesLegalityQLAListOrdinal
    {
         /* Oridinal variables */ 

        internal Int32 LegalityQLADetailsID;
internal Int32 IsLegal;
internal Int32 IsContractual;
internal Int32 IsQualified;
internal Int32 Request;
internal Int32 Response;
internal Int32 CreatedBy;
internal Int32 CreateDate;
internal Int32 UpdatedBy;
internal Int32 UpdatedDate;
internal Int32 ReservesCrewSequenceLegalityID;
internal Int32 LegalityPhaseID;
internal Int32 FosRAP;
internal Int32 IsCurrentRAP;
internal Int32 LanguageID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.LegalityQLADetailsID = sqlDataReader.GetOrdinal("LegalityQLADetailsID");
this.IsLegal = sqlDataReader.GetOrdinal("IsLegal");
this.IsContractual = sqlDataReader.GetOrdinal("IsContractual");
this.IsQualified = sqlDataReader.GetOrdinal("IsQualified");
this.Request = sqlDataReader.GetOrdinal("Request");
this.Response = sqlDataReader.GetOrdinal("Response");
this.CreatedBy = sqlDataReader.GetOrdinal("CreatedBy");
this.CreateDate = sqlDataReader.GetOrdinal("CreateDate");
this.UpdatedBy = sqlDataReader.GetOrdinal("UpdatedBy");
this.UpdatedDate = sqlDataReader.GetOrdinal("UpdatedDate");
this.ReservesCrewSequenceLegalityID = sqlDataReader.GetOrdinal("ReservesCrewSequenceLegalityID");
this.LegalityPhaseID = sqlDataReader.GetOrdinal("LegalityPhaseID");
this.FosRAP = sqlDataReader.GetOrdinal("FosRAP");
this.IsCurrentRAP = sqlDataReader.GetOrdinal("IsCurrentRAP");
this.LanguageID = sqlDataReader.GetOrdinal("LanguageID");


            this.Initialized = true;
        }
    }
}
