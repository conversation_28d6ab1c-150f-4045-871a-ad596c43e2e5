using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class PostQLAStatesMapper : MapperBase<List<PostQLAStatesDTO>, List<PostQLAState>>
    {
        public override List<PostQLAState> Map(List<PostQLAStatesDTO> postQLAStatesDtoList)
        {
            try
            {
                return postQLAStatesDtoList.Select(postQLAStatesDto => new PostQLAState
                {
                    PostQLAStateID = postQLAStatesDto.PostQLAStateID,
                    PostQLAState1 = postQLAStatesDto.PostQLAState,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<PostQLAStatesDTO> Map(List<PostQLAState> element)
        {
            throw new NotImplementedException();
        }
    }
}
