using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityProcessBidCrewWaiverSupportingDataOrdinal
    {
        /* Oridinal variables */

        internal Int32 ProcessBidCrewWaiverID;
        internal Int32 BaseCoTerminal;
        internal Int32 TimeToDeparture;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.ProcessBidCrewWaiverID = sqlDataReader.GetOrdinal("ProcessBidCrewWaiverID");
            this.BaseCoTerminal = sqlDataReader.GetOrdinal("BaseCoTerminal");
            this.TimeToDeparture = sqlDataReader.GetOrdinal("TimeToDeparture");


            this.Initialized = true;
        }
    }
}
