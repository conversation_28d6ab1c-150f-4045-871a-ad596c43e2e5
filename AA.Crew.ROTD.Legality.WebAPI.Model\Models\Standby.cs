using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class Standby
    {
        public Standby()
        {
            this.ReservesCrewSequenceLegalities = new HashSet<ReservesCrewSequenceLegality>();
        }
    
        public int StandByID { get; set; }
        public int StandByTypeID { get; set; }
        public int GateID { get; set; }
        public int MinAvailDays { get; set; }
        public int ShiftDurationID { get; set; }
        public System.TimeSpan ShiftStartTime { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public Nullable<System.DateTime> ExpiryDate { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public long RunID { get; set; }
    
        public virtual Gate Gate { get; set; }
        public virtual ShiftDuration ShiftDuration { get; set; }
        public virtual StandByType StandByType { get; set; }
        public virtual ICollection<ReservesCrewSequenceLegality> ReservesCrewSequenceLegalities { get; set; }
        public virtual Run Run { get; set; }
    }
}
