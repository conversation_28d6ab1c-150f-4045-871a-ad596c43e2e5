﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    public class NoFDTouch : IROTDRules
    {
        private List<string> retRuleName;
        public string RuleName { get; set; }

        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faActivity, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate baseDate, ref bool possibleIsLegal, ref string rapCode, ref int isCurrentRAP, List<QLARuleResult> qlaRuleResult, bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleName = new List<string>();
            var fDCount = qlaRuleResult == null ? false : qlaRuleResult.Any(s => s.Rule.Trim().ToUpper(CultureInfo.CurrentCulture).Equals(QLARuleConst.TOUCHFD) || s.Rule.Trim().ToUpper(CultureInfo.CurrentCulture).Equals(QLARuleConst.TOUCHLH));
            if (fDCount) return retRuleName;
            possibleIsLegal = false;
            retRuleName.Add(ruleName);
            return retRuleName;
        }
    }
}
