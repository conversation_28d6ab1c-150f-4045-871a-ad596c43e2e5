﻿using AA.Crew.ROTD.Legality.WebAPI.Database;
using AA.Crew.ROTD.Legality.WebAPI.Model;
//using AA.Crew.ROTD.Legality.WebAPI.Model.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.Request;
using AA.Crew.ROTD.Legality.WebAPI.Model.Response;
using AA.Crew.WebClient.Messages;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Interface
{
    public interface IDataLoader
    {
        //ContractMonth getContractMonthForGivenDate(DateTime date);
        //Task<AppSettings> GetAppSettings(string name);
        //void LoadData(int runID, int subPhase, int runContextId);

    }
}
