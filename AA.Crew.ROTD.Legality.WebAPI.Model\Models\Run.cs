using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class Run
    {
        public Run()
        {
            this.ReservesCrewSequenceLegalities = new HashSet<ReservesCrewSequenceLegality>();
            this.Standbies = new HashSet<Standby>();
            this.Sequences = new HashSet<Sequence>();
            this.ReservesCrewMembers = new HashSet<ReservesCrewMember>();
        }
    
        public long RunID { get; set; }
        public string RunName { get; set; }
        public Nullable<long> BaseDateID { get; set; }
        public Nullable<System.DateTime> CreateDate { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public Nullable<System.DateTime> UpdateDate { get; set; }
        public Nullable<System.DateTime> StartDateTime { get; set; }
        public Nullable<System.DateTime> EndDateTime { get; set; }
        public Nullable<long> RunStatusID { get; set; }
        public Nullable<long> RunCategoryID { get; set; }
        public Nullable<long> InitiatedEmployeeID { get; set; }
        public string InitiatedFirstName { get; set; }
        public string InitiatedLastName { get; set; }
        public Nullable<long> ModifiedEmployeeID { get; set; }
        public string ModifiedLastName { get; set; }
        public string ModifiedFirstName { get; set; }
        public Nullable<long> CancelledEmployeeID { get; set; }
        public string CancelledFirstName { get; set; }
        public string CancelledLastName { get; set; }
        public Nullable<int> RunContextID { get; set; }
    
        public virtual BaseDate BaseDate { get; set; }
        public virtual ICollection<ReservesCrewSequenceLegality> ReservesCrewSequenceLegalities { get; set; }
        public virtual RunCategory RunCategory { get; set; }
        public virtual RunStatu RunStatu { get; set; }
        public virtual ICollection<Standby> Standbies { get; set; }
        public virtual ICollection<Sequence> Sequences { get; set; }
        public virtual ICollection<ReservesCrewMember> ReservesCrewMembers { get; set; }
    }
}
