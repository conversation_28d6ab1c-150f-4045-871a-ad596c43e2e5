using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityReservesCrewMemberLanguageDetails_Result
    {
        public int LanguageID { get; set; }
        public Nullable<bool> IsExcluded { get; set; }
        public long EmployeeID { get; set; }
    }
}
