name: Validate catalog-info yaml
on:
  create:
    branches: [ main ]
  push:
    branches: [ main ]
  pull_request:
    types: [opened, synchronize, ready_for_review]
    branches: [ main ]
jobs:
  validate-catalog-info-yaml:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Validate catalog-info yaml
        uses: AAInternal/backstage-catalog-info-yaml-validator@v3
        with:
          path: 'catalog-info.yaml'
