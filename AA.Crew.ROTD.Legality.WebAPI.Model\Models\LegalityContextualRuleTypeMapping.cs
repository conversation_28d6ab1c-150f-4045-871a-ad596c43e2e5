using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class LegalityContextualRuleTypeMapping
    {
        public long LegalityContextualRuleTypeMapID { get; set; }
        public long LegalityContextualRuleTypeID { get; set; }
        public long LegalityContextualRuleID { get; set; }
        public Nullable<short> LegalityContextualRulePriority { get; set; }
        public Nullable<bool> Active { get; set; }
    
        public virtual LegalityContextualRule LegalityContextualRule { get; set; }
        public virtual LegalityContextualRuleType LegalityContextualRuleType { get; set; }
    }
}
