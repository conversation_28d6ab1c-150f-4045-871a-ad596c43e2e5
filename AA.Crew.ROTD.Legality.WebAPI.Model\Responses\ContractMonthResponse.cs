﻿using System;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Response
{
    /// <summary>
    /// Contract month details from CCS
    /// </summary>
    public class ContractMonthResponse : ResponseBase
    {
        /// <summary>
        /// Airline Code
        /// </summary>
        public string airlineCode { get; set; }

        /// <summary>
        /// Contract Month
        /// </summary>
        public string contractMonth { get; set; }

        /// <summary>
        /// End Date
        /// </summary>
        public DateTime endDate { get; set; }

        /// <summary>
        /// Start Date
        /// </summary>
        public DateTime startDate { get; set; }
    }
}
