using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityBidCrewWaiverOrdinal
    {
        /* Oridinal variables */

        internal Int32 BidCrewWaiverID;
        internal Int32 CrewMemberID;
        internal Int32 CreateDate;
        internal Int32 EndDate;
        internal Int32 BidTypeID;
        internal Int32 WaiverTypeID;
        internal Int32 WaiverTypeDescription;
        internal Int32 WTActive;
        internal Int32 BDWActive;
        internal Int32 UpdateDate;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.BidCrewWaiverID = sqlDataReader.GetOrdinal("BidCrewWaiverID");
            this.CrewMemberID = sqlDataReader.GetOrdinal("CrewMemberID");
            this.CreateDate = sqlDataReader.GetOrdinal("CreateDate");
            this.EndDate = sqlDataReader.GetOrdinal("EndDate");
            this.BidTypeID = sqlDataReader.GetOrdinal("BidTypeID");
            this.WaiverTypeID = sqlDataReader.GetOrdinal("WaiverTypeID");
            this.WaiverTypeDescription = sqlDataReader.GetOrdinal("WaiverTypeDescription");
            this.WTActive = sqlDataReader.GetOrdinal("WTActive");
            this.BDWActive = sqlDataReader.GetOrdinal("BDWActive");
            this.UpdateDate = sqlDataReader.GetOrdinal("UpdateDate");


            this.Initialized = true;
        }
    }
}
