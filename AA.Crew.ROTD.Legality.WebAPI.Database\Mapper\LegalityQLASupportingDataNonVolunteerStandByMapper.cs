using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityQLASupportingDataNonVolunteerStandByMapper : MapperBase<List<LegalityQLASupportingDataNonVolunteerStandByDTO>, List<LegalityQLASupportingData>>
    {
        public override List<LegalityQLASupportingData> Map(List<LegalityQLASupportingDataNonVolunteerStandByDTO> legalityQLASupportingData_NonVolunteerStandByDtoList)
        {
            try
            {
                return legalityQLASupportingData_NonVolunteerStandByDtoList.Select(legalityQLASupportingData_NonVolunteerStandByDto => new LegalityQLASupportingData
                {
                    RunId = legalityQLASupportingData_NonVolunteerStandByDto.RunId,
                    EmployeeId = legalityQLASupportingData_NonVolunteerStandByDto.EmployeeId,
                    AffectedType = legalityQLASupportingData_NonVolunteerStandByDto.AffectedType,
                    ContractMonth = legalityQLASupportingData_NonVolunteerStandByDto.ContractMonth,
                    ActivityId = legalityQLASupportingData_NonVolunteerStandByDto.ActivityId,
                    ActivityCode = legalityQLASupportingData_NonVolunteerStandByDto.Activitycode,
                    ActivityType = legalityQLASupportingData_NonVolunteerStandByDto.ActivityType,
                    ActivityOriginationDate = legalityQLASupportingData_NonVolunteerStandByDto.ActivityOriginationDate,
                    PositionCode = legalityQLASupportingData_NonVolunteerStandByDto.PositionCode,
                    PickupContractMonth = legalityQLASupportingData_NonVolunteerStandByDto.PickupContractMonth,
                    PickupActivityId = legalityQLASupportingData_NonVolunteerStandByDto.PickupActivityId,
                    PickupActivityCode = legalityQLASupportingData_NonVolunteerStandByDto.PickupActivityCode,
                    PickupActivityType = legalityQLASupportingData_NonVolunteerStandByDto.PickupActivityType,
                    PickupActivityOriginationDate = legalityQLASupportingData_NonVolunteerStandByDto.PickupActivityOriginationDate,
                    PickupPositionCode = legalityQLASupportingData_NonVolunteerStandByDto.PickupPositionCode,
                    ActivityReportDateTime = legalityQLASupportingData_NonVolunteerStandByDto.ActivityReportDateTime,

                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityQLASupportingDataNonVolunteerStandByDTO> Map(List<LegalityQLASupportingData> element)
        {
            throw new NotImplementedException();
        }
    }
}
