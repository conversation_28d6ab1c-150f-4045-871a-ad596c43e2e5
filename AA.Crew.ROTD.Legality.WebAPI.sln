﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32901.215
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AA.Crew.ROTD.Legality.WebAPI", "AA.Crew.ROTD.Legality.WebAPI\AA.Crew.ROTD.Legality.WebAPI.csproj", "{8CF2A989-0787-4032-BD36-CA85BB00B267}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AA.Crew.ROTD.Legality.WebAPI.Business", "AA.Crew.ROTD.Legality.WebAPI.Business\AA.Crew.ROTD.Legality.WebAPI.Business.csproj", "{242A4CB8-44DE-492E-A9E0-883B34F58A48}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AA.Crew.ROTD.Legality.WebAPI.Model", "AA.Crew.ROTD.Legality.WebAPI.Model\AA.Crew.ROTD.Legality.WebAPI.Model.csproj", "{46A5E4DB-0707-4FB8-8662-E993C846CBEF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AA.Crew.ROTD.Legality.WebAPI.Database", "AA.Crew.ROTD.Legality.WebAPI.Database\AA.Crew.ROTD.Legality.WebAPI.Database.csproj", "{3935C8CA-D2EC-43C3-B13C-85B71BEB8FD7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AA.Crew.ROTD.Legality.WebAPI.Tests", "AA.Crew.ROTD.Legality.WebAPI.Tests\AA.Crew.ROTD.Legality.WebAPI.Tests.csproj", "{C053D195-2B46-47B9-B808-5B2EA3AAAB24}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AA.Crew.ROTD.Legality.WebAPI.Service", "AA.Crew.ROTD.Legality.WebAPI.Service\AA.Crew.ROTD.Legality.WebAPI.Service.csproj", "{C6919919-7049-4B7A-9D28-ADBFDF9BB61F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8CF2A989-0787-4032-BD36-CA85BB00B267}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8CF2A989-0787-4032-BD36-CA85BB00B267}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8CF2A989-0787-4032-BD36-CA85BB00B267}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8CF2A989-0787-4032-BD36-CA85BB00B267}.Release|Any CPU.Build.0 = Release|Any CPU
		{242A4CB8-44DE-492E-A9E0-883B34F58A48}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{242A4CB8-44DE-492E-A9E0-883B34F58A48}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{242A4CB8-44DE-492E-A9E0-883B34F58A48}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{242A4CB8-44DE-492E-A9E0-883B34F58A48}.Release|Any CPU.Build.0 = Release|Any CPU
		{46A5E4DB-0707-4FB8-8662-E993C846CBEF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{46A5E4DB-0707-4FB8-8662-E993C846CBEF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{46A5E4DB-0707-4FB8-8662-E993C846CBEF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{46A5E4DB-0707-4FB8-8662-E993C846CBEF}.Release|Any CPU.Build.0 = Release|Any CPU
		{3935C8CA-D2EC-43C3-B13C-85B71BEB8FD7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3935C8CA-D2EC-43C3-B13C-85B71BEB8FD7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3935C8CA-D2EC-43C3-B13C-85B71BEB8FD7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3935C8CA-D2EC-43C3-B13C-85B71BEB8FD7}.Release|Any CPU.Build.0 = Release|Any CPU
		{C053D195-2B46-47B9-B808-5B2EA3AAAB24}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C053D195-2B46-47B9-B808-5B2EA3AAAB24}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C053D195-2B46-47B9-B808-5B2EA3AAAB24}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C053D195-2B46-47B9-B808-5B2EA3AAAB24}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6919919-7049-4B7A-9D28-ADBFDF9BB61F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6919919-7049-4B7A-9D28-ADBFDF9BB61F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6919919-7049-4B7A-9D28-ADBFDF9BB61F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6919919-7049-4B7A-9D28-ADBFDF9BB61F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2B583316-74FE-4135-B81B-8829BEBCD563}
	EndGlobalSection
EndGlobal
