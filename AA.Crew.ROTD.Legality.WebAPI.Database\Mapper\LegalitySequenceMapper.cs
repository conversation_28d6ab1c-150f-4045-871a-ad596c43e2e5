using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalitySequenceMapper : MapperBase<List<LegalitySequenceDTO>, List<Sequence>>
    {
        public override List<Sequence> Map(List<LegalitySequenceDTO> legalitySequenceDtoList)
        {
            try
            {
                return legalitySequenceDtoList.Select(legalitySequenceDto => new Sequence
                {
                    SequenceID = legalitySequenceDto.SequenceID,
                    SequenceNumber = Convert.ToInt64(legalitySequenceDto.SequenceNumber),
                    SequencePosition = legalitySequenceDto.SequencePosition,
                    SequencePositionDetailsID = legalitySequenceDto.SequencePositionDetailsID,
                    NoOfDays = legalitySequenceDto.DurationInDays.Value,
                    OriginationDate = legalitySequenceDto.OriginationDate.Value,
                    SequenceStartDateTime = legalitySequenceDto.SequenceDepartureDateTime.Value,
                    SequenceReportDateTime = legalitySequenceDto.SequenceStartDateTime.Value,
                    SequenceEndDateTime = legalitySequenceDto.SequenceEndDateTime.Value,
                    CreditThisMonth = legalitySequenceDto.TotalCreditCurrentMonth.Value,
                    CreditNextMonth = legalitySequenceDto.TotalCreditNextMonth.Value,
                    SatelliteStation = (legalitySequenceDto.SatelliteStation != null && legalitySequenceDto.SatelliteStation != string.Empty) ? true : false,
                    CoTerminalStation = (legalitySequenceDto.CoTerminalStation != null && legalitySequenceDto.CoTerminalStation != string.Empty) ? true : false,
                    StartBase = legalitySequenceDto.CoTerminalStation != null ? legalitySequenceDto.CoTerminalStation : legalitySequenceDto.SatelliteStation != null ? legalitySequenceDto.SatelliteStation : "base"
                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalitySequenceDTO> Map(List<Sequence> element)
        {
            throw new NotImplementedException();
        }
    }
}
