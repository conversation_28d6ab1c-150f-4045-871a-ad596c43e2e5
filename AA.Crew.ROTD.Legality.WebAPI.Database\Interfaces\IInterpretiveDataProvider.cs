﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Interfaces
{
    public interface IInterpretiveDataProvider
    {
        //int RunId { get; set; }
        //List<getLegalityQLASupportingData_Result> GetLegalityQLASupportingData(int runId);
        //List<RcsDetails> SaveQLADetails(List<Entities.Sequence> sequence, List<StandBy> standBy, List<ROTDLegalityContextDetails> contextualDetails, List<QLAResponse> lstQlaResponse, List<QLARequest> lstQlaRequest, List<QLARuleResult> lstQlaRuleResult, List<RcsDetails> lstRcs, int runId, int phaseID, ROTDPhase lstROTDPhase);
        //List<LegalityPhases> GetROTDPhases();
        //List<getLegalityReservesCrewMembers_Result> GetLegalityReservesCrewMembers();
        //List<getLegalityReservesCrewMemberActivity_Result> GetLegalityCrewMemberActivity();
        Task<List<Activity>> GetLegalityCrewMemberActivity(Int64 runId);
        //List<getLegalitySequence_Result> GetLegalitySequence();
        //List<getLegalityStandBy_Result> GetLegalityStandBy();
        //List<RapCode> GetRAPCodeDetails();


        //List<RapCodeTime> GetRapCodeTimes(DateTime ProcessDate, string BaseCD);
        //List<getLegalityBidCrewWaiver_Result> GetLegalityBidCrewWaiver();
        //List<getLegalityProcessBidCrewWaiver_Result> GetLegalityProcessBidCrewWaiver();
        //List<getLegalityBidCrewWaiverSupportingData_Result> GetLegalityProcessBidCrewWaiverSupportingData();
        //List<getLegalityProcessAggressiveBidCrewWaiver_Result> GetLegalityProcessAggressiveBidCrewWaiver();
        //List<getLegalityReservesCrewMemberBidStatus_Result> GetLegalityBidStatus();
        //List<getLegalitySequenceLanguageDetails_Result> GetLegalitySequenceLanguageDetails();
        //List<getLegalityCrewSequenceByRunID_Result> GetLegalityCrewSequenceByRunID();
        Task<getLegalityBaseProcessingDateByRunID_Result> GetBaseProcessingDate(Int64 runId);
        Task<List<FlightAttendant>> getReservesCrewMemberDetails(List<Activity> lstFaActivity, long runId);
        Task<List<FALanguageDetails>> GetLegalityReservesCrewMemberLanguageDetails();
        Task<List<Model.BusinessObject.Sequence>> getSequenceDetails(string baseCD);
        Task<List<StandBy>> getStandByDetails(DateTime bidOperatingDate);
        Task<List<BidCrewWaiver>> getLegalityProcessBidCrewWaiver();
        Task<List<AggressiveBidCrewWaiver>> getLegalityProcessAggressiveBidCrewWaiver();
        //List<getLegalityCrewSequence_Result> GetLegalityCrewSequence();
        //List<getLegalityReservesCrewMemberLanguageDetails_Result> GetLegalityReservesCrewMemberLanguageDetails();
        //List<OperatingLanguage> GetOperatingLanguagesForRun(long runID);
        //void SaveROTDLegality(List<ROTDLegalityContextDetails> contextualDetails, int runId, int phaseId);        
        //void SaveROTDInterpretiveLegality(List<AA.Crew.Legalities.Entities.Sequence> sequence, List<StandBy> standBy, List<ROTDLegalityContextDetails> contextualDetails, ROTDPhase lstROTDPhase, List<RcsDetails> lstRcs, int runId, int phaseID, List<Entities.LegalityQLASupportingData> qlaSupportingData, DateTime processingDate);
        //void SaveQLAMessage(Int64 runId, Int64 legalityPhaseId, string rawRequest, string rawResponse);
        //List<getLegalityROTDPhaseDetail_Result> getLegalityROTDPhaseDetail(int phaseid, ref bool isRunCompleted);
        //void SetContextDetailsReservesCrewSequenceLegalityID(List<ROTDLegalityContextDetails> contextualDetails, int runId);
        //List<string> GetApplicationSetting(string nameOfSettingToGet);
        //List<PostQLAMappingDetails> GetLegalityPostQLAMapping(int runId);
        //void SaveQLAMessage(Int64 runId, Int64 legalityPhaseId, Dictionary<string, string> qlaRequestResponse);
        //List<getBaseWithCoTerminals_Result> GetBaseCoTerminals();
    }
}
