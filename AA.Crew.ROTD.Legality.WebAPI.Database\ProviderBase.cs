﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Provider
{
    public abstract class ProviderBase<T> where T : ITransactionalAdaptor, IDisposable
    {
        #region Members

        protected bool Disposed = false;


        #endregion Members

        #region Properties

        protected T Adaptor
        {
            get;
            set;
        }


        #endregion Properties

        #region Constructors

        public ProviderBase()
        {

        }

        #endregion Constructors

        #region IDisposable Members

        /// <summary>
        /// Disposes of any SQLTransaction, then Closes and Disposes the SqlConnection.
        /// </summary>
        public virtual void Dispose()
        {
            ApplicationException disposeExcp = null;

            if (!this.Disposed)
            {
                try
                {
                    if (this.Adaptor != null)
                    {
                        this.Adaptor.Dispose();
                    }

                }
                catch (ApplicationException ex)
                {
                    disposeExcp = ex;
                }
                finally
                {
                    this.Disposed = true;
                }
            }

            if (disposeExcp != null)
            {
                throw disposeExcp;
            }
        }

        #endregion
    }
}
