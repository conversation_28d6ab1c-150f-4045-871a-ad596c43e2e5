﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct BaseDateOrdinals
    {
        /* Oridinal variables */
        internal Int32 BaseCD { get; set; }
        internal Int32 ProcessingDate { get; set; }
        internal Boolean Initialized;

        /// <summary>
        /// Determines the ordinal positions for each of the columns in the App Settings
        /// </summary>
        /// <param name="sqlDataReader">The DataReader object to determine the ordinal positions from.</param>
        internal void Initalize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.BaseCD = sqlDataReader.GetOrdinal("BaseCD");
            this.ProcessingDate = sqlDataReader.GetOrdinal("ProcessingDate");

            this.Initialized = true;
        }
    }
}
