﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class LegalityPostQLAMappingDTO
    {
        public long PostQLAMappingID { get; set; }
        public long PostQLAStateID { get; set; }
        public long QLARuleID { get; set; }
        public long ContractSectionsID { get; set; }
        public string ContractSection { get; set; }
        public long LegalityPhaseID { get; set; }
        public string QLARule { get; set; }
        public string PostQLAState { get; set; }
        public string WaiverTypeID { get; set; }
    }
}
