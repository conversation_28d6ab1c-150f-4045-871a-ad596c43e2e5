# Example of a github workflow that can be dropped into
# repos with webapp.yaml files
name: Lint webapp yaml files

on:
  pull_request:
    paths:
    - 'k8s/**'

jobs:
  lint-webapp-yaml:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Lint Webapp yaml
      uses: AAInternal/runway-operator-kopf/.github/actions/runway-operator-linting@main
      with:
        AAINTERNAL_READONLY_PAT: ${{ secrets.AAINTERNAL_READONLY_PAT }}