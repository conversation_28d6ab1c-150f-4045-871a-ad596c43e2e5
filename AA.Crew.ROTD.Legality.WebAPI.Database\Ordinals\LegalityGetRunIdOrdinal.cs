using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct LegalityGetRunIdOrdinal
    {
        /* Oridinal variables */

        internal Int32 RunID;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.RunID = sqlDataReader.GetOrdinal("RunID");

            this.Initialized = true;
        }
    }
}
