﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class QLARuleListDTO
    {
        public long LegalityQLARulesID { get; set; }
        public Nullable<long> LegalityQLADetailsID { get; set; }
        public Nullable<long> QLARuleID { get; set; }
        public string Result { get; set; }
        public string Message { get; set; }
        public string QLARuleName { get; set; }
        public Int32 SequenceID { get; set; }
    }
}
