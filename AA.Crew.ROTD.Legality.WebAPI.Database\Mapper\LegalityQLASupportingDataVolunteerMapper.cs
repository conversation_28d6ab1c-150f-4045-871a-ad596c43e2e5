using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityQLASupportingDataVolunteerMapper : MapperBase<List<LegalityQLASupportingDataVolunteerDTO>, List<LegalityQLASupportingData>>
    {
        public override List<LegalityQLASupportingData> Map(List<LegalityQLASupportingDataVolunteerDTO> legalityQLASupportingData_volunteerDtoList)
        {
            try
            {
                return legalityQLASupportingData_volunteerDtoList.Select(legalityQLASupportingData_volunteerDto => new LegalityQLASupportingData
                {
                    RunId = legalityQLASupportingData_volunteerDto.RunId,
                    EmployeeId = legalityQLASupportingData_volunteerDto.EmployeeId,
                    AffectedType = legalityQLASupportingData_volunteerDto.AffectedType,
                    ContractMonth = legalityQLASupportingData_volunteerDto.ContractMonth,
                    ActivityId = legalityQLASupportingData_volunteerDto.ActivityId,
                    ActivityCode = legalityQLASupportingData_volunteerDto.Activitycode,
                    ActivityType = legalityQLASupportingData_volunteerDto.ActivityType,
                    ActivityOriginationDate = legalityQLASupportingData_volunteerDto.ActivityOriginationDate,
                    PositionCode = legalityQLASupportingData_volunteerDto.PositionCode,
                    PickupContractMonth = legalityQLASupportingData_volunteerDto.PickupContractMonth,
                    PickupActivityId = legalityQLASupportingData_volunteerDto.PickupActivityId,
                    PickupActivityCode = legalityQLASupportingData_volunteerDto.PickupActivityCode,
                    PickupActivityType = legalityQLASupportingData_volunteerDto.PickupActivityType,
                    PickupActivityOriginationDate = legalityQLASupportingData_volunteerDto.PickupActivityOriginationDate,
                    PickupPositionCode = legalityQLASupportingData_volunteerDto.PickupPositionCode,
                    ActivityReportDateTime = legalityQLASupportingData_volunteerDto.ActivityReportDateTime,


                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityQLASupportingDataVolunteerDTO> Map(List<LegalityQLASupportingData> element)
        {
            throw new NotImplementedException();
        }
    }
}
