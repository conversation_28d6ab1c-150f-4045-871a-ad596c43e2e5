﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DTO
{
    public class LegalityStandByDTO
    {
        public int StandByID { get; set; }
        public string Airport_Gate { get; set; }
        public System.DateTime ReportTime { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public int MinAVLDays { get; set; }
        public string ShiftDurationHrs { get; set; }
        public string BaseStation { get; set; }
        public bool CoTerminalStation { get; set; }
        public int Duration { get; set; }
    }
}
