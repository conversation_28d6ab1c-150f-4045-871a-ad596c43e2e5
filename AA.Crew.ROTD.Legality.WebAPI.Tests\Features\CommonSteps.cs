using AA.Crew.ROTD.Legality.WebAPI.Model;
using AA.Crew.ROTD.Legality.WebAPI.Model.Response;
using AA.Crew.ROTD.Legality.WebAPI.Tests.BusinessTests.TestDataBuilder;
using AA.Crew.ROTD.Legality.WebAPI.Tests.Models;
using AA.Crew.ROTD.Legality.WebAPI.Tests.SoftAssert;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using TechTalk.SpecFlow;

namespace AA.Crew.ROTD.Legality.WebAPI.Tests.Features
{
    [Binding]
    public class CommonSteps
    {
        TestDataContext testDataContext;
        public CommonSteps(TestDataContext testDataContext)
        {
            this.testDataContext = testDataContext;
        }

        TestDataBuilder testDataBuilder = new TestDataBuilder();

        #region Feature file Readability

        [Given(@"")]
        [When(@"")]
        [Then(@"")]
        public void ImproveReadabilityOfFeatureFiles()
        {
            // Dummy step. Just to improve readability of .feature files.
        }
        #endregion

        [Given(@"I have read the test case data for '(.*)' type from excel\.")]
        public void GivenIHaveReadTheTestCaseDataForTypeFromExcel_(string p0)
        {
            // ScenarioContext.Current.Pending();
        }

        [Then(@"I conclude all the test cases are executed and verified successfully\.")]
        public void ThenIConcludeAllTheTestCasesAreExecutedAndVerifiedSuccessfully_()
        {
            SoftAssert.SoftAssert.ReviewAll();
        }


        #region Given

        private DateTime GetEndTimeOfTheDate(DateTime dt)
        {
            return new DateTime(dt.Year, dt.Month, dt.Day, 11, 59, 59);
        }



        #endregion
    }
}
