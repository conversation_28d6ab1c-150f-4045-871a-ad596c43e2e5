﻿using AA.Crew.Legalities.ROTD.Data.Interfaces;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public class MappedRules //: IMappedRules
    {
        List<LegalityPhas> lstLegalityPhas = null;
        List<PostQLAMapping> lstPostQLAMapping = null;
        List<ContractSection> lstContractSection = null;
        List<LegalityPhaseContractDetail> lstLegalityPhaseContractDetail = null;
        //public MappedRules()
        //{
        //    //come back
        //    //lstLegalityPhas = objCrewReservesEntities.LegalityPhases.ToList();
        //    //lstContractSection = objCrewReservesEntities.ContractSections.ToList();
        //}

        //public List<PostQLAMapping> GetROTDLegalityRules()
        //{
        //    lstPostQLAMapping = objCrewReservesEntities.PostQLAMappings.ToList();

        //    return lstPostQLAMapping;
        //}

        //ROTDContextualRuleSet contextualRules = null;
        //public ROTDContextualRuleSet GetAllContextualMappedRules(string applicationType)
        //{
        //    contextualRules = new ROTDContextualRuleSet();
        //    try
        //    {
        //        objCrewReservesEntities = new CrewReservesROTDEntities();
        //        SqlParameter ParamAppType = new SqlParameter("@AppType", applicationType);
        //        contextualRules.MappedRules = objCrewReservesEntities.Database.SqlQuery<ROTDContextualRule>("ROTD.getAllContextualMappedRules @AppType", ParamAppType).ToList();

        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }

        //    return contextualRules;
        //}

        //public List<LegalityContractSection> GetLegalitiesROTDContractSection()
        //{
        //    List<LegalityContractSection> lstLegalityRules = new List<LegalityContractSection>();
        //    lstLegalityPhaseContractDetail = objCrewReservesEntities.LegalityPhaseContractDetails.ToList();

        //    lstLegalityRules = (from phases in lstLegalityPhas
        //                        join qlaMapping in lstLegalityPhaseContractDetail
        //                        on phases.LegalityPhaseID equals qlaMapping.LegalityPhaseID
        //                        join objcontract in lstContractSection on
        //                        qlaMapping.ContractSectionsID equals objcontract.ContractSectionsID
        //                        select new LegalityContractSection
        //                        {
        //                            LegalityPhase = phases.LegalityPhase,
        //                            ContractSection = objcontract.ContractSection1,
        //                            ContractSectionsID = objcontract.ContractSectionsID,
        //                            LeaglityPhaseID = (int)phases.LegalityPhaseID
        //                        }).ToList();

        //    return lstLegalityRules;
        //}

        //public List<WaiverTypeDetails> GetLegalitiesWaiverType()
        //{
        //    List<WaiverTypeDetails> lstLegalityWaiverRules = new List<WaiverTypeDetails>();


        //    lstLegalityWaiverRules = (from rules in objCrewReservesEntities.QLARules
        //                              join waiver in objCrewReservesEntities.QLARulesWaiverDetails on rules.QLARuleID equals waiver.QLARuleID
        //                              join waivertype in objCrewReservesEntities.WaiverTypes on waiver.WaiverTypeID equals waivertype.WaiverTypeID
        //                              select new WaiverTypeDetails
        //                              {
        //                                  QLARule = rules.QLARule1,
        //                                  WaiverTypeDescription = waivertype.WaiverTypeDescription
        //                              }

        //                              ).ToList();




        //    return lstLegalityWaiverRules;
        //}

    }
}
