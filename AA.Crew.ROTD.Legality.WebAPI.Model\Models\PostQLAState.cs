using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class PostQLAState
    {
        public PostQLAState()
        {
            this.PostQLAMappings = new HashSet<PostQLAMapping>();
            this.LegalityPostQLAStatus = new HashSet<LegalityPostQLAStatu>();
        }
    
        public long PostQLAStateID { get; set; }
        public string PostQLAState1 { get; set; }
    
        public virtual ICollection<PostQLAMapping> PostQLAMappings { get; set; }
        public virtual ICollection<LegalityPostQLAStatu> LegalityPostQLAStatus { get; set; }
    }
}
