﻿using AA.Crew.ROTD.Legality.WebAPI.Business.Interface;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Business.Rules
{
    public class ActivityIsSequence : IROTDRules
    {
        private List<string> retRuleName;

        public List<string> ExecuteRule(Sequence sequence, List<Bid> bids, FlightAttendant flightAttendant, Activity faActivity, StandBy standby, string ruleName, List<BidCrewWaiver> bidCrewWaiver, List<RAPShifts> rapShifts, BaseDate _base, ref bool possibleIsLegal, ref string rapCode, ref int isCurrentRAP, List<QLARuleResult> qlaRuleResult,  bool IsBaseCoTerminal, List<RAPShifts> previousMonthRAPShifts)
        {
            retRuleName = new List<string>();

            if (sequence != null)
            {
                possibleIsLegal = false;
                retRuleName.Add(ruleName);
            }
            return retRuleName;
        }

        public string RuleName { get; set; }
    }
}
