﻿using AA.Crew.ROTD.Legality.WebAPI.Model.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.DBEntities
{
    public class ROTDContextualRule
    {
        public long RuleID { get; set; }
        public string RuleName { get; set; }
        public long RuleTypeID { get; set; }
        public string RuleTypeName { get; set; }
        public string RuleClass { get; set; }
    }

    public class LegalityRules
    {
        public string LegalityPhase { get; set; }
        public List<LegalityContractSection> lstContractSection { get; set; }
    }
    public class LegalityContractSection
    {
        public string LegalityPhase { get; set; }
        public int LeaglityPhaseID { set; get; }
        public long ContractSectionsID { get; set; }
        public string ContractSection { get; set; }
        public List<LegalityQLARule> lstQLARule { get; set; }
    }

    public class WaiverTypeDetails
    {
        public string QLARule { get; set; }

        public string WaiverTypeDescription { get; set; }
    }
    public class LegalityQLARule
    {
        public long QLARuleID { get; set; }
        public string QLARule { get; set; }
        public LegalityQLARuleState LegalityQLARuleStates { get; set; }
    }
    public class LegalityQLARuleState
    {
        public long QLAStateID { get; set; }
        public string QLAState { get; set; }
    }

    public class ROTDContextualRuleSet
    {
        public List<ROTDContextualRule> MappedRules { get; set; }
        public List<LegalityPhas> lstLegalityPhases { get; set; }
        public List<ContractSection> lstContractSections { get; set; }
    }
}
