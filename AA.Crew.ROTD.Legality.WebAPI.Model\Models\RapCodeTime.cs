using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class RapCodeTime
    {
        public long RapCodeTimeID { get; set; }
        public long BaseDateID { get; set; }
        public int RapCodeID { get; set; }
        public Nullable<System.DateTime> StartTime { get; set; }
        public Nullable<System.DateTime> EndTime { get; set; }
    
        public virtual BaseDate BaseDate { get; set; }
        public virtual RapCode RapCode { get; set; }
    }
}
