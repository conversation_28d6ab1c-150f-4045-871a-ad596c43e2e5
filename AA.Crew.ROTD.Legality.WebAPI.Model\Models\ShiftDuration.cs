using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class ShiftDuration
    {
        public ShiftDuration()
        {
            this.Standbies = new HashSet<Standby>();
        }
    
        public int ShiftDurationID { get; set; }
        public string ShiftDurationHrs { get; set; }
    
        public virtual ICollection<Standby> Standbies { get; set; }
    }
}
