﻿namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Response
{

    public class SickTime
    {
        /// <summary>
        /// Personal vacation day accrued next year
        /// </summary>
        public int vcAccrNextYear { get; set; }

        /// <summary>
        /// Personal vacation day used to date
        /// </summary>
        public int pvdUsed { get; set; }

        ///<summary>
        ///Remaining hours for vac bank trip drop odd year
        ///</summary>
        public int rmngHrsVbankTripDropOdd { get; set; }

        ///<summary>
        ///Remaining hours for vac bank trip drop even year
        ///</summary>
        public int rmngHrsVbankTripDropEvn { get; set; }

        ///<summary>
        ///Available sick Time in Minutes
        ///</summary>
        public int available { get; set; }

        ///<summary>
        ///Sick Time Accrued for Next Year in Minutes
        ///</summary>
        public int accruredNextYear { get; set; }

        ///<summary>
        ///Total Sick Time Used
        ///  ///</summary>
        public int used { get; set; }

        ///<summary>
        ///Sick Time Used MTD in Minutes
        ///</summary>
        public int usedMtd { get; set; }

        /// <summary>
        /// Total Sick Time Made-up in Minutes
        /// </summary>
        public int madeUp { get; set; }


        /// <summary>
        ///Sick Time Made-up MTD in Minutes
        /// </summary>
        public int madeUpMtd { get; set; }

        /// <summary>
        ///Sick Time Accrued at Start of Sick Absence
        /// </summary>
        public int accruedAtAbsenceStart { get; set; }


        /// <summary>
        /// YR for accrued Personal Vacation Day
        /// </summary>
        public int yrVcAccrNextYear { get; set; }

        /// <summary>
        ///CPA Time Balance in minutes
        /// </summary>
        public int cpaBalance { get; set; }

        /// <summary>
        ///Total Eligible Hours for Rapid Re-Accrual
        /// </summary>
        public int eligReAccrRapid { get; set; }

        /// <summary>
        ///Remaining Sick Time to be Rapidly Re-Accrued
        /// </summary>
        public int rmngSickReAccrRapid { get; set; }
    }
}
