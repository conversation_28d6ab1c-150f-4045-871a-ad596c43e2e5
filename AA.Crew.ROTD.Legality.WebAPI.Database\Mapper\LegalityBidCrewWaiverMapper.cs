using AA.Crew.ROTD.Legality.WebAPI.Database.DTO;
using AA.Crew.ROTD.Legality.WebAPI.Model.BusinessObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Mapper
{
    public class LegalityBidCrewWaiverMapper : MapperBase<List<LegalityBidCrewWaiverDTO>, List<BidCrewWaiver>>
    {
        public override List<BidCrewWaiver> Map(List<LegalityBidCrewWaiverDTO> legalityBidCrewWaiverDtoList)
        {
            try
            {
                return legalityBidCrewWaiverDtoList.Select(legalityBidCrewWaiverDto => new BidCrewWaiver
                {
                    BidCrewWaiverId = legalityBidCrewWaiverDto.BidCrewWaiverID,
                    BidTypeId = legalityBidCrewWaiverDto.BidTypeID,
                    CrewMemberId = legalityBidCrewWaiverDto.CrewMemberID,
                    IsActive = legalityBidCrewWaiverDto.BDWActive,
                    StartDate = legalityBidCrewWaiverDto.CreateDate,
                    WaiverTypeID = legalityBidCrewWaiverDto.WaiverTypeID,
                    WaiverTypeDescription = legalityBidCrewWaiverDto.WaiverTypeDescription,
                    
                }).ToList();
            }
            catch (Exception)
            {
                throw;
            }
        }

        public override List<LegalityBidCrewWaiverDTO> Map(List<BidCrewWaiver> element)
        {
            throw new NotImplementedException();
        }
    }
}
