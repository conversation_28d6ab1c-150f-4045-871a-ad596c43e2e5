﻿using AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Common;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Sequence
{
    public class FlightTimes
    {

        private static long serialVersionUID = -8039568693221026176L;

        /// <summary>
        /// FlightLeg Departure DateTime
        /// </summary>
        public CCSDateTime departureDateTime { get; set; }

        /// <summary>
        /// FlightLeg Arrival DateTime
        /// </summary>
        public CCSDateTime arrivalDateTime { get; set; }

        public FlightTimes()
        {
        }

        public FlightTimes(CCSDateTime departureDateTime, CCSDateTime arrivalDateTime)
        {
            this.departureDateTime = departureDateTime;
            this.arrivalDateTime = arrivalDateTime;
        }

    }
}
