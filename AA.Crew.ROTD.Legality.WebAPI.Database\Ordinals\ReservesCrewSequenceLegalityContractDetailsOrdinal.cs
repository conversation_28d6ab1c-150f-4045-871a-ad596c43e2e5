using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.Ordinals
{
    public struct ReservesCrewSequenceLegalityContractDetailsOrdinal
    {
        /* Oridinal variables */

        internal Int32 ReservesCrewSequenceLegalityContractDetailsID;
        internal Int32 ContractSectionsID;
        internal Int32 LegalityPhaseID;
        internal Int32 ReservesCrewSequenceLegalityID;
        internal Int32 LanguageID;
        internal Int32 FosRAP;
        internal Int32 IsCurrentRAP;
        internal Int32 RowNumber;


        internal Boolean Initialized;

        internal void Initialize(System.Data.SqlClient.SqlDataReader sqlDataReader)
        {
            this.ReservesCrewSequenceLegalityContractDetailsID = sqlDataReader.GetOrdinal("ReservesCrewSequenceLegalityContractDetailsID");
            this.ContractSectionsID = sqlDataReader.GetOrdinal("ContractSectionsID");
            this.LegalityPhaseID = sqlDataReader.GetOrdinal("LegalityPhaseID");
            this.ReservesCrewSequenceLegalityID = sqlDataReader.GetOrdinal("ReservesCrewSequenceLegalityID");
            this.LanguageID = sqlDataReader.GetOrdinal("LanguageID");
            this.FosRAP = sqlDataReader.GetOrdinal("FosRAP");
            this.IsCurrentRAP = sqlDataReader.GetOrdinal("IsCurrentRAP");
            this.RowNumber = sqlDataReader.GetOrdinal("RowNumber");


            this.Initialized = true;
        }
    }
}
