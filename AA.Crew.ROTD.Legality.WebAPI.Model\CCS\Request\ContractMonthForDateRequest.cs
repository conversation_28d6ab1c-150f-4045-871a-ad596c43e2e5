﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.CCS.Request
{
    public class ContractMonthForDateRequest
    {
        public ContractMonthForDateRequest(string airlineCode, DateTime date)
        {
            this.airlineCode = airlineCode;
            this.date = date.ToString("yyyy-MM-dd");
        }
        public string airlineCode { get; }
        public string date { get; }
    }
}
