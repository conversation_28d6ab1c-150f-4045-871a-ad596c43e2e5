{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "", "FUNCTIONS_WORKER_RUNTIME": "dotnet", "DBConnectionString": "Persist Security Info=True;data source={Put Connection String}", "AirlineCode": "AA", "USBaseOnly": "true", "BaseToConsiderForTime": "DFW", "ROMSTestDataPath": "TestData\\", "ROMSTestResultsPath": "bin\\Debug\\net6.0\\TestResults\\", "CCSBatchMaxFA": 20, "AsyncBatchCount": 5, "CCSRetryCount": 5, "ROMSAPIURL": "https://localhost:44390/", "MaxTotalAvailableDays": 18}, "webClientConfiguration": {"Services": [{"ServiceName": "ccsCloudDataService", "connectionString": "ccsApigeeConnectionString", "tokenConnectionString": "ccsToken", "ResourceCollection": [{"Name": "ContractMonthForDate", "Path": "ccsce/CrewStaticDataService/getContractMonthForGivenDate"}, {"Name": "GetBaseAirportList", "Path": "ccsce/CrewStaticDataService/getBaseAirportList"}]}]}, "ConnectionStrings": {"ccsApigeeConnectionString": "url=https://aa-ch-test-atm-ccs.trafficmanager.net/; cauth={Put Authentication Details}", "ccsToken": "url=https://api.test.aa.com/edgemicro-auth/token; bauth={Put Authentication Details};content=grant_type:client_credentials"}, "Host": {"LocalHttpPort": 7071, "CORS": "*", "CORSCredentials": false}}