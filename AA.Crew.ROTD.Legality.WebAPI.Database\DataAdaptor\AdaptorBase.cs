﻿using AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor.Interfaces;
using Microsoft.Extensions.Configuration;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Database.DataAdaptor
{
    public abstract class AdaptorBase : ITransactionalAdaptor, IDisposable
    {
        #region Members

        protected bool Disposed = false;


        /// <summary>
        /// 
        /// </summary>
        protected SqlCommand SqlCommand = null;

        /// <summary>
        /// 
        /// </summary>
        protected SqlConnection SqlConn = null;
        protected string connectionString = null;
        #endregion Members

        #region Properties

        /// <summary>
        /// Gets the connection string for the this connection.
        /// </summary>
        public string ConnectionString
        {
            get
            {
                return connectionString;
            }
        }

        /// <summary>
        /// Gets a boolean value indicating if the System is connected to the Database and ready for requests.
        /// </summary>
        public bool Connected
        {
            get
            {
                return this.SqlConn != null
                    && this.SqlConn.State == ConnectionState.Open;
            }
        }


        /// <summary>
        /// The Sql Command has a pending transaction
        /// </summary>
        public bool HasTransaction
        {
            get
            {
                bool result = false;
                if (this.SqlCommand != null
                    && this.SqlCommand.Transaction != null)
                {
                    result = true;
                }
                return result;
            }
        }



        #endregion Properties

        #region Constructors

        /// <summary>
        /// Initalizes the Providers.SqlConnection to the current VMS Connections String.
        /// </ProviderBase>
        public AdaptorBase(string connectionString)
        {
            this.connectionString = connectionString;
            this.SqlConn = new SqlConnection(this.connectionString);
            this.SqlCommand = new SqlCommand();
            this.SqlCommand.CommandTimeout = 240;
            this.SqlCommand.Connection = this.SqlConn;
            this.SqlCommand.CommandType = CommandType.StoredProcedure;
        }

        #endregion Constructors

        #region IDataAdaptor Methods


        /// <summary>
        /// Opens a connection to the Database        
        /// </summary>
        /// <exception cref="Exceptions.DataConnectionException">Connection failure exception</exception>
        public async Task Connect()
        {
            try
            {
                if (this.SqlConn.State == ConnectionState.Broken)
                {
                    this.SqlConn.Close();
                }

                if (this.SqlConn.State == ConnectionState.Closed)
                {
                    await this.SqlConn.OpenAsync();
                }
            }
            catch (Exception ex)
            {
                throw new Exceptions.DataConnectionException("Failure opening database connection", ex);
            }
        }


        /// <summary>
        /// Closes a connection to the Database        
        /// </summary>
        /// <exception cref="Exceptions.DataConnectionException">Connection failure exception</exception>
        public void Close()
        {
            try
            {

                if (this.SqlConn != null)
                {
                    if (this.SqlConn.State != ConnectionState.Closed)
                    {
                        this.SqlConn.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exceptions.DataConnectionException("Failure closing database connection", ex);
            }
        }

        #endregion IDataAdaptor Methods

        #region ITransactionalAdaptor Methods


        /// <summary>
        /// Begins a ADO.Net transaction 
        /// </summary>
        public void BeginTransaction()
        {
            if (this.SqlCommand != null
                && this.SqlCommand.Transaction == null)
            {
                this.SqlCommand.Transaction = this.SqlConn.BeginTransaction();
            }
        }

        /// <summary>
        /// Commits a ADO.Net transaction 
        /// </summary>
        public void CommitTransaction()
        {
            if (this.HasTransaction)
            {
                try
                {
                    this.SqlCommand.Transaction.Commit();
                }
                catch (Exception ex)
                {

                    this.SqlCommand.Transaction.Rollback();
                    throw new Exceptions.DataTransactionException("Failure commiting database transaction", ex);
                }
                finally
                {
                    //this.SqlCommand.Transaction.Dispose();
                    //this.SqlCommand.Transaction = null;
                }
            }
        }

        /// <summary>
        /// Rollsback a ADO.Net transaction 
        /// </summary>
        public void RollBackTransaction()
        {
            if (this.HasTransaction)
            {
                try
                {
                    this.SqlCommand.Transaction.Rollback();
                }
                catch (Exception ex)
                {
                    throw new Exceptions.DataTransactionException("Failure rolling back database transaction", ex);
                }
                finally
                {
                    //this.SqlCommand.Transaction.Dispose();
                    //this.SqlCommand.Transaction = null;
                }
            }
        }


        #endregion ITransactionalAdaptor Methods

        #region IDisposable Members

        /// <summary>
        /// Disposes of any SQLTransaction, then Closes and Disposes the SqlConnection.
        /// </summary>
        public virtual void Dispose()
        {
            ApplicationException disposeExcp = null;

            if (!this.Disposed)
            {
                try
                {
                    if (this.HasTransaction)
                    {
                        this.RollBackTransaction();
                    }
                }
                catch (Exceptions.DataTransactionException ex)
                {
                    disposeExcp = ex;
                }

                if (this.SqlConn != null)
                {
                    try
                    {
                        this.Close();
                    }
                    catch (Exceptions.DataConnectionException ex)
                    {
                        disposeExcp = ex;
                    }
                    finally
                    {
                        this.SqlConn.Dispose();
                        this.SqlConn = null;
                    }
                }

                this.Disposed = true;
            }

            if (disposeExcp != null)
            {
                throw disposeExcp;
            }
        }

        #endregion
    }
}
