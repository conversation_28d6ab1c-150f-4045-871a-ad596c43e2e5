using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AA.Crew.ROTD.Legality.WebAPI.Model.Entities
{
    public partial class getLegalityQLASupportingData_Result
    {
        public long RunId { get; set; }
        public long EmployeeId { get; set; }
        public string AffectedType { get; set; }
        public string ContractMonth { get; set; }
        public int ActivityId { get; set; }
        public string ActivityCode { get; set; }
        public string ActivityType { get; set; }
        public string ActivityOriginationDate { get; set; }
        public string PositionCode { get; set; }
        public string PickupContractMonth { get; set; }
        public int PickupActivityId { get; set; }
        public string PickupActivityCode { get; set; }
        public string PickupActivityType { get; set; }
        public string PickupActivityOriginationDate { get; set; }
        public string PickupPositionCode { get; set; }
        public string StartDateTime { get; set; }
        public string EndDateTime { get; set; }
        public string PickupStartDateTime { get; set; }
        public string PickupEndDateTime { get; set; }
        public Nullable<System.DateTime> ActivityReportDateTime { get; set; }
        public Nullable<long> LegalityPhaseId { get; set; }
    }
}
